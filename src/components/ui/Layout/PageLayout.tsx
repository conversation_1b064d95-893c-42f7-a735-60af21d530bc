import React from 'react';
import { Link } from 'react-router-dom';

interface PageLayoutProps {
  /** Page content */
  children: React.ReactNode;
  /** Page title */
  title?: string;
  /** Show back button */
  showBackButton?: boolean;
  /** Back button URL */
  backUrl?: string;
  /** Additional CSS classes */
  className?: string;
  /** Header actions (e.g., buttons, user menu) */
  headerActions?: React.ReactNode;
}

/**
 * Page layout component providing consistent structure across the application
 * Includes optional header with title, back button, and actions
 * 
 * @example
 * ```tsx
 * <PageLayout title="Opprett nytt prosjekt" showBackButton backUrl="/">
 *   <CreateProjectForm />
 * </PageLayout>
 * 
 * <PageLayout 
 *   title="Dashboard" 
 *   headerActions={<UserButton />}
 * >
 *   <ProjectGrid />
 * </PageLayout>
 * ```
 */
export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  backUrl = '/',
  className = '',
  headerActions,
}) => {
  return (
    <div className={`min-h-screen bg-white animate-fade-in ${className}`.trim()}>
      <div className="container-content container-section">
        {/* Header Section */}
        {(title || showBackButton || headerActions) && (
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-4">
              {/* Back Button */}
              {showBackButton && (
                <Link
                  to={backUrl}
                  className="btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary-soft"
                  aria-label="Tilbake"
                >
                  <svg 
                    className="w-5 h-5" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M15 19l-7-7 7-7" 
                    />
                  </svg>
                </Link>
              )}

              {/* Page Title */}
              {title && (
                <div className="animate-slide-up">
                  <h1 className="text-heading-1 text-jobblogg-text-strong">
                    {title}
                  </h1>
                </div>
              )}
            </div>

            {/* Header Actions */}
            {headerActions && (
              <div className="flex items-center gap-4">
                {headerActions}
              </div>
            )}
          </div>
        )}

        {/* Main Content */}
        <main className="animate-slide-up" style={{ animationDelay: '0.1s' }}>
          {children}
        </main>
      </div>
    </div>
  );
};

export default PageLayout;
