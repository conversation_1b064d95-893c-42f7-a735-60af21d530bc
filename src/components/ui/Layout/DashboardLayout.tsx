import React from 'react';
import { Link } from 'react-router-dom';

interface DashboardLayoutProps {
  /** Page content */
  children: React.ReactNode;
  /** Page title */
  title?: string;
  /** Subtitle or description */
  subtitle?: string;
  /** Header actions (e.g., buttons, user menu) */
  headerActions?: React.ReactNode;
  /** Stats section content */
  statsSection?: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Disable default animations */
  noAnimation?: boolean;
}

/**
 * Dashboard layout component optimized for overview pages with stats and grids
 * Provides consistent structure for dashboard-style pages
 * 
 * @example
 * ```tsx
 * <DashboardLayout 
 *   title="Dine prosjekter"
 *   subtitle="Velkommen tilbake, bruker! 👋"
 *   headerActions={<UserButton />}
 *   statsSection={<ProjectStats />}
 * >
 *   <ProjectGrid />
 * </DashboardLayout>
 * ```
 */
export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  subtitle,
  headerActions,
  statsSection,
  className = '',
  noAnimation = false,
}) => {
  const animationClass = noAnimation ? '' : 'animate-fade-in';

  return (
    <div className={`min-h-screen bg-white ${animationClass} ${className}`.trim()}>
      <div className="container-full section-spacing">
        {/* Dashboard Header */}
        {(title || subtitle || headerActions) && (
          <header className="page-header">
            <div className="flex-between gap-6 mb-8">
              <div className="flex items-center gap-6">
                {/* Title Section */}
                {(title || subtitle) && (
                  <div className={noAnimation ? '' : 'animate-slide-up'}>
                    {title && (
                      <h1 className="text-heading-1 gradient-header bg-clip-text text-transparent mb-2">
                        {title}
                      </h1>
                    )}
                    {subtitle && (
                      <p className="text-lg text-jobblogg-text-medium">
                        {subtitle}
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* Header Actions */}
              {headerActions && (
                <div className="flex items-center gap-4">
                  {headerActions}
                </div>
              )}
            </div>
          </header>
        )}

        {/* Stats Section */}
        {statsSection && (
          <section className="space-section">
            {statsSection}
          </section>
        )}

        {/* Main Content */}
        <main 
          className={`page-content ${noAnimation ? '' : 'animate-slide-up'}`}
          style={noAnimation ? {} : { animationDelay: '0.2s' }}
        >
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
