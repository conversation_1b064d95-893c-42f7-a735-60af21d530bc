import React, { forwardRef } from 'react';

/**
 * Props for the TextInput component
 */
export interface TextInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** Input label text */
  label?: string;
  /** Error message to display */
  error?: string;
  /** Helper text to display below the input */
  helperText?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Size variant of the input */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the input in full width */
  fullWidth?: boolean;
  /** Icon to display at the start of the input */
  startIcon?: React.ReactNode;
  /** Icon to display at the end of the input */
  endIcon?: React.ReactNode;
}

/**
 * TextInput component with WCAG AA accessibility compliance
 * 
 * Features:
 * - WCAG AA compliant with proper contrast ratios
 * - Keyboard navigation support
 * - Screen reader friendly with ARIA attributes
 * - Error state handling with proper announcements
 * - Focus management and visual indicators
 * - Consistent styling with JobbLogg design system
 * 
 * @example
 * ```tsx
 * <TextInput
 *   label="Prosjektnavn"
 *   placeholder="Skriv inn prosjektnavnet"
 *   required
 *   error={errors.name}
 *   helperText="Velg et beskrivende navn for prosjektet"
 * />
 * ```
 */
export const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      label,
      error,
      helperText,
      required = false,
      disabled = false,
      size = 'medium',
      fullWidth = false,
      startIcon,
      endIcon,
      className = '',
      id,
      'aria-describedby': ariaDescribedBy,
      ...props
    },
    ref
  ) => {
    // Generate unique IDs for accessibility
    const inputId = id || `text-input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${inputId}-error` : undefined;
    const helperTextId = helperText ? `${inputId}-helper` : undefined;
    
    // Build aria-describedby attribute
    const describedBy = [
      ariaDescribedBy,
      errorId,
      helperTextId
    ].filter(Boolean).join(' ') || undefined;

    // Size classes
    const sizeClasses = {
      small: 'h-8 text-sm px-3',
      medium: 'h-10 text-base px-4',
      large: 'h-12 text-lg px-4'
    };

    // Base input classes with WCAG AA compliance
    const baseClasses = `
      w-full rounded-lg border transition-all duration-200
      bg-white text-jobblogg-text-strong
      placeholder:text-jobblogg-text-muted
      focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary
      disabled:bg-jobblogg-neutral disabled:text-jobblogg-text-muted disabled:cursor-not-allowed
      ${error 
        ? 'border-jobblogg-error text-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error' 
        : 'border-jobblogg-neutral-secondary hover:border-jobblogg-primary'
      }
      ${sizeClasses[size]}
      ${fullWidth ? 'w-full' : ''}
      ${startIcon ? 'pl-10' : ''}
      ${endIcon ? 'pr-10' : ''}
    `.trim().replace(/\s+/g, ' ');

    return (
      <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className={`
              block text-sm font-medium mb-2
              ${error ? 'text-jobblogg-error' : 'text-jobblogg-text-strong'}
              ${disabled ? 'text-jobblogg-text-muted' : ''}
            `}
          >
            {label}
            {required && (
              <span className="text-jobblogg-error ml-1" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Start Icon */}
          {startIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none">
              {startIcon}
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            id={inputId}
            className={baseClasses}
            disabled={disabled}
            required={required}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={describedBy}
            {...props}
          />

          {/* End Icon */}
          {endIcon && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none">
              {endIcon}
            </div>
          )}
        </div>

        {/* Helper Text */}
        {helperText && !error && (
          <p 
            id={helperTextId}
            className="mt-1 text-sm text-jobblogg-text-muted"
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p 
            id={errorId}
            className="mt-1 text-sm text-jobblogg-error flex items-center gap-1"
            role="alert"
            aria-live="polite"
          >
            <svg 
              className="w-4 h-4 flex-shrink-0" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
            {error}
          </p>
        )}
      </div>
    );
  }
);

TextInput.displayName = 'TextInput';
