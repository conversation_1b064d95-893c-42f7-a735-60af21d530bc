import React from 'react';

interface ProjectCardProps {
  /** Project title */
  title: string;
  /** Project description */
  description: string;
  /** Project ID for navigation */
  projectId: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Click handler for card interaction */
  onClick: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Animation delay for staggered animations */
  animationDelay?: string;
}

/**
 * Project card component following JobbLogg design system
 * Displays project information with gradient background and hover effects
 * 
 * @example
 * ```tsx
 * <ProjectCard
 *   title="Kjøkkenrenovering"
 *   description="Komplett renovering av kjøkken med nye skap og benkeplate"
 *   projectId="project-123"
 *   updatedAt="2025-01-26"
 *   onClick={() => navigate(`/project/project-123`)}
 * />
 * ```
 */
export const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  projectId: _projectId,
  updatedAt,
  onClick,
  className = '',
  animationDelay = '0s',
}) => {
  const handleClick = () => {
    onClick();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick();
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('nb-NO');
    } catch {
      return dateString;
    }
  };

  return (
    <div
      className={`
        card-elevated hover-lift animate-slide-up group cursor-pointer
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      style={{ animationDelay }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Åpne prosjekt: ${title}`}
    >
      {/* Project Image/Icon Area */}
      <figure className="px-6 pt-6">
        <div className="w-full h-48 gradient-blue-soft rounded-xl flex items-center justify-center group-hover:gradient-card-hover transition-all duration-300">
          <div className="p-4 bg-white/90 backdrop-blur-sm rounded-2xl shadow-soft group-hover:shadow-medium transition-all duration-300">
            <svg
              className="w-12 h-12 text-jobblogg-primary group-hover:text-jobblogg-primary-light transition-colors duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        </div>
      </figure>

      {/* Project Content */}
      <div className="card-body p-6">
        <h2 className="card-title text-jobblogg-text-strong font-semibold text-xl group-hover:text-jobblogg-primary transition-colors duration-200">
          {title}
        </h2>
        
        <p className="text-jobblogg-text-medium text-sm leading-relaxed line-clamp-2">
          {description || 'Ingen beskrivelse tilgjengelig'}
        </p>

        {/* Project Metadata */}
        <div className="flex items-center gap-2 mt-4">
          <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary-soft text-jobblogg-primary">
            Nytt
          </div>
          <span className="text-jobblogg-text-muted text-xs">
            Opprettet {formatDate(updatedAt)}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="card-actions justify-end mt-6 gap-3">
          <button
            className="btn-outline text-sm flex items-center gap-2"
            onClick={(e) => {
              e.stopPropagation();
              // Handle details view
            }}
            aria-label={`Se detaljer for ${title}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Se detaljer
          </button>
          
          <button
            className="btn-modern text-sm bg-jobblogg-primary text-white flex items-center gap-2"
            onClick={(e) => {
              e.stopPropagation();
              onClick();
            }}
            aria-label={`Åpne prosjektlogg for ${title}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Åpne logg
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
