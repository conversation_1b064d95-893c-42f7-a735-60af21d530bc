import React from 'react';

interface PrimaryButtonProps {
  /** Button content */
  children: React.ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  icon?: React.ReactNode;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
}

/**
 * Primary button component following JobbLogg design system
 * 
 * @example
 * ```tsx
 * <PrimaryButton onClick={() => console.log('clicked')}>
 *   Opprett prosjekt
 * </PrimaryButton>
 * 
 * <PrimaryButton type="submit" loading={isLoading} icon={<PlusIcon />}>
 *   Lagre endringer
 * </PrimaryButton>
 * ```
 */
export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  loading = false,
  icon,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
}) => {
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && !loading) {
      event.preventDefault();
      if (onClick) onClick();
    }
  };

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return 'bg-jobblogg-neutral text-jobblogg-text-strong hover:bg-jobblogg-neutral-dark border border-jobblogg-border';
      case 'outline':
        return 'bg-transparent text-jobblogg-primary border border-jobblogg-primary hover:bg-jobblogg-primary hover:text-white';
      case 'ghost':
        return 'bg-transparent text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong';
      case 'danger':
        return 'bg-jobblogg-error text-white hover:bg-jobblogg-error-dark';
      default:
        return 'bg-jobblogg-primary text-white hover:bg-jobblogg-primary-light';
    }
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2 text-base';
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || loading}
      className={`
        btn-modern
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${fullWidth ? 'w-full' : ''}
        focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50
        disabled:opacity-50 disabled:cursor-not-allowed
        flex items-center justify-center gap-2
        font-medium rounded-lg transition-all duration-200
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-disabled={disabled || loading}
    >
      {loading ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Laster...</span>
        </>
      ) : (
        <>
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span>{children}</span>
        </>
      )}
    </button>
  );
};

export default PrimaryButton;
