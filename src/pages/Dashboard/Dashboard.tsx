import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { ProjectCard, TextStrong, TextMedium, TextMuted, EmptyState } from '../../components/ui';

const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="min-h-screen bg-white animate-fade-in">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Modern Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
          <div className="flex items-center gap-6">
            <div className="animate-slide-up">
              <TextStrong as="h1" className="text-heading-1 gradient-header bg-clip-text text-transparent">
                Dine prosjekter
              </TextStrong>
              <TextMedium className="mt-2 text-lg">
                Velkommen tilbake, {user?.firstName || 'Bruker'}! 👋
              </TextMedium>
            </div>
            <div className="flex items-center gap-3">
              <UserButton
                afterSignOutUrl="/"
                appearance={{
                  elements: {
                    avatarBox: "w-10 h-10 rounded-full ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200"
                  }
                }}
              />
            </div>
          </div>
          <Link
            to="/create"
            className="btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl group"
          >
            <svg className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Nytt prosjekt
          </Link>
        </div>

        {/* Stats Section - Moved to top */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 space-section">
          <div className="card-elevated animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-jobblogg-primary-soft rounded-xl">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <TextStrong className="text-2xl text-primary">{sortedProjects.length}</TextStrong>
                <TextMuted>Totale prosjekter</TextMuted>
              </div>
            </div>
          </div>

          <div className="card-elevated animate-scale-in" style={{animationDelay: '0.1s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-jobblogg-accent-soft rounded-xl">
                <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <TextStrong className="text-2xl text-success">
                  {sortedProjects.filter(p => {
                    const projectDate = new Date(p.createdAt);
                    const now = new Date();
                    return projectDate.getMonth() === now.getMonth() &&
                           projectDate.getFullYear() === now.getFullYear();
                  }).length}
                </TextStrong>
                <TextMuted>Denne måneden</TextMuted>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-hover animate-scale-in" style={{animationDelay: '0.2s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-50 rounded-xl">
                <svg className="w-6 h-6 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <TextStrong className="text-2xl text-info">
                  {sortedProjects.length > 0 ?
                    new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
                    '-'
                  }
                </TextStrong>
                <TextMuted>Siste prosjekt</TextMuted>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-hover animate-scale-in" style={{animationDelay: '0.3s'}}>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-jobblogg-warning-soft rounded-xl">
                <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <TextStrong className="text-2xl text-warning">0</TextStrong>
                <TextMuted>Totalt bilder</TextMuted>
              </div>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="mb-8">
          <TextStrong as="h2" className="text-2xl mb-6 flex items-center gap-3">
            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            Prosjektoversikt
          </TextStrong>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Project Cards using UI Component */}
            {sortedProjects.map((project, index) => (
              <ProjectCard
                key={project._id}
                title={project.name}
                description={project.description || 'Ingen beskrivelse tilgjengelig'}
                projectId={project._id}
                updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
                onClick={() => navigate(`/project/${project._id}`)}
                animationDelay={`${index * 0.1}s`}
              />
            ))}

            {/* Empty State using UI Component */}
            {sortedProjects.length === 0 && (
              <div className="col-span-full">
                <EmptyState
                  title="🚀 Kom i gang med ditt første prosjekt!"
                  description="JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                  actionLabel="Opprett ditt første prosjekt"
                  onAction={() => navigate('/create')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
