import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, BodyText, TextMuted, TextInput, TextArea, FormError, SubmitButton } from '../../components/ui';

const CreateProject: React.FC = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [formData, setFormData] = useState({
    projectName: '',
    description: ''
  });
  const navigate = useNavigate();
  const createProject = useMutation(api.projects.create);
  const { user } = useUser();

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.projectName || formData.projectName.trim().length < 2) {
      newErrors.projectName = 'Prosjektnavn må være minst 2 tegn langt';
    }

    if (formData.projectName && formData.projectName.length > 100) {
      newErrors.projectName = 'Prosjektnavn kan ikke være lengre enn 100 tegn';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate form
      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      if (!user?.id) {
        console.error('User not authenticated');
        setIsLoading(false);
        return;
      }

      await createProject({
        name: formData.projectName.trim(),
        description: formData.description.trim(),
        userId: user.id
      });

      // Reset form
      setFormData({ projectName: '', description: '' });

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/'); // Navigate back to Dashboard after success
      }, 2000);

    } catch (error) {
      console.error('Error creating project:', error);
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageLayout
      title="Opprett nytt prosjekt"
      showBackButton
      backUrl="/"
      headerActions={
        <BodyText className="text-lg">
          Fyll ut informasjonen nedenfor for å starte et nytt prosjekt ✨
        </BodyText>
      }
    >

        {/* Modern Success Alert */}
        {showSuccess && (
          <div className="bg-jobblogg-accent-soft border border-jobblogg-accent rounded-xl p-6 mb-8 animate-scale-in">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-jobblogg-accent-soft rounded-full">
                <svg
                  className="w-6 h-6 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <BodyText className="text-jobblogg-accent font-semibold">Prosjekt opprettet! 🎉</BodyText>
                <TextMuted className="text-success/80 text-sm">Du blir snart omdirigert til oversikten...</TextMuted>
              </div>
            </div>
          </div>
        )}

        {/* General Error Alert */}
        {errors.general && (
          <div className="mb-8">
            <FormError message={errors.general} />
          </div>
        )}

        {/* Modern Create Project Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8 animate-slide-up border border-gray-100">
            <div className="mb-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <Heading2 className="mb-2">Prosjektdetaljer</Heading2>
              <BodyText>Gi prosjektet ditt et navn og en beskrivelse</BodyText>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Project Name Field */}
              <TextInput
                label="Prosjektnavn"
                placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
                required
                fullWidth
                size="large"
                value={formData.projectName}
                onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                error={errors.projectName}
              />

              {/* Description Field */}
              <TextArea
                label="Beskrivelse"
                placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
                fullWidth
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                helperText="💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"
              />

              {/* Form Actions */}
              <div className="mt-12">
                <SubmitButton
                  size="large"
                  fullWidth
                  loading={isLoading}
                  loadingText="Oppretter prosjekt..."
                  disabled={isLoading}
                >
                  Opprett prosjekt
                </SubmitButton>
              </div>
            </form>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-jobblogg-neutral rounded-xl p-6 text-center">
            <div className="flex items-center justify-center gap-6">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <TextMuted>* Obligatoriske felt</TextMuted>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <TextMuted>Kan redigeres senere</TextMuted>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <TextMuted>Sikker lagring</TextMuted>
              </div>
            </div>
          </div>
        </div>
    </PageLayout>
  );
};

export default CreateProject;
