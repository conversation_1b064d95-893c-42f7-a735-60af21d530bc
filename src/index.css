@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Modern button styles - WCAG AA compliant */
  .btn-modern {
    @apply transition-all duration-200 transform hover:scale-105 focus:scale-105 focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-xl font-medium px-6 py-3 shadow-sm hover:shadow-md;
  }

  /* Button variants with enhanced contrast */
  .btn-outline {
    @apply border-2 border-jobblogg-primary text-jobblogg-primary bg-transparent hover:bg-jobblogg-primary hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50;
  }

  .btn-soft {
    @apply bg-jobblogg-primary-soft text-jobblogg-primary hover:bg-jobblogg-primary hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50;
  }

  .btn-success-soft {
    @apply bg-jobblogg-accent-soft text-jobblogg-accent hover:bg-jobblogg-accent hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-accent focus:ring-opacity-50;
  }

  .btn-warning-soft {
    @apply bg-jobblogg-warning-soft text-jobblogg-warning hover:bg-jobblogg-warning hover:text-jobblogg-text-strong transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-warning focus:ring-opacity-50;
  }

  .btn-error-soft {
    @apply bg-jobblogg-error-soft text-jobblogg-error hover:bg-jobblogg-error hover:text-white transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-error focus:ring-opacity-50;
  }

  /* Additional accessible button variants */
  .btn-primary-solid {
    @apply bg-jobblogg-primary text-white hover:bg-jobblogg-primary-light transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50 shadow-sm hover:shadow-md;
  }

  .btn-secondary-solid {
    @apply bg-jobblogg-accent text-white hover:bg-jobblogg-accent-light transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-accent focus:ring-opacity-50 shadow-sm hover:shadow-md;
  }

  .btn-ghost-enhanced {
    @apply text-jobblogg-text-medium hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral transition-all duration-200 rounded-xl font-medium px-6 py-3 focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-30;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-xl hover:-translate-y-1 rounded-xl;
  }

  /* Input focus styles */
  .input-modern {
    @apply bg-white border border-gray-300 text-jobblogg-text-strong placeholder-jobblogg-text-muted transition-all duration-200 focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-primary rounded-xl;
  }

  /* Textarea styling */
  .textarea {
    @apply bg-white border border-gray-300 text-jobblogg-text-strong placeholder-jobblogg-text-muted resize-none;
  }

  .textarea-bordered {
    @apply border-gray-300 focus:border-primary;
  }

  /* Input styling */
  .input {
    @apply bg-white border border-gray-300 text-jobblogg-text-strong placeholder-jobblogg-text-muted;
  }

  .input-bordered {
    @apply border-gray-300 focus:border-primary;
  }

  /* Alert components - Enhanced contrast for WCAG AA compliance */
  .alert-success {
    @apply bg-jobblogg-accent-soft border border-jobblogg-accent text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-warning {
    @apply bg-jobblogg-warning-soft border border-jobblogg-warning text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-error {
    @apply bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-info {
    @apply bg-jobblogg-primary-soft border border-jobblogg-primary text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  /* Alert icon containers with proper contrast */
  .alert-icon-success {
    @apply bg-jobblogg-accent text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-warning {
    @apply bg-jobblogg-warning text-jobblogg-text-strong rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-error {
    @apply bg-jobblogg-error text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-info {
    @apply bg-jobblogg-primary text-white rounded-full p-2 flex-shrink-0;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded-xl;
  }

  /* Typography hierarchy - WCAG AA compliant */
  .text-heading-1 {
    @apply text-4xl lg:text-5xl font-bold text-jobblogg-text-strong;
  }

  .text-heading-2 {
    @apply text-3xl lg:text-4xl font-bold text-jobblogg-text-strong;
  }

  .text-heading-3 {
    @apply text-2xl lg:text-3xl font-semibold text-jobblogg-text-strong;
  }

  .text-body {
    @apply text-base text-jobblogg-text-medium;
  }

  .text-body-small {
    @apply text-sm text-jobblogg-text-medium;
  }

  .text-caption {
    @apply text-xs text-jobblogg-text-muted;
  }

  /* Additional text utility classes for accessibility */
  .text-strong {
    @apply text-jobblogg-text-strong;
  }

  .text-medium {
    @apply text-jobblogg-text-medium;
  }

  .text-muted {
    @apply text-jobblogg-text-muted;
  }

  /* Card and container styles */
  .card-modern {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
  }

  .card-elevated {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-200;
  }

  .container-section {
    @apply py-12 px-4;
  }

  .container-content {
    @apply max-w-7xl mx-auto;
  }

  /* Spacing utilities */
  .space-section {
    @apply mb-12;
  }

  .space-component {
    @apply mb-8;
  }

  .space-element {
    @apply mb-4;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-secondary;
  }

  .gradient-header {
    @apply bg-gradient-to-br from-jobblogg-primary to-jobblogg-accent;
  }

  .gradient-soft {
    @apply bg-gradient-to-br from-jobblogg-primary-soft to-jobblogg-accent-soft;
  }

  /* Additional harmonious gradients */
  .gradient-blue-soft {
    @apply bg-gradient-to-br from-blue-50 to-indigo-50;
  }

  .gradient-neutral-soft {
    @apply bg-gradient-to-br from-slate-50 to-slate-100;
  }

  .gradient-card-hover {
    @apply bg-gradient-to-br from-blue-100 to-indigo-100;
  }

  /* Smooth transitions for all elements */
  * {
    @apply transition-colors duration-200;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
