@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Modern button system - Flat design with enhanced accessibility */
  .btn-modern {
    @apply inline-flex items-center justify-center gap-2 transition-all duration-200
           font-medium rounded-xl px-6 py-3 min-h-[44px]
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed
           shadow-soft hover:shadow-medium;
  }

  /* Primary button - Main CTA */
  .btn-primary-solid {
    @apply btn-modern bg-jobblogg-primary text-white
           hover:bg-jobblogg-primary-light active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
  }

  /* Secondary button - Alternative actions */
  .btn-secondary-solid {
    @apply btn-modern bg-jobblogg-accent text-white
           hover:bg-jobblogg-accent-light active:bg-jobblogg-accent-dark
           focus:ring-jobblogg-accent;
  }

  /* Outline button - Secondary actions */
  .btn-outline {
    @apply btn-modern border-2 border-jobblogg-primary text-jobblogg-primary bg-white
           hover:bg-jobblogg-primary hover:text-white active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
  }

  /* Ghost button - Subtle actions */
  .btn-ghost-enhanced {
    @apply btn-modern text-jobblogg-text-medium bg-transparent
           hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral
           focus:ring-jobblogg-primary shadow-none hover:shadow-soft;
  }

  /* Soft button variants */
  .btn-soft {
    @apply btn-modern bg-jobblogg-primary-soft text-jobblogg-primary
           hover:bg-jobblogg-primary hover:text-white
           focus:ring-jobblogg-primary;
  }

  .btn-success-soft {
    @apply btn-modern bg-jobblogg-accent-soft text-jobblogg-accent
           hover:bg-jobblogg-accent hover:text-white
           focus:ring-jobblogg-accent;
  }

  .btn-warning-soft {
    @apply btn-modern bg-jobblogg-warning-soft text-jobblogg-warning
           hover:bg-jobblogg-warning hover:text-jobblogg-text-strong
           focus:ring-jobblogg-warning;
  }

  .btn-error-soft {
    @apply btn-modern bg-jobblogg-error-soft text-jobblogg-error
           hover:bg-jobblogg-error hover:text-white
           focus:ring-jobblogg-error;
  }

  /* Modern card system - Flat design with subtle elevation */
  .card-modern {
    @apply bg-white rounded-xl border border-jobblogg-border p-6
           transition-all duration-200 shadow-soft;
  }

  .card-elevated {
    @apply card-modern shadow-medium hover:shadow-large
           hover:-translate-y-0.5 cursor-pointer;
  }

  .card-hover {
    @apply card-elevated;
  }

  /* Modern input system - Enhanced accessibility and flat design */
  .input-modern {
    @apply w-full bg-white border border-jobblogg-border text-jobblogg-text-strong
           placeholder:text-jobblogg-text-muted rounded-xl px-4 py-3 min-h-[44px]
           transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary
           hover:border-jobblogg-primary
           disabled:bg-jobblogg-neutral disabled:text-jobblogg-text-muted disabled:cursor-not-allowed;
  }

  /* Textarea styling - Consistent with input design */
  .textarea-modern {
    @apply input-modern resize-y min-h-[120px] py-3;
  }



  .input-bordered {
    @apply input-modern;
  }

  /* Alert components - Enhanced contrast for WCAG AA compliance */
  .alert-success {
    @apply bg-jobblogg-accent-soft border border-jobblogg-accent text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-warning {
    @apply bg-jobblogg-warning-soft border border-jobblogg-warning text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-error {
    @apply bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-info {
    @apply bg-jobblogg-primary-soft border border-jobblogg-primary text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  /* Alert icon containers with proper contrast */
  .alert-icon-success {
    @apply bg-jobblogg-accent text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-warning {
    @apply bg-jobblogg-warning text-jobblogg-text-strong rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-error {
    @apply bg-jobblogg-error text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-info {
    @apply bg-jobblogg-primary text-white rounded-full p-2 flex-shrink-0;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded-xl;
  }

  /* Modern typography hierarchy - 2025 design system */
  .text-heading-1 {
    @apply text-4xl sm:text-5xl text-jobblogg-text-strong font-bold leading-tight mb-4;
  }

  .text-heading-2 {
    @apply text-3xl sm:text-4xl text-jobblogg-text-strong font-semibold leading-tight mb-3;
  }

  .text-heading-3 {
    @apply text-2xl sm:text-3xl text-jobblogg-text-strong font-semibold leading-snug mb-2;
  }

  .text-body {
    @apply text-base text-jobblogg-text-medium leading-relaxed;
  }

  .text-small {
    @apply text-sm text-jobblogg-text-medium leading-normal;
  }

  .text-caption {
    @apply text-xs text-jobblogg-text-muted leading-normal;
  }

  /* Text color utilities - WCAG AA compliant */
  .text-strong {
    @apply text-jobblogg-text-strong;
  }

  .text-medium {
    @apply text-jobblogg-text-medium;
  }

  .text-muted {
    @apply text-jobblogg-text-muted;
  }



  /* Modern layout system - Mobile-first responsive design */
  .container-section {
    @apply py-8 px-4 sm:py-12 sm:px-6 lg:px-8;
  }

  .container-content {
    @apply max-w-7xl mx-auto;
  }

  /* Enhanced container system for different content types */
  .container-narrow {
    @apply max-w-2xl mx-auto px-4 sm:px-6;
  }

  .container-medium {
    @apply max-w-4xl mx-auto px-4 sm:px-6;
  }

  .container-wide {
    @apply max-w-6xl mx-auto px-4 sm:px-6;
  }

  .container-full {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Enhanced spacing system for modern design */
  .space-section {
    @apply mb-12 sm:mb-16 lg:mb-20;
  }

  .space-component {
    @apply mb-6 sm:mb-8;
  }

  .space-element {
    @apply mb-3 sm:mb-4;
  }

  /* Page layout utilities */
  .page-header {
    @apply mb-8 sm:mb-12;
  }

  .page-content {
    @apply space-y-6 sm:space-y-8;
  }

  /* Modern grid system - Enhanced responsive grids */
  .grid-auto-fit {
    @apply grid gap-6 grid-cols-[repeat(auto-fit,minmax(280px,1fr))];
  }

  .grid-auto-fill {
    @apply grid gap-6 grid-cols-[repeat(auto-fill,minmax(280px,1fr))];
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  .grid-stats {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
  }

  /* Enhanced flex utilities for modern layouts */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Modern section spacing */
  .section-spacing {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .section-spacing-sm {
    @apply py-8 sm:py-12;
  }

  .section-spacing-lg {
    @apply py-16 sm:py-20 lg:py-24;
  }

  /* Enhanced interaction utilities */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-[1.02] hover:shadow-lg;
  }

  .hover-lift-sm {
    @apply transition-transform duration-200 hover:scale-[1.01] hover:shadow-md;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  .focus-ring-accent {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-accent focus:ring-offset-2;
  }

  /* Modern card variants */
  .card-stats {
    @apply bg-white rounded-xl p-6 shadow-sm border border-jobblogg-border hover:shadow-md transition-all duration-200;
  }

  .card-interactive {
    @apply card-elevated cursor-pointer hover-lift focus-ring;
  }

  /* Layout utilities for modern design */
  .layout-stack {
    @apply space-y-6 sm:space-y-8;
  }

  .layout-stack-sm {
    @apply space-y-3 sm:space-y-4;
  }

  .layout-stack-lg {
    @apply space-y-8 sm:space-y-12;
  }

  /* Modern gradient system - Subtle and harmonious */
  .gradient-header {
    @apply bg-gradient-to-br from-jobblogg-primary to-jobblogg-accent;
  }

  .gradient-soft {
    @apply bg-gradient-to-br from-jobblogg-primary-soft to-jobblogg-accent-soft;
  }

  .gradient-blue-soft {
    @apply bg-gradient-to-br from-jobblogg-blue-50 to-jobblogg-indigo-50;
  }

  .gradient-card-hover {
    @apply bg-gradient-to-br from-jobblogg-blue-100 to-jobblogg-indigo-100;
  }

  .gradient-neutral-soft {
    @apply bg-gradient-to-br from-jobblogg-neutral to-jobblogg-neutral-light;
  }

  /* Micro-interactions - Smooth and purposeful */
  .hover-lift {
    @apply transition-all duration-200 hover:-translate-y-0.5 hover:shadow-medium;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  /* ===== ENHANCED FORM UTILITIES ===== */
  .form-field-group {
    @apply space-y-2;
  }

  .form-field-row {
    @apply flex flex-col sm:flex-row sm:items-center gap-4;
  }

  .form-field-inline {
    @apply flex items-center gap-3;
  }

  .form-section {
    @apply space-y-6 p-6 bg-white rounded-xl border border-jobblogg-border;
  }

  .form-section-header {
    @apply pb-4 border-b border-jobblogg-border mb-6;
  }

  .form-actions {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border;
  }

  .form-actions-right {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border sm:justify-end;
  }

  .form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .form-grid-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* File upload specific utilities */
  .file-drop-zone {
    @apply border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
           border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral;
  }

  .file-drop-zone-active {
    @apply border-jobblogg-primary bg-jobblogg-primary-soft scale-105;
  }

  .file-drop-zone-error {
    @apply border-jobblogg-error hover:border-jobblogg-error hover:bg-jobblogg-error-soft;
  }

  .file-preview-grid {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .file-preview-single {
    @apply grid gap-4 grid-cols-1;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
