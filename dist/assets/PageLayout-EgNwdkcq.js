import{j as e,L as u}from"./index-B9NzjkAC.js";const h=({children:t,title:a,showBackButton:n=!1,backUrl:l="/",className:c="",headerActions:i,containerWidth:o="full",sectionSpacing:m=!1,headerContent:r,noAnimation:s=!1})=>{const d=()=>{switch(o){case"narrow":return"container-narrow";case"medium":return"container-medium";case"wide":return"container-wide";default:return"container-full"}},x=s?"":"animate-fade-in",g=m?"section-spacing":"container-section";return e.jsx("div",{className:`min-h-screen bg-white ${x} ${c}`.trim(),children:e.jsxs("div",{className:`${d()} ${g}`,children:[(a||n||i||r)&&e.jsxs("header",{className:"page-header",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-8",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[n&&e.jsx(u,{to:l,className:"inline-flex items-center justify-center w-10 h-10 rounded-xl bg-jobblogg-neutral hover:bg-jobblogg-primary-soft text-jobblogg-text-medium hover:text-jobblogg-primary transition-all duration-200 focus-ring hover-lift","aria-label":"Tilbake",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),a&&e.jsx("div",{className:s?"":"animate-slide-up",children:e.jsx("h1",{className:"text-heading-1 text-jobblogg-text-strong",children:a})})]}),i&&e.jsx("div",{className:"flex items-center gap-4",children:i})]}),r&&e.jsx("div",{className:"space-component",children:r})]}),e.jsx("main",{className:`page-content ${s?"":"animate-slide-up"}`,style:s?{}:{animationDelay:"0.1s"},children:t})]})})};export{h as P};
