import{j as e,f as b,u as v,a as u,b as h,c as j,L as p,P as d}from"./index-CKtwqk6x.js";import{H as m}from"./Heading2-D3ePk88U.js";import{B as g}from"./BodyText-DuggM1es.js";import{P as k,T as o}from"./PageLayout-B8oEbEaj.js";import{E as N}from"./EmptyState-C2i22Zrb.js";const c=({children:s,className:n="",as:t="h3"})=>e.jsx(t,{className:`
        text-heading-3 text-jobblogg-text-strong font-semibold leading-snug
        ${n}
      `.trim().replace(/\s+/g," "),children:s}),B=()=>{const{projectId:s}=b(),{user:n}=v(),t=u(),a=h(j.projects.getById,s?{projectId:s}:"skip"),x=h(j.logEntries.getByProject,s&&n?.id?{projectId:s,userId:n.id}:"skip");if(a===void 0||x===void 0)return e.jsx("div",{className:"min-h-screen bg-white",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-8",children:[e.jsx("div",{className:"skeleton h-10 w-10 rounded-full"}),e.jsx("div",{className:"skeleton h-8 w-64"}),e.jsx("div",{className:"ml-auto skeleton h-10 w-32 rounded-lg"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-base-100 rounded-xl p-8 shadow-lg",children:[e.jsx("div",{className:"skeleton h-8 w-48 mb-4"}),e.jsx("div",{className:"skeleton h-20 w-full mb-6"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"skeleton h-12 w-32 rounded-lg"}),e.jsx("div",{className:"skeleton h-12 w-32 rounded-lg"})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"bg-base-100 rounded-xl p-6 shadow-lg",children:[e.jsx("div",{className:"skeleton h-6 w-24 mb-4"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"skeleton h-4 w-full"}),e.jsx("div",{className:"skeleton h-4 w-3/4"}),e.jsx("div",{className:"skeleton h-4 w-1/2"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border",children:[e.jsx("div",{className:"skeleton h-8 w-32 mb-6"}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((l,i)=>e.jsx("div",{className:"skeleton h-48 w-full rounded-xl"},i))})]})]})});if(!a||a.userId!==n?.id)return e.jsx("div",{className:"min-h-screen bg-base-200/30 animate-fade-in",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsx("div",{className:"flex items-center gap-4 mb-8",children:e.jsx(p,{to:"/",className:"btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary/10","aria-label":"Tilbake til oversikt",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})}),e.jsxs("div",{className:"bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-xl p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-error/20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-jobblogg-error",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsx(m,{className:"text-jobblogg-error mb-2",children:"Prosjekt ikke funnet"}),e.jsx(g,{className:"text-jobblogg-error/80 mb-6",children:"Dette prosjektet eksisterer ikke eller du har ikke tilgang til det."}),e.jsx(d,{onClick:()=>t("/"),children:"Tilbake til oversikt"})]})]})});const r=x.sort((l,i)=>i.createdAt-l.createdAt);return e.jsxs(k,{title:a.name,showBackButton:!0,backUrl:"/",headerActions:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(o,{className:"text-lg",children:"📊 Prosjektdetaljer og bildesamling"}),e.jsxs(d,{onClick:()=>t(`/project/${s}`),children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Legg til bilde"]})]}),children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-jobblogg-primary/10 to-jobblogg-accent/10 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx(m,{children:"Prosjektinformasjon"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs(c,{className:"mb-3 flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h7"})}),"Beskrivelse"]}),e.jsx("div",{className:"bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border",children:e.jsx(g,{className:"leading-relaxed",children:a.description||e.jsx(o,{className:"italic",children:"Ingen beskrivelse tilgjengelig. Du kan legge til en beskrivelse ved å redigere prosjektet."})})})]}),e.jsxs("div",{children:[e.jsxs(c,{className:"mb-3 flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Opprettet"]}),e.jsx("div",{className:"bg-jobblogg-neutral rounded-lg p-4",children:e.jsx(g,{children:new Date(a.createdAt).toLocaleDateString("nb-NO",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-3 mt-8 pt-6 border-t border-base-200",children:[e.jsxs(d,{onClick:()=>t(`/project/${s}`),className:"flex-1 sm:flex-none",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Legg til bilde"]}),e.jsxs("button",{className:"btn btn-outline btn-modern flex-1 sm:flex-none",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Rediger prosjekt"]})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 animate-slide-up border border-jobblogg-border",style:{animationDelay:"100ms"},children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-jobblogg-accent/10 to-jobblogg-accent/10 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-jobblogg-accent",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),e.jsx(c,{children:"Statistikk"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-jobblogg-primary-soft rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-jobblogg-primary mb-1",children:r.length}),e.jsx(o,{className:"text-sm",children:"Totalt bilder"})]}),e.jsxs("div",{className:"bg-jobblogg-accent-soft rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-jobblogg-accent mb-1",children:r.length>0?new Date(r[0].createdAt).toLocaleDateString("nb-NO",{day:"numeric",month:"short"}):"Ingen aktivitet"}),e.jsx(o,{className:"text-sm",children:"Siste aktivitet"})]}),e.jsxs("div",{className:"bg-accent/5 rounded-lg p-4 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-accent mb-1",children:Math.ceil((Date.now()-a.createdAt)/(1e3*60*60*24))}),e.jsx("div",{className:"text-sm text-base-content/60",children:"Dager siden oppstart"})]})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-jobblogg-border p-8 animate-slide-up",style:{animationDelay:"200ms"},children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-jobblogg-primary/10 to-jobblogg-primary/10 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx(m,{children:"📸 Bildesamling"}),e.jsx(o,{className:"text-sm",children:"Kronologisk oversikt over prosjektets fremgang"})]})]}),r.length>0&&e.jsxs("div",{className:"bg-jobblogg-primary-soft text-jobblogg-primary px-3 py-1 rounded-full text-sm font-medium",children:[r.length," bilde",r.length!==1?"r":""]})]}),r.length===0?e.jsx("div",{className:"py-16",children:e.jsx(N,{title:"Ingen bilder lagt til ennå",description:"Start dokumentering av prosjektet ved å legge til ditt første bilde. Bilder hjelper deg å følge fremgangen og dele resultater! 📷",actionLabel:"Legg til første bilde",onAction:()=>t(`/project/${s}`)})}):e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map((l,i)=>e.jsxs("div",{className:"bg-jobblogg-neutral rounded-xl overflow-hidden card-hover animate-scale-in group border border-jobblogg-border",style:{animationDelay:`${i*100}ms`},children:[e.jsxs("div",{className:"aspect-video relative overflow-hidden",children:[l.imageUrl?e.jsx("img",{src:l.imageUrl,alt:l.description||"Prosjektbilde",className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"}):e.jsx("div",{className:"w-full h-full bg-base-300/50 flex items-center justify-center",children:e.jsx("svg",{className:"w-12 h-12 text-base-content/30",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{className:"absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-full text-xs",children:["#",r.length-i]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("p",{className:"text-base-content leading-relaxed text-sm mb-3",children:l.description||e.jsx("span",{className:"text-base-content/50 italic",children:"Ingen beskrivelse"})}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-base-content/60",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.jsx("span",{children:new Date(l.createdAt).toLocaleDateString("nb-NO",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"})})]}),l.imageUrl&&e.jsxs("div",{className:"flex items-center gap-1 text-jobblogg-accent",children:[e.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.jsx("span",{children:"Med bilde"})]})]})]})]},l._id))})]}),r.length>0&&e.jsx("div",{className:"mt-12 text-center",children:e.jsxs("div",{className:"bg-base-200/30 rounded-xl p-8",children:[e.jsx(c,{className:"mb-3",children:"Fortsett dokumenteringen! 🚀"}),e.jsx(o,{className:"mb-6",children:"Legg til flere bilder for å følge prosjektets fremgang over tid"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsxs(d,{onClick:()=>t(`/project/${s}`),className:"shadow-lg hover:shadow-xl btn-lg",children:[e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Legg til nytt bilde"]}),e.jsxs("button",{onClick:()=>t("/"),className:"btn btn-outline btn-lg btn-modern",children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"})]}),"Tilbake til oversikt"]})]})]})})]})};export{B as default};
