import{r as o,a as b,d as k,c as f,u as N,j as e,T as n,e as w,f as y}from"./index-Dj9-ovU_.js";import{H as L}from"./Heading2-C3PiuDad.js";import{B as d}from"./BodyText-B5ifKtmm.js";import{P as B}from"./PageLayout-DZth2Qsp.js";import{F as S,S as M}from"./SubmitButton-D4tB_e7D.js";const E=()=>{const[g,m]=o.useState(!1),[j,a]=o.useState(!1),[i,l]=o.useState({}),[t,c]=o.useState({projectName:"",description:""}),h=b(),p=k(f.projects.create),{user:x}=N(),u=()=>{const r={};return(!t.projectName||t.projectName.trim().length<2)&&(r.projectName="Prosjektnavn må være minst 2 tegn langt"),t.projectName&&t.projectName.length>100&&(r.projectName="Prosjektnavn kan ikke være lengre enn 100 tegn"),l(r),Object.keys(r).length===0},v=async r=>{r.preventDefault(),a(!0),l({});try{if(!u()){a(!1);return}if(!x?.id){console.error("User not authenticated"),a(!1);return}await p({name:t.projectName.trim(),description:t.description.trim(),userId:x.id}),c({projectName:"",description:""}),m(!0),setTimeout(()=>{m(!1),h("/")},2e3)}catch(s){console.error("Error creating project:",s),l({general:"Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen."})}finally{a(!1)}};return e.jsxs(B,{title:"Opprett nytt prosjekt",showBackButton:!0,backUrl:"/",headerActions:e.jsx(d,{className:"text-lg",children:"Fyll ut informasjonen nedenfor for å starte et nytt prosjekt ✨"}),children:[g&&e.jsx("div",{className:"bg-jobblogg-accent-soft border border-jobblogg-accent rounded-xl p-6 mb-8 animate-scale-in",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-2 bg-jobblogg-accent-soft rounded-full",children:e.jsx("svg",{className:"w-6 h-6 text-jobblogg-accent",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx(d,{className:"text-jobblogg-accent font-semibold",children:"Prosjekt opprettet! 🎉"}),e.jsx(n,{className:"text-jobblogg-accent/80 text-sm",children:"Du blir snart omdirigert til oversikten..."})]})]})}),i.general&&e.jsx("div",{className:"mb-8",children:e.jsx(S,{message:i.general})}),e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 animate-slide-up border border-jobblogg-border",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),e.jsx(L,{className:"mb-2",children:"Prosjektdetaljer"}),e.jsx(d,{children:"Gi prosjektet ditt et navn og en beskrivelse"})]}),e.jsxs("form",{onSubmit:v,className:"space-y-8",children:[e.jsx(w,{label:"Prosjektnavn",placeholder:"F.eks. Kjøkkenrenovering, Terrasse bygging...",required:!0,fullWidth:!0,size:"large",value:t.projectName,onChange:r=>c(s=>({...s,projectName:r.target.value})),error:i.projectName}),e.jsx(y,{label:"Beskrivelse",placeholder:"Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer...",fullWidth:!0,rows:4,value:t.description,onChange:r=>c(s=>({...s,description:r.target.value})),helperText:"💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"}),e.jsx("div",{className:"mt-12",children:e.jsx(M,{size:"large",fullWidth:!0,loading:j,loadingText:"Oppretter prosjekt...",disabled:j,children:"Opprett prosjekt"})})]})]})}),e.jsx("div",{className:"mt-8 max-w-2xl mx-auto",children:e.jsx("div",{className:"bg-jobblogg-neutral rounded-xl p-6 text-center",children:e.jsxs("div",{className:"flex items-center justify-center gap-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsx(n,{children:"* Obligatoriske felt"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-jobblogg-accent",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx(n,{children:"Kan redigeres senere"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"w-4 h-4 text-info",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),e.jsx(n,{children:"Sikker lagring"})]})]})})})]})};export{E as default};
