import{j as e,u as g,a as j,b as p,c as u,U as b,L as v}from"./index-e3QZ0lXD.js";import{T as l,a as N,b as d}from"./TextMuted-C8yc3q9m.js";import{E as k}from"./EmptyState-DvTWrNXq.js";const f=({title:i,description:n,projectId:c,updatedAt:t,onClick:s,className:a="",animationDelay:o="0s"})=>{const m=()=>{s()},x=r=>{(r.key==="Enter"||r.key===" ")&&(r.preventDefault(),s())},h=r=>{try{return new Date(r).toLocaleDateString("nb-NO")}catch{return r}};return e.jsxs("div",{className:`
        card-elevated animate-slide-up group cursor-pointer
        ${a}
      `.trim().replace(/\s+/g," "),style:{animationDelay:o},onClick:m,onKeyDown:x,tabIndex:0,role:"button","aria-label":`Åpne prosjekt: ${i}`,children:[e.jsx("figure",{className:"px-6 pt-6",children:e.jsx("div",{className:"w-full h-48 bg-gradient-to-br from-jobblogg-blue-50 to-jobblogg-indigo-50 rounded-xl flex items-center justify-center group-hover:from-jobblogg-blue-100 group-hover:to-jobblogg-indigo-100 transition-all duration-300",children:e.jsx("div",{className:"p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm group-hover:shadow-md transition-all duration-300",children:e.jsx("svg",{className:"w-12 h-12 text-jobblogg-primary group-hover:text-jobblogg-primary-light transition-colors duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),e.jsxs("div",{className:"card-body p-6",children:[e.jsx("h2",{className:"card-title text-jobblogg-text-strong font-semibold text-xl group-hover:text-jobblogg-primary transition-colors duration-200",children:i}),e.jsx("p",{className:"text-jobblogg-text-medium text-sm leading-relaxed line-clamp-2",children:n||"Ingen beskrivelse tilgjengelig"}),e.jsxs("div",{className:"flex items-center gap-2 mt-4",children:[e.jsx("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary-soft text-jobblogg-primary",children:"Nytt"}),e.jsxs("span",{className:"text-jobblogg-text-muted text-xs",children:["Opprettet ",h(t)]})]}),e.jsxs("div",{className:"card-actions justify-end mt-6 gap-3",children:[e.jsxs("button",{className:"btn-outline text-sm flex items-center gap-2",onClick:r=>{r.stopPropagation()},"aria-label":`Se detaljer for ${i}`,children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Se detaljer"]}),e.jsxs("button",{className:"btn-modern text-sm bg-jobblogg-primary text-white flex items-center gap-2",onClick:r=>{r.stopPropagation(),s()},"aria-label":`Åpne prosjektlogg for ${i}`,children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),"Åpne logg"]})]})]})]})},D=()=>{const{user:i}=g(),n=j(),c=p(u.projects.getByUser,{userId:i?.id||""});if(c===void 0)return e.jsx("div",{className:"min-h-screen bg-jobblogg-neutral",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12",children:[e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsx("div",{className:"skeleton h-12 w-64"}),e.jsx("div",{className:"skeleton h-10 w-10 rounded-full"})]}),e.jsx("div",{className:"skeleton h-12 w-40 rounded-xl"})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[...Array(4)].map((s,a)=>e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100",children:[e.jsx("div",{className:"skeleton h-4 w-20 mb-3"}),e.jsx("div",{className:"skeleton h-8 w-16 mb-2"}),e.jsx("div",{className:"skeleton h-3 w-24"})]},a))}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[...Array(6)].map((s,a)=>e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 border border-gray-100",children:[e.jsx("div",{className:"skeleton h-48 w-full rounded-lg mb-4"}),e.jsx("div",{className:"skeleton h-6 w-3/4 mb-2"}),e.jsx("div",{className:"skeleton h-4 w-full mb-4"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"skeleton h-8 w-20"}),e.jsx("div",{className:"skeleton h-8 w-24"})]})]},a))})]})});const t=c.sort((s,a)=>a.createdAt-s.createdAt);return e.jsx("div",{className:"min-h-screen bg-white animate-fade-in",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12",children:[e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsxs("div",{className:"animate-slide-up",children:[e.jsx(l,{as:"h1",className:"text-heading-1 gradient-header bg-clip-text text-transparent",children:"Dine prosjekter"}),e.jsxs(N,{className:"mt-2 text-lg",children:["Velkommen tilbake, ",i?.firstName||"Bruker","! 👋"]})]}),e.jsx("div",{className:"flex items-center gap-3",children:e.jsx(b,{afterSignOutUrl:"/",appearance:{elements:{avatarBox:"w-10 h-10 rounded-full ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200"}}})})]}),e.jsxs(v,{to:"/create",className:"btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl group",children:[e.jsx("svg",{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Nytt prosjekt"]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 space-section",children:[e.jsx("div",{className:"card-elevated animate-scale-in",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-3 bg-jobblogg-primary-soft rounded-xl",children:e.jsx("svg",{className:"w-6 h-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),e.jsxs("div",{children:[e.jsx(l,{className:"text-2xl text-primary",children:t.length}),e.jsx(d,{children:"Totale prosjekter"})]})]})}),e.jsx("div",{className:"card-elevated animate-scale-in",style:{animationDelay:"0.1s"},children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-3 bg-jobblogg-accent-soft rounded-xl",children:e.jsx("svg",{className:"w-6 h-6 text-success",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx(l,{className:"text-2xl text-success",children:t.filter(s=>{const a=new Date(s.createdAt),o=new Date;return a.getMonth()===o.getMonth()&&a.getFullYear()===o.getFullYear()}).length}),e.jsx(d,{children:"Denne måneden"})]})]})}),e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-hover animate-scale-in",style:{animationDelay:"0.2s"},children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-3 bg-blue-50 rounded-xl",children:e.jsx("svg",{className:"w-6 h-6 text-info",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx(l,{className:"text-2xl text-info",children:t.length>0?new Date(t[0].createdAt).toLocaleDateString("nb-NO",{day:"numeric",month:"short"}):"-"}),e.jsx(d,{children:"Siste prosjekt"})]})]})}),e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-hover animate-scale-in",style:{animationDelay:"0.3s"},children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-3 bg-jobblogg-warning-soft rounded-xl",children:e.jsx("svg",{className:"w-6 h-6 text-warning",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsxs("div",{children:[e.jsx(l,{className:"text-2xl text-warning",children:"0"}),e.jsx(d,{children:"Totalt bilder"})]})]})})]}),e.jsxs("div",{className:"mb-8",children:[e.jsxs(l,{as:"h2",className:"text-2xl mb-6 flex items-center gap-3",children:[e.jsx("svg",{className:"w-6 h-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),"Prosjektoversikt"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[t.map((s,a)=>e.jsx(f,{title:s.name,description:s.description||"Ingen beskrivelse tilgjengelig",projectId:s._id,updatedAt:new Date(s.createdAt).toLocaleDateString("nb-NO"),onClick:()=>n(`/project/${s._id}`),animationDelay:`${a*.1}s`},s._id)),t.length===0&&e.jsx("div",{className:"col-span-full",children:e.jsx(k,{title:"🚀 Kom i gang med ditt første prosjekt!",description:"JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang.",actionLabel:"Opprett ditt første prosjekt",onAction:()=>n("/create")})})]})]})]})})};export{D as default};
