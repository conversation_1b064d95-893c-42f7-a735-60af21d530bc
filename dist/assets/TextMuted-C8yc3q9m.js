import{j as r}from"./index-e3QZ0lXD.js";const o=({children:t,as:e="p",className:s=""})=>r.jsx(e,{className:`
        text-jobblogg-text-strong font-semibold
        ${s}
      `.trim().replace(/\s+/g," "),children:t}),x=({children:t,as:e="p",className:s=""})=>r.jsx(e,{className:`
        text-jobblogg-text-medium
        ${s}
      `.trim().replace(/\s+/g," "),children:t}),m=({children:t,as:e="p",className:s=""})=>r.jsx(e,{className:`
        text-jobblogg-text-muted text-sm
        ${s}
      `.trim().replace(/\s+/g," "),children:t});export{o as T,x as a,m as b};
