import{j as e,L as l}from"./index-e3QZ0lXD.js";const o=({children:n,title:s,showBackButton:i=!1,backUrl:t="/",className:r="",headerActions:a})=>e.jsx("div",{className:`min-h-screen bg-white animate-fade-in ${r}`.trim(),children:e.jsxs("div",{className:"container-content container-section",children:[(s||i||a)&&e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[i&&e.jsx(l,{to:t,className:"btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary-soft","aria-label":"Tilbake",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),s&&e.jsx("div",{className:"animate-slide-up",children:e.jsx("h1",{className:"text-heading-1 text-jobblogg-text-strong",children:s})})]}),a&&e.jsx("div",{className:"flex items-center gap-4",children:a})]}),e.jsx("main",{className:"animate-slide-up",style:{animationDelay:"0.1s"},children:n})]})});export{o as P};
