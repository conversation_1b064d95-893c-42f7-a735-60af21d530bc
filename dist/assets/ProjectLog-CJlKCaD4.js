import{f as q,u as J,a as K,r as t,b as T,c as j,d as U,j as e,P as n,e as Q}from"./index-e3QZ0lXD.js";import{T as o,a as i,b as p}from"./TextMuted-C8yc3q9m.js";import{P as X}from"./PageLayout-DEikj3UW.js";import{F as Y,S as Z}from"./SubmitButton-Dwdmtb8V.js";const ae=()=>{const{projectId:a}=q(),{user:x}=J(),d=K(),[c,v]=t.useState(null),[h,b]=t.useState(null),[P,f]=t.useState(!1),[I,C]=t.useState(!1),[k,l]=t.useState(""),[B,N]=t.useState(!1),[m,M]=t.useState(""),[w,L]=t.useState({}),u=t.useRef(null),S=T(j.projects.getByUser,{userId:x?.id||""}),D=S?.find(s=>s._id===a),g=T(j.logEntries.getByProject,a&&x?.id?{projectId:a,userId:x.id}:"skip"),E=U(j.logEntries.generateUploadUrl),H=U(j.logEntries.create),z=s=>{if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(s.type))return alert("Kun .jpg, .jpeg, .png og .webp filer er tillatt"),!1;if(s.size>10*1024*1024)return alert("Bildet kan ikke være større enn 10MB"),!1;v(s);const y=URL.createObjectURL(s);return b(y),!0},O=s=>{const r=s.target.files?.[0];r&&z(r)},R=s=>{s.preventDefault(),N(!0)},V=s=>{s.preventDefault(),N(!1)},A=s=>{s.preventDefault(),N(!1);const r=s.dataTransfer.files;r.length>0&&z(r[0])},F=()=>{h&&URL.revokeObjectURL(h),v(null),b(null),u.current&&(u.current.value="")},$=()=>{const s={};return(!m||m.trim().length<5)&&(s.description="Beskrivelse må være minst 5 tegn lang"),m&&m.length>1e3&&(s.description="Beskrivelse kan ikke være lengre enn 1000 tegn"),L(s),Object.keys(s).length===0},G=async s=>{s.preventDefault(),f(!0),l(""),L({});try{if(!$()){f(!1);return}if(!x?.id||!a)throw new Error("User not authenticated or project ID missing");let r;if(c){l("Laster opp bilde...");const y=await E(),W=await fetch(y,{method:"POST",headers:{"Content-Type":c.type},body:c});if(!W.ok)throw new Error("Failed to upload image");const{storageId:_}=await W.json();r=_,l("Lagrer logg...")}else l("Lagrer logg...");await H({projectId:a,userId:x.id,description:m,imageId:r}),M(""),v(null),h&&URL.revokeObjectURL(h),b(null),l(""),C(!0),setTimeout(()=>{C(!1)},3e3)}catch(r){console.error("Error creating log entry:",r),L({general:"Det oppstod en feil ved lagring av loggen. Prøv igjen."}),l(""),setTimeout(()=>l(""),3e3)}finally{f(!1)}};return S===void 0?e.jsx("div",{className:"min-h-screen bg-jobblogg-neutral",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-8",children:[e.jsx("div",{className:"skeleton h-10 w-10 rounded-full"}),e.jsx("div",{className:"skeleton h-8 w-64"})]}),e.jsxs("div",{className:"bg-white rounded-xl p-8 shadow-lg border border-gray-100",children:[e.jsx("div",{className:"skeleton h-48 w-full rounded-xl mb-6"}),e.jsx("div",{className:"skeleton h-20 w-full rounded-lg mb-6"}),e.jsx("div",{className:"skeleton h-12 w-32 rounded-lg"})]})]})}):D?e.jsxs(X,{title:D.name,showBackButton:!0,backUrl:"/",headerActions:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(p,{className:"text-lg",children:"📸 Legg til nytt bilde og beskrivelse"}),e.jsxs(n,{onClick:()=>d(`/project/${a}/details`),children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Se detaljer"]})]}),children:[I&&e.jsx("div",{className:"bg-jobblogg-accent-soft border border-jobblogg-accent rounded-xl p-6 mb-8 animate-scale-in",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-jobblogg-accent-soft rounded-full flex-shrink-0",children:e.jsx("svg",{className:"w-6 h-6 text-success",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx(o,{as:"h3",className:"text-success mb-2",children:"🎉 Logg lagret!"}),e.jsx(i,{className:"text-success/80 text-sm mb-4",children:"Bildet og beskrivelsen er lagret i prosjektet ditt."}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs(n,{onClick:()=>d(`/project/${a}/details`),size:"sm",children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Se prosjektdetaljer"]}),e.jsxs(n,{onClick:()=>d("/"),variant:"outline",size:"sm",children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"})]}),"Tilbake til oversikt"]})]})]})]})}),k&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8 animate-scale-in",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"p-2 bg-blue-100 rounded-full",children:e.jsx("svg",{className:"w-6 h-6 text-info animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),e.jsxs("div",{children:[e.jsx(o,{as:"h3",className:"text-info",children:"Arbeider..."}),e.jsx(i,{className:"text-info/80 text-sm",children:k})]})]})}),e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-8 animate-slide-up",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),e.jsx(o,{as:"h2",className:"text-2xl mb-2",children:"📝 Ny loggføring"}),e.jsx(i,{children:"Dokumenter fremgangen med bilder og beskrivelser"})]}),e.jsxs("form",{onSubmit:G,className:"space-y-8",children:[w.general&&e.jsx(Y,{error:w.general}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx(i,{className:"font-semibold",children:"Bilde"}),e.jsx(p,{className:"text-sm",children:"(valgfritt)"})]}),h?e.jsxs("div",{className:"relative rounded-xl overflow-hidden bg-jobblogg-neutral animate-scale-in",children:[e.jsx("img",{src:h,alt:"Forhåndsvisning",className:"w-full h-64 object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-gray-900 bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center",children:e.jsx(n,{onClick:F,variant:"danger",size:"sm",className:"shadow-lg rounded-full w-10 h-10 p-0","aria-label":"Fjern bilde",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-gray-900 to-transparent p-4",children:[e.jsxs("p",{className:"text-white text-sm font-medium",children:["✅ ",c?.name]}),e.jsxs("p",{className:"text-gray-200 text-xs",children:[c&&(c.size/1024/1024).toFixed(1)," MB"]})]})]}):e.jsxs("div",{className:`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer ${B?"border-jobblogg-primary bg-jobblogg-primary-soft scale-105":"border-gray-300 hover:border-jobblogg-primary hover:bg-jobblogg-neutral"}`,onDragOver:R,onDragLeave:V,onDrop:A,onClick:()=>u.current?.click(),children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsx(o,{as:"h3",className:"text-lg mb-2",children:B?"📁 Slipp bildet her!":"📸 Last opp bilde"}),e.jsx(i,{className:"mb-4",children:"Dra og slipp et bilde her, eller klikk for å velge"}),e.jsxs("div",{className:"flex items-center justify-center gap-4 text-caption",children:[e.jsx("span",{children:"JPG"}),e.jsx("span",{children:"•"}),e.jsx("span",{children:"PNG"}),e.jsx("span",{children:"•"}),e.jsx("span",{children:"WebP"}),e.jsx("span",{children:"•"}),e.jsx("span",{children:"Maks 10MB"})]})]}),e.jsx("input",{ref:u,type:"file",accept:".jpg,.jpeg,.png,.webp",onChange:O,className:"hidden"})]}),e.jsx(Q,{label:"Beskrivelse *",value:m,onChange:s=>M(s.target.value),placeholder:"Beskriv hva som ble gjort i dag, utfordringer, fremgang, eller andre viktige detaljer...",rows:4,maxLength:1e3,showCharacterCount:!0,error:w.description,helperText:"💡 Tips: Detaljerte beskrivelser hjelper deg å følge fremgangen over tid",required:!0}),e.jsxs("div",{className:"mt-12",children:[e.jsxs(Z,{loading:P,loadingText:k||"Lagrer logg...",className:"shadow-lg hover:shadow-xl group mb-4",children:[e.jsx("svg",{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Lagre logg"]}),e.jsx("div",{className:"flex justify-center",children:e.jsxs(n,{onClick:()=>d(`/project/${a}/details`),variant:"ghost",children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Se alle logger i prosjektet"]})})]})]})]})}),g&&g.length>0&&e.jsx("div",{className:"mt-12 max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[e.jsx("div",{className:"w-10 h-10 bg-jobblogg-accent-soft rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx(o,{as:"h2",className:"text-2xl",children:"📋 Tidligere logger"}),e.jsxs(p,{className:"text-sm",children:[g.length," oppføringer"]})]})]}),e.jsx("div",{className:"space-y-6",children:g.map((s,r)=>e.jsx("div",{className:"bg-jobblogg-neutral rounded-xl p-6 card-hover animate-slide-up",style:{animationDelay:`${r*100}ms`},children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex justify-between items-start gap-4",children:[e.jsx(i,{className:"leading-relaxed flex-1",children:s.description}),e.jsxs("div",{className:"flex items-center gap-2 text-body-small flex-shrink-0",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.jsx("span",{children:new Date(s.createdAt).toLocaleDateString("nb-NO",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),s.imageUrl&&e.jsx("div",{className:"mt-4",children:e.jsx("img",{src:s.imageUrl,alt:"Prosjektbilde",className:"rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-shadow duration-200"})})]})},s._id))})]})}),g&&g.length===0&&e.jsx("div",{className:"mt-12 max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-jobblogg-neutral rounded-xl p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-neutral-secondary rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-muted",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx(o,{as:"h3",className:"text-lg mb-2",children:"Ingen logger ennå"}),e.jsx(p,{children:"Dette prosjektet har ingen logger. Legg til den første loggen ovenfor! 📝"})]})})]}):e.jsx("div",{className:"min-h-screen bg-jobblogg-neutral animate-fade-in",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsx("div",{className:"flex items-center gap-4 mb-8",children:e.jsx(n,{onClick:()=>d("/"),variant:"ghost",size:"sm","aria-label":"Tilbake til oversikt",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})}),e.jsxs("div",{className:"bg-jobblogg-error-soft border border-jobblogg-error rounded-xl p-8 text-center",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-error-soft rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-8 h-8 text-error",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsx(o,{as:"h2",className:"text-2xl text-error mb-2",children:"Prosjekt ikke funnet"}),e.jsx(i,{className:"text-error/80 mb-6",children:"Dette prosjektet eksisterer ikke eller du har ikke tilgang til det."}),e.jsx(n,{onClick:()=>d("/"),children:"Tilbake til oversikt"})]})]})})};export{ae as default};
