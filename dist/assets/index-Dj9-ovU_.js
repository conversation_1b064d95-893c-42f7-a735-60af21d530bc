const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-DUwE_EIa.js","assets/Heading2-C3PiuDad.js","assets/EmptyState-CzxehTk4.js","assets/CreateProject-C-to07im.js","assets/BodyText-B5ifKtmm.js","assets/PageLayout-DZth2Qsp.js","assets/SubmitButton-D4tB_e7D.js","assets/ProjectDetail-A1xRxMUl.js","assets/ProjectLog-DbJS2rWz.js"])))=>i.map(i=>d[i]);
(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))o(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function r(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(u){if(u.ep)return;u.ep=!0;const f=r(u);fetch(u.href,f)}})();function Wb(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var uc={exports:{}},Dl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cg;function Kb(){if(cg)return Dl;cg=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function r(o,u,f){var d=null;if(f!==void 0&&(d=""+f),u.key!==void 0&&(d=""+u.key),"key"in u){f={};for(var v in u)v!=="key"&&(f[v]=u[v])}else f=u;return u=f.ref,{$$typeof:a,type:o,key:d,ref:u!==void 0?u:null,props:f}}return Dl.Fragment=l,Dl.jsx=r,Dl.jsxs=r,Dl}var fg;function Zb(){return fg||(fg=1,uc.exports=Kb()),uc.exports}var x=Zb(),cc={exports:{}},ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dg;function Fb(){if(dg)return ce;dg=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),O=Symbol.iterator;function S(_){return _===null||typeof _!="object"?null:(_=O&&_[O]||_["@@iterator"],typeof _=="function"?_:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},A=Object.assign,L={};function N(_,Q,I){this.props=_,this.context=Q,this.refs=L,this.updater=I||R}N.prototype.isReactComponent={},N.prototype.setState=function(_,Q){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,Q,"setState")},N.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function C(){}C.prototype=N.prototype;function H(_,Q,I){this.props=_,this.context=Q,this.refs=L,this.updater=I||R}var $=H.prototype=new C;$.constructor=H,A($,N.prototype),$.isPureReactComponent=!0;var J=Array.isArray,Y={H:null,A:null,T:null,S:null,V:null},Z=Object.prototype.hasOwnProperty;function le(_,Q,I,G,ee,he){return I=he.ref,{$$typeof:a,type:_,key:Q,ref:I!==void 0?I:null,props:he}}function W(_,Q){return le(_.type,Q,void 0,void 0,void 0,_.props)}function oe(_){return typeof _=="object"&&_!==null&&_.$$typeof===a}function ne(_){var Q={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(I){return Q[I]})}var be=/\/+/g;function ve(_,Q){return typeof _=="object"&&_!==null&&_.key!=null?ne(""+_.key):Q.toString(36)}function Ce(){}function De(_){switch(_.status){case"fulfilled":return _.value;case"rejected":throw _.reason;default:switch(typeof _.status=="string"?_.then(Ce,Ce):(_.status="pending",_.then(function(Q){_.status==="pending"&&(_.status="fulfilled",_.value=Q)},function(Q){_.status==="pending"&&(_.status="rejected",_.reason=Q)})),_.status){case"fulfilled":return _.value;case"rejected":throw _.reason}}throw _}function F(_,Q,I,G,ee){var he=typeof _;(he==="undefined"||he==="boolean")&&(_=null);var re=!1;if(_===null)re=!0;else switch(he){case"bigint":case"string":case"number":re=!0;break;case"object":switch(_.$$typeof){case a:case l:re=!0;break;case y:return re=_._init,F(re(_._payload),Q,I,G,ee)}}if(re)return ee=ee(_),re=G===""?"."+ve(_,0):G,J(ee)?(I="",re!=null&&(I=re.replace(be,"$&/")+"/"),F(ee,Q,I,"",function(nn){return nn})):ee!=null&&(oe(ee)&&(ee=W(ee,I+(ee.key==null||_&&_.key===ee.key?"":(""+ee.key).replace(be,"$&/")+"/")+re)),Q.push(ee)),1;re=0;var ut=G===""?".":G+":";if(J(_))for(var Me=0;Me<_.length;Me++)G=_[Me],he=ut+ve(G,Me),re+=F(G,Q,I,he,ee);else if(Me=S(_),typeof Me=="function")for(_=Me.call(_),Me=0;!(G=_.next()).done;)G=G.value,he=ut+ve(G,Me++),re+=F(G,Q,I,he,ee);else if(he==="object"){if(typeof _.then=="function")return F(De(_),Q,I,G,ee);throw Q=String(_),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return re}function M(_,Q,I){if(_==null)return _;var G=[],ee=0;return F(_,G,"","",function(he){return Q.call(I,he,ee++)}),G}function P(_){if(_._status===-1){var Q=_._result;Q=Q(),Q.then(function(I){(_._status===0||_._status===-1)&&(_._status=1,_._result=I)},function(I){(_._status===0||_._status===-1)&&(_._status=2,_._result=I)}),_._status===-1&&(_._status=0,_._result=Q)}if(_._status===1)return _._result.default;throw _._result}var X=typeof reportError=="function"?reportError:function(_){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof _=="object"&&_!==null&&typeof _.message=="string"?String(_.message):String(_),error:_});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",_);return}console.error(_)};function ue(){}return ce.Children={map:M,forEach:function(_,Q,I){M(_,function(){Q.apply(this,arguments)},I)},count:function(_){var Q=0;return M(_,function(){Q++}),Q},toArray:function(_){return M(_,function(Q){return Q})||[]},only:function(_){if(!oe(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},ce.Component=N,ce.Fragment=r,ce.Profiler=u,ce.PureComponent=H,ce.StrictMode=o,ce.Suspense=m,ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Y,ce.__COMPILER_RUNTIME={__proto__:null,c:function(_){return Y.H.useMemoCache(_)}},ce.cache=function(_){return function(){return _.apply(null,arguments)}},ce.cloneElement=function(_,Q,I){if(_==null)throw Error("The argument must be a React element, but you passed "+_+".");var G=A({},_.props),ee=_.key,he=void 0;if(Q!=null)for(re in Q.ref!==void 0&&(he=void 0),Q.key!==void 0&&(ee=""+Q.key),Q)!Z.call(Q,re)||re==="key"||re==="__self"||re==="__source"||re==="ref"&&Q.ref===void 0||(G[re]=Q[re]);var re=arguments.length-2;if(re===1)G.children=I;else if(1<re){for(var ut=Array(re),Me=0;Me<re;Me++)ut[Me]=arguments[Me+2];G.children=ut}return le(_.type,ee,void 0,void 0,he,G)},ce.createContext=function(_){return _={$$typeof:d,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null},_.Provider=_,_.Consumer={$$typeof:f,_context:_},_},ce.createElement=function(_,Q,I){var G,ee={},he=null;if(Q!=null)for(G in Q.key!==void 0&&(he=""+Q.key),Q)Z.call(Q,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(ee[G]=Q[G]);var re=arguments.length-2;if(re===1)ee.children=I;else if(1<re){for(var ut=Array(re),Me=0;Me<re;Me++)ut[Me]=arguments[Me+2];ee.children=ut}if(_&&_.defaultProps)for(G in re=_.defaultProps,re)ee[G]===void 0&&(ee[G]=re[G]);return le(_,he,void 0,void 0,null,ee)},ce.createRef=function(){return{current:null}},ce.forwardRef=function(_){return{$$typeof:v,render:_}},ce.isValidElement=oe,ce.lazy=function(_){return{$$typeof:y,_payload:{_status:-1,_result:_},_init:P}},ce.memo=function(_,Q){return{$$typeof:g,type:_,compare:Q===void 0?null:Q}},ce.startTransition=function(_){var Q=Y.T,I={};Y.T=I;try{var G=_(),ee=Y.S;ee!==null&&ee(I,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(ue,X)}catch(he){X(he)}finally{Y.T=Q}},ce.unstable_useCacheRefresh=function(){return Y.H.useCacheRefresh()},ce.use=function(_){return Y.H.use(_)},ce.useActionState=function(_,Q,I){return Y.H.useActionState(_,Q,I)},ce.useCallback=function(_,Q){return Y.H.useCallback(_,Q)},ce.useContext=function(_){return Y.H.useContext(_)},ce.useDebugValue=function(){},ce.useDeferredValue=function(_,Q){return Y.H.useDeferredValue(_,Q)},ce.useEffect=function(_,Q,I){var G=Y.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(_,Q)},ce.useId=function(){return Y.H.useId()},ce.useImperativeHandle=function(_,Q,I){return Y.H.useImperativeHandle(_,Q,I)},ce.useInsertionEffect=function(_,Q){return Y.H.useInsertionEffect(_,Q)},ce.useLayoutEffect=function(_,Q){return Y.H.useLayoutEffect(_,Q)},ce.useMemo=function(_,Q){return Y.H.useMemo(_,Q)},ce.useOptimistic=function(_,Q){return Y.H.useOptimistic(_,Q)},ce.useReducer=function(_,Q,I){return Y.H.useReducer(_,Q,I)},ce.useRef=function(_){return Y.H.useRef(_)},ce.useState=function(_){return Y.H.useState(_)},ce.useSyncExternalStore=function(_,Q,I){return Y.H.useSyncExternalStore(_,Q,I)},ce.useTransition=function(){return Y.H.useTransition()},ce.version="19.1.0",ce}var hg;function Hs(){return hg||(hg=1,cc.exports=Fb()),cc.exports}var w=Hs();const q=Wb(w);var fc={exports:{}},Ll={},dc={exports:{}},hc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mg;function Jb(){return mg||(mg=1,function(a){function l(M,P){var X=M.length;M.push(P);e:for(;0<X;){var ue=X-1>>>1,_=M[ue];if(0<u(_,P))M[ue]=P,M[X]=_,X=ue;else break e}}function r(M){return M.length===0?null:M[0]}function o(M){if(M.length===0)return null;var P=M[0],X=M.pop();if(X!==P){M[0]=X;e:for(var ue=0,_=M.length,Q=_>>>1;ue<Q;){var I=2*(ue+1)-1,G=M[I],ee=I+1,he=M[ee];if(0>u(G,X))ee<_&&0>u(he,G)?(M[ue]=he,M[ee]=X,ue=ee):(M[ue]=G,M[I]=X,ue=I);else if(ee<_&&0>u(he,X))M[ue]=he,M[ee]=X,ue=ee;else break e}}return P}function u(M,P){var X=M.sortIndex-P.sortIndex;return X!==0?X:M.id-P.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,v=d.now();a.unstable_now=function(){return d.now()-v}}var m=[],g=[],y=1,O=null,S=3,R=!1,A=!1,L=!1,N=!1,C=typeof setTimeout=="function"?setTimeout:null,H=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function J(M){for(var P=r(g);P!==null;){if(P.callback===null)o(g);else if(P.startTime<=M)o(g),P.sortIndex=P.expirationTime,l(m,P);else break;P=r(g)}}function Y(M){if(L=!1,J(M),!A)if(r(m)!==null)A=!0,Z||(Z=!0,ve());else{var P=r(g);P!==null&&F(Y,P.startTime-M)}}var Z=!1,le=-1,W=5,oe=-1;function ne(){return N?!0:!(a.unstable_now()-oe<W)}function be(){if(N=!1,Z){var M=a.unstable_now();oe=M;var P=!0;try{e:{A=!1,L&&(L=!1,H(le),le=-1),R=!0;var X=S;try{t:{for(J(M),O=r(m);O!==null&&!(O.expirationTime>M&&ne());){var ue=O.callback;if(typeof ue=="function"){O.callback=null,S=O.priorityLevel;var _=ue(O.expirationTime<=M);if(M=a.unstable_now(),typeof _=="function"){O.callback=_,J(M),P=!0;break t}O===r(m)&&o(m),J(M)}else o(m);O=r(m)}if(O!==null)P=!0;else{var Q=r(g);Q!==null&&F(Y,Q.startTime-M),P=!1}}break e}finally{O=null,S=X,R=!1}P=void 0}}finally{P?ve():Z=!1}}}var ve;if(typeof $=="function")ve=function(){$(be)};else if(typeof MessageChannel<"u"){var Ce=new MessageChannel,De=Ce.port2;Ce.port1.onmessage=be,ve=function(){De.postMessage(null)}}else ve=function(){C(be,0)};function F(M,P){le=C(function(){M(a.unstable_now())},P)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(M){M.callback=null},a.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<M?Math.floor(1e3/M):5},a.unstable_getCurrentPriorityLevel=function(){return S},a.unstable_next=function(M){switch(S){case 1:case 2:case 3:var P=3;break;default:P=S}var X=S;S=P;try{return M()}finally{S=X}},a.unstable_requestPaint=function(){N=!0},a.unstable_runWithPriority=function(M,P){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var X=S;S=M;try{return P()}finally{S=X}},a.unstable_scheduleCallback=function(M,P,X){var ue=a.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?ue+X:ue):X=ue,M){case 1:var _=-1;break;case 2:_=250;break;case 5:_=1073741823;break;case 4:_=1e4;break;default:_=5e3}return _=X+_,M={id:y++,callback:P,priorityLevel:M,startTime:X,expirationTime:_,sortIndex:-1},X>ue?(M.sortIndex=X,l(g,M),r(m)===null&&M===r(g)&&(L?(H(le),le=-1):L=!0,F(Y,X-ue))):(M.sortIndex=_,l(m,M),A||R||(A=!0,Z||(Z=!0,ve()))),M},a.unstable_shouldYield=ne,a.unstable_wrapCallback=function(M){var P=S;return function(){var X=S;S=P;try{return M.apply(this,arguments)}finally{S=X}}}}(hc)),hc}var gg;function e0(){return gg||(gg=1,dc.exports=Jb()),dc.exports}var mc={exports:{}},dt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pg;function t0(){if(pg)return dt;pg=1;var a=Hs();function l(m){var g="https://react.dev/errors/"+m;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)g+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+m+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(l(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},u=Symbol.for("react.portal");function f(m,g,y){var O=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:O==null?null:""+O,children:m,containerInfo:g,implementation:y}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(m,g){if(m==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return dt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,dt.createPortal=function(m,g){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(l(299));return f(m,g,null,y)},dt.flushSync=function(m){var g=d.T,y=o.p;try{if(d.T=null,o.p=2,m)return m()}finally{d.T=g,o.p=y,o.d.f()}},dt.preconnect=function(m,g){typeof m=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(m,g))},dt.prefetchDNS=function(m){typeof m=="string"&&o.d.D(m)},dt.preinit=function(m,g){if(typeof m=="string"&&g&&typeof g.as=="string"){var y=g.as,O=v(y,g.crossOrigin),S=typeof g.integrity=="string"?g.integrity:void 0,R=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;y==="style"?o.d.S(m,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:O,integrity:S,fetchPriority:R}):y==="script"&&o.d.X(m,{crossOrigin:O,integrity:S,fetchPriority:R,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},dt.preinitModule=function(m,g){if(typeof m=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var y=v(g.as,g.crossOrigin);o.d.M(m,{crossOrigin:y,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(m)},dt.preload=function(m,g){if(typeof m=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var y=g.as,O=v(y,g.crossOrigin);o.d.L(m,y,{crossOrigin:O,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},dt.preloadModule=function(m,g){if(typeof m=="string")if(g){var y=v(g.as,g.crossOrigin);o.d.m(m,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:y,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(m)},dt.requestFormReset=function(m){o.d.r(m)},dt.unstable_batchedUpdates=function(m,g){return m(g)},dt.useFormState=function(m,g,y){return d.H.useFormState(m,g,y)},dt.useFormStatus=function(){return d.H.useHostTransitionStatus()},dt.version="19.1.0",dt}var vg;function gp(){if(vg)return mc.exports;vg=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),mc.exports=t0(),mc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yg;function n0(){if(yg)return Ll;yg=1;var a=e0(),l=Hs(),r=gp();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(f(e)!==e)throw Error(o(188))}function m(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,i=t;;){var s=n.return;if(s===null)break;var c=s.alternate;if(c===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===c.child){for(c=s.child;c;){if(c===n)return v(s),e;if(c===i)return v(s),t;c=c.sibling}throw Error(o(188))}if(n.return!==i.return)n=s,i=c;else{for(var h=!1,p=s.child;p;){if(p===n){h=!0,n=s,i=c;break}if(p===i){h=!0,i=s,n=c;break}p=p.sibling}if(!h){for(p=c.child;p;){if(p===n){h=!0,n=c,i=s;break}if(p===i){h=!0,i=c,n=s;break}p=p.sibling}if(!h)throw Error(o(189))}}if(n.alternate!==i)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,O=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),R=Symbol.for("react.portal"),A=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),H=Symbol.for("react.consumer"),$=Symbol.for("react.context"),J=Symbol.for("react.forward_ref"),Y=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),le=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),ne=Symbol.for("react.memo_cache_sentinel"),be=Symbol.iterator;function ve(e){return e===null||typeof e!="object"?null:(e=be&&e[be]||e["@@iterator"],typeof e=="function"?e:null)}var Ce=Symbol.for("react.client.reference");function De(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ce?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case A:return"Fragment";case N:return"Profiler";case L:return"StrictMode";case Y:return"Suspense";case Z:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case R:return"Portal";case $:return(e.displayName||"Context")+".Provider";case H:return(e._context.displayName||"Context")+".Consumer";case J:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case le:return t=e.displayName||null,t!==null?t:De(e.type)||"Memo";case W:t=e._payload,e=e._init;try{return De(e(t))}catch{}}return null}var F=Array.isArray,M=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X={pending:!1,data:null,method:null,action:null},ue=[],_=-1;function Q(e){return{current:e}}function I(e){0>_||(e.current=ue[_],ue[_]=null,_--)}function G(e,t){_++,ue[_]=e.current,e.current=t}var ee=Q(null),he=Q(null),re=Q(null),ut=Q(null);function Me(e,t){switch(G(re,t),G(he,e),G(ee,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?qm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=qm(t),e=Bm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(ee),G(ee,e)}function nn(){I(ee),I(he),I(re)}function mn(e){e.memoizedState!==null&&G(ut,e);var t=ee.current,n=Bm(t,e.type);t!==n&&(G(he,e),G(ee,n))}function ha(e){he.current===e&&(I(ee),I(he)),ut.current===e&&(I(ut),Rl._currentValue=X)}var nr=Object.prototype.hasOwnProperty,xe=a.unstable_scheduleCallback,Ee=a.unstable_cancelCallback,Fe=a.unstable_shouldYield,ze=a.unstable_requestPaint,He=a.unstable_now,Ln=a.unstable_getCurrentPriorityLevel,Ie=a.unstable_ImmediatePriority,Je=a.unstable_UserBlockingPriority,bt=a.unstable_NormalPriority,ar=a.unstable_LowPriority,zi=a.unstable_IdlePriority,ir=a.log,St=a.unstable_setDisableYieldValue,gt=null,it=null;function an(e){if(typeof ir=="function"&&St(e),it&&typeof it.setStrictMode=="function")try{it.setStrictMode(gt,e)}catch{}}var lt=Math.clz32?Math.clz32:Lv,Uv=Math.log,Dv=Math.LN2;function Lv(e){return e>>>=0,e===0?32:31-(Uv(e)/Dv|0)|0}var lr=256,rr=4194304;function ma(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function sr(e,t,n){var i=e.pendingLanes;if(i===0)return 0;var s=0,c=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var p=i&134217727;return p!==0?(i=p&~c,i!==0?s=ma(i):(h&=p,h!==0?s=ma(h):n||(n=p&~e,n!==0&&(s=ma(n))))):(p=i&~c,p!==0?s=ma(p):h!==0?s=ma(h):n||(n=i&~e,n!==0&&(s=ma(n)))),s===0?0:t!==0&&t!==s&&(t&c)===0&&(c=s&-s,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:s}function qi(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Nv(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _f(){var e=lr;return lr<<=1,(lr&4194048)===0&&(lr=256),e}function Ef(){var e=rr;return rr<<=1,(rr&62914560)===0&&(rr=4194304),e}function Zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Bi(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function zv(e,t,n,i,s,c){var h=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,b=e.expirationTimes,j=e.hiddenUpdates;for(n=h&~n;0<n;){var z=31-lt(n),V=1<<z;p[z]=0,b[z]=-1;var U=j[z];if(U!==null)for(j[z]=null,z=0;z<U.length;z++){var D=U[z];D!==null&&(D.lane&=-536870913)}n&=~V}i!==0&&xf(e,i,0),c!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=c&~(h&~t))}function xf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-lt(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|n&4194090}function wf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-lt(n),s=1<<i;s&t|e[i]&t&&(e[i]|=t),n&=~s}}function Fs(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Af(){var e=P.p;return e!==0?e:(e=window.event,e===void 0?32:ig(e.type))}function qv(e,t){var n=P.p;try{return P.p=e,t()}finally{P.p=n}}var Nn=Math.random().toString(36).slice(2),ct="__reactFiber$"+Nn,_t="__reactProps$"+Nn,Na="__reactContainer$"+Nn,eo="__reactEvents$"+Nn,Bv="__reactListeners$"+Nn,Hv="__reactHandles$"+Nn,Cf="__reactResources$"+Nn,Hi="__reactMarker$"+Nn;function to(e){delete e[ct],delete e[_t],delete e[eo],delete e[Bv],delete e[Hv]}function za(e){var t=e[ct];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Na]||n[ct]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pm(e);e!==null;){if(n=e[ct])return n;e=Pm(e)}return t}e=n,n=e.parentNode}return null}function qa(e){if(e=e[ct]||e[Na]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Vi(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function Ba(e){var t=e[Cf];return t||(t=e[Cf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function et(e){e[Hi]=!0}var Tf=new Set,Of={};function ga(e,t){Ha(e,t),Ha(e+"Capture",t)}function Ha(e,t){for(Of[e]=t,e=0;e<t.length;e++)Tf.add(t[e])}var Vv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Rf={},kf={};function Qv(e){return nr.call(kf,e)?!0:nr.call(Rf,e)?!1:Vv.test(e)?kf[e]=!0:(Rf[e]=!0,!1)}function or(e,t,n){if(Qv(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function ur(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function gn(e,t,n,i){if(i===null)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+i)}}var no,jf;function Va(e){if(no===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);no=t&&t[1]||"",jf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+no+e+jf}var ao=!1;function io(e,t){if(!e||ao)return"";ao=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(D){var U=D}Reflect.construct(e,[],V)}else{try{V.call()}catch(D){U=D}e.call(V.prototype)}}else{try{throw Error()}catch(D){U=D}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(D){if(D&&U&&typeof D.stack=="string")return[D.stack,U.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),h=c[0],p=c[1];if(h&&p){var b=h.split(`
`),j=p.split(`
`);for(s=i=0;i<b.length&&!b[i].includes("DetermineComponentFrameRoot");)i++;for(;s<j.length&&!j[s].includes("DetermineComponentFrameRoot");)s++;if(i===b.length||s===j.length)for(i=b.length-1,s=j.length-1;1<=i&&0<=s&&b[i]!==j[s];)s--;for(;1<=i&&0<=s;i--,s--)if(b[i]!==j[s]){if(i!==1||s!==1)do if(i--,s--,0>s||b[i]!==j[s]){var z=`
`+b[i].replace(" at new "," at ");return e.displayName&&z.includes("<anonymous>")&&(z=z.replace("<anonymous>",e.displayName)),z}while(1<=i&&0<=s);break}}}finally{ao=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Va(n):""}function Pv(e){switch(e.tag){case 26:case 27:case 5:return Va(e.type);case 16:return Va("Lazy");case 13:return Va("Suspense");case 19:return Va("SuspenseList");case 0:case 15:return io(e.type,!1);case 11:return io(e.type.render,!1);case 1:return io(e.type,!0);case 31:return Va("Activity");default:return""}}function Mf(e){try{var t="";do t+=Pv(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Nt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Uf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yv(e){var t=Uf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(h){i=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(h){i=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function cr(e){e._valueTracker||(e._valueTracker=Yv(e))}function Df(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=Uf(e)?e.checked?"true":"false":e.value),e=i,e!==n?(t.setValue(e),!0):!1}function fr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var $v=/[\n"\\]/g;function zt(e){return e.replace($v,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function lo(e,t,n,i,s,c,h,p){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Nt(t)):e.value!==""+Nt(t)&&(e.value=""+Nt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?ro(e,h,Nt(t)):n!=null?ro(e,h,Nt(n)):i!=null&&e.removeAttribute("value"),s==null&&c!=null&&(e.defaultChecked=!!c),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Nt(p):e.removeAttribute("name")}function Lf(e,t,n,i,s,c,h,p){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+Nt(n):"",t=t!=null?""+Nt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=p?e.checked:!!i,e.defaultChecked=!!i,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function ro(e,t,n){t==="number"&&fr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Qa(e,t,n,i){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&i&&(e[n].defaultSelected=!0)}else{for(n=""+Nt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,i&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Nf(e,t,n){if(t!=null&&(t=""+Nt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Nt(n):""}function zf(e,t,n,i){if(t==null){if(i!=null){if(n!=null)throw Error(o(92));if(F(i)){if(1<i.length)throw Error(o(93));i=i[0]}n=i}n==null&&(n=""),t=n}n=Nt(t),e.defaultValue=n,i=e.textContent,i===n&&i!==""&&i!==null&&(e.value=i)}function Pa(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Iv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function qf(e,t,n){var i=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,n):typeof n!="number"||n===0||Iv.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Bf(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var s in t)i=t[s],t.hasOwnProperty(s)&&n[s]!==i&&qf(e,s,i)}else for(var c in t)t.hasOwnProperty(c)&&qf(e,c,t[c])}function so(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Gv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Xv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function dr(e){return Xv.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var oo=null;function uo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ya=null,$a=null;function Hf(e){var t=qa(e);if(t&&(e=t.stateNode)){var n=e[_t]||null;e:switch(e=t.stateNode,t.type){case"input":if(lo(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+zt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var s=i[_t]||null;if(!s)throw Error(o(90));lo(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<n.length;t++)i=n[t],i.form===e.form&&Df(i)}break e;case"textarea":Nf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Qa(e,!!n.multiple,t,!1)}}}var co=!1;function Vf(e,t,n){if(co)return e(t,n);co=!0;try{var i=e(t);return i}finally{if(co=!1,(Ya!==null||$a!==null)&&(Zr(),Ya&&(t=Ya,e=$a,$a=Ya=null,Hf(t),e)))for(t=0;t<e.length;t++)Hf(e[t])}}function Qi(e,t){var n=e.stateNode;if(n===null)return null;var i=n[_t]||null;if(i===null)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var pn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fo=!1;if(pn)try{var Pi={};Object.defineProperty(Pi,"passive",{get:function(){fo=!0}}),window.addEventListener("test",Pi,Pi),window.removeEventListener("test",Pi,Pi)}catch{fo=!1}var zn=null,ho=null,hr=null;function Qf(){if(hr)return hr;var e,t=ho,n=t.length,i,s="value"in zn?zn.value:zn.textContent,c=s.length;for(e=0;e<n&&t[e]===s[e];e++);var h=n-e;for(i=1;i<=h&&t[n-i]===s[c-i];i++);return hr=s.slice(e,1<i?1-i:void 0)}function mr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function gr(){return!0}function Pf(){return!1}function Et(e){function t(n,i,s,c,h){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(c):c[p]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?gr:Pf,this.isPropagationStopped=Pf,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gr)},persist:function(){},isPersistent:gr}),t}var pa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pr=Et(pa),Yi=y({},pa,{view:0,detail:0}),Wv=Et(Yi),mo,go,$i,vr=y({},Yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$i&&($i&&e.type==="mousemove"?(mo=e.screenX-$i.screenX,go=e.screenY-$i.screenY):go=mo=0,$i=e),mo)},movementY:function(e){return"movementY"in e?e.movementY:go}}),Yf=Et(vr),Kv=y({},vr,{dataTransfer:0}),Zv=Et(Kv),Fv=y({},Yi,{relatedTarget:0}),po=Et(Fv),Jv=y({},pa,{animationName:0,elapsedTime:0,pseudoElement:0}),ey=Et(Jv),ty=y({},pa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ny=Et(ty),ay=y({},pa,{data:0}),$f=Et(ay),iy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ly={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ry={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ry[e])?!!t[e]:!1}function vo(){return sy}var oy=y({},Yi,{key:function(e){if(e.key){var t=iy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=mr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ly[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vo,charCode:function(e){return e.type==="keypress"?mr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?mr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),uy=Et(oy),cy=y({},vr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),If=Et(cy),fy=y({},Yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vo}),dy=Et(fy),hy=y({},pa,{propertyName:0,elapsedTime:0,pseudoElement:0}),my=Et(hy),gy=y({},vr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),py=Et(gy),vy=y({},pa,{newState:0,oldState:0}),yy=Et(vy),by=[9,13,27,32],yo=pn&&"CompositionEvent"in window,Ii=null;pn&&"documentMode"in document&&(Ii=document.documentMode);var Sy=pn&&"TextEvent"in window&&!Ii,Gf=pn&&(!yo||Ii&&8<Ii&&11>=Ii),Xf=" ",Wf=!1;function Kf(e,t){switch(e){case"keyup":return by.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ia=!1;function _y(e,t){switch(e){case"compositionend":return Zf(t);case"keypress":return t.which!==32?null:(Wf=!0,Xf);case"textInput":return e=t.data,e===Xf&&Wf?null:e;default:return null}}function Ey(e,t){if(Ia)return e==="compositionend"||!yo&&Kf(e,t)?(e=Qf(),hr=ho=zn=null,Ia=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Gf&&t.locale!=="ko"?null:t.data;default:return null}}var xy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ff(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xy[e.type]:t==="textarea"}function Jf(e,t,n,i){Ya?$a?$a.push(i):$a=[i]:Ya=i,t=as(t,"onChange"),0<t.length&&(n=new pr("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var Gi=null,Xi=null;function wy(e){Um(e,0)}function yr(e){var t=Vi(e);if(Df(t))return e}function ed(e,t){if(e==="change")return t}var td=!1;if(pn){var bo;if(pn){var So="oninput"in document;if(!So){var nd=document.createElement("div");nd.setAttribute("oninput","return;"),So=typeof nd.oninput=="function"}bo=So}else bo=!1;td=bo&&(!document.documentMode||9<document.documentMode)}function ad(){Gi&&(Gi.detachEvent("onpropertychange",id),Xi=Gi=null)}function id(e){if(e.propertyName==="value"&&yr(Xi)){var t=[];Jf(t,Xi,e,uo(e)),Vf(wy,t)}}function Ay(e,t,n){e==="focusin"?(ad(),Gi=t,Xi=n,Gi.attachEvent("onpropertychange",id)):e==="focusout"&&ad()}function Cy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return yr(Xi)}function Ty(e,t){if(e==="click")return yr(t)}function Oy(e,t){if(e==="input"||e==="change")return yr(t)}function Ry(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:Ry;function Wi(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!nr.call(t,s)||!Tt(e[s],t[s]))return!1}return!0}function ld(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function rd(e,t){var n=ld(e);e=0;for(var i;n;){if(n.nodeType===3){if(i=e+n.textContent.length,e<=t&&i>=t)return{node:n,offset:t-e};e=i}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ld(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function od(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=fr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=fr(e.document)}return t}function _o(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ky=pn&&"documentMode"in document&&11>=document.documentMode,Ga=null,Eo=null,Ki=null,xo=!1;function ud(e,t,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;xo||Ga==null||Ga!==fr(i)||(i=Ga,"selectionStart"in i&&_o(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ki&&Wi(Ki,i)||(Ki=i,i=as(Eo,"onSelect"),0<i.length&&(t=new pr("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=Ga)))}function va(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xa={animationend:va("Animation","AnimationEnd"),animationiteration:va("Animation","AnimationIteration"),animationstart:va("Animation","AnimationStart"),transitionrun:va("Transition","TransitionRun"),transitionstart:va("Transition","TransitionStart"),transitioncancel:va("Transition","TransitionCancel"),transitionend:va("Transition","TransitionEnd")},wo={},cd={};pn&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete Xa.animationend.animation,delete Xa.animationiteration.animation,delete Xa.animationstart.animation),"TransitionEvent"in window||delete Xa.transitionend.transition);function ya(e){if(wo[e])return wo[e];if(!Xa[e])return e;var t=Xa[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in cd)return wo[e]=t[n];return e}var fd=ya("animationend"),dd=ya("animationiteration"),hd=ya("animationstart"),jy=ya("transitionrun"),My=ya("transitionstart"),Uy=ya("transitioncancel"),md=ya("transitionend"),gd=new Map,Ao="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ao.push("scrollEnd");function Wt(e,t){gd.set(e,t),ga(t,[e])}var pd=new WeakMap;function qt(e,t){if(typeof e=="object"&&e!==null){var n=pd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Mf(t)},pd.set(e,t),t)}return{value:e,source:t,stack:Mf(t)}}var Bt=[],Wa=0,Co=0;function br(){for(var e=Wa,t=Co=Wa=0;t<e;){var n=Bt[t];Bt[t++]=null;var i=Bt[t];Bt[t++]=null;var s=Bt[t];Bt[t++]=null;var c=Bt[t];if(Bt[t++]=null,i!==null&&s!==null){var h=i.pending;h===null?s.next=s:(s.next=h.next,h.next=s),i.pending=s}c!==0&&vd(n,s,c)}}function Sr(e,t,n,i){Bt[Wa++]=e,Bt[Wa++]=t,Bt[Wa++]=n,Bt[Wa++]=i,Co|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function To(e,t,n,i){return Sr(e,t,n,i),_r(e)}function Ka(e,t){return Sr(e,null,null,t),_r(e)}function vd(e,t,n){e.lanes|=n;var i=e.alternate;i!==null&&(i.lanes|=n);for(var s=!1,c=e.return;c!==null;)c.childLanes|=n,i=c.alternate,i!==null&&(i.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(s=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,s&&t!==null&&(s=31-lt(n),e=c.hiddenUpdates,i=e[s],i===null?e[s]=[t]:i.push(t),t.lane=n|536870912),c):null}function _r(e){if(50<_l)throw _l=0,Uu=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Za={};function Dy(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,n,i){return new Dy(e,t,n,i)}function Oo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function vn(e,t){var n=e.alternate;return n===null?(n=Ot(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function yd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Er(e,t,n,i,s,c){var h=0;if(i=e,typeof e=="function")Oo(e)&&(h=1);else if(typeof e=="string")h=Nb(e,n,ee.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=Ot(31,n,t,s),e.elementType=oe,e.lanes=c,e;case A:return ba(n.children,s,c,t);case L:h=8,s|=24;break;case N:return e=Ot(12,n,t,s|2),e.elementType=N,e.lanes=c,e;case Y:return e=Ot(13,n,t,s),e.elementType=Y,e.lanes=c,e;case Z:return e=Ot(19,n,t,s),e.elementType=Z,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case C:case $:h=10;break e;case H:h=9;break e;case J:h=11;break e;case le:h=14;break e;case W:h=16,i=null;break e}h=29,n=Error(o(130,e===null?"null":typeof e,"")),i=null}return t=Ot(h,n,t,s),t.elementType=e,t.type=i,t.lanes=c,t}function ba(e,t,n,i){return e=Ot(7,e,i,t),e.lanes=n,e}function Ro(e,t,n){return e=Ot(6,e,null,t),e.lanes=n,e}function ko(e,t,n){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Fa=[],Ja=0,xr=null,wr=0,Ht=[],Vt=0,Sa=null,yn=1,bn="";function _a(e,t){Fa[Ja++]=wr,Fa[Ja++]=xr,xr=e,wr=t}function bd(e,t,n){Ht[Vt++]=yn,Ht[Vt++]=bn,Ht[Vt++]=Sa,Sa=e;var i=yn;e=bn;var s=32-lt(i)-1;i&=~(1<<s),n+=1;var c=32-lt(t)+s;if(30<c){var h=s-s%5;c=(i&(1<<h)-1).toString(32),i>>=h,s-=h,yn=1<<32-lt(t)+s|n<<s|i,bn=c+e}else yn=1<<c|n<<s|i,bn=e}function jo(e){e.return!==null&&(_a(e,1),bd(e,1,0))}function Mo(e){for(;e===xr;)xr=Fa[--Ja],Fa[Ja]=null,wr=Fa[--Ja],Fa[Ja]=null;for(;e===Sa;)Sa=Ht[--Vt],Ht[Vt]=null,bn=Ht[--Vt],Ht[Vt]=null,yn=Ht[--Vt],Ht[Vt]=null}var pt=null,Ve=null,_e=!1,Ea=null,ln=!1,Uo=Error(o(519));function xa(e){var t=Error(o(418,""));throw Ji(qt(t,e)),Uo}function Sd(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[ct]=e,t[_t]=i,n){case"dialog":pe("cancel",t),pe("close",t);break;case"iframe":case"object":case"embed":pe("load",t);break;case"video":case"audio":for(n=0;n<xl.length;n++)pe(xl[n],t);break;case"source":pe("error",t);break;case"img":case"image":case"link":pe("error",t),pe("load",t);break;case"details":pe("toggle",t);break;case"input":pe("invalid",t),Lf(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),cr(t);break;case"select":pe("invalid",t);break;case"textarea":pe("invalid",t),zf(t,i.value,i.defaultValue,i.children),cr(t)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||i.suppressHydrationWarning===!0||zm(t.textContent,n)?(i.popover!=null&&(pe("beforetoggle",t),pe("toggle",t)),i.onScroll!=null&&pe("scroll",t),i.onScrollEnd!=null&&pe("scrollend",t),i.onClick!=null&&(t.onclick=is),t=!0):t=!1,t||xa(e)}function _d(e){for(pt=e.return;pt;)switch(pt.tag){case 5:case 13:ln=!1;return;case 27:case 3:ln=!0;return;default:pt=pt.return}}function Zi(e){if(e!==pt)return!1;if(!_e)return _d(e),_e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Wu(e.type,e.memoizedProps)),n=!n),n&&Ve&&xa(e),_d(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ve=Zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ve=null}}else t===27?(t=Ve,Jn(e.type)?(e=Ju,Ju=null,Ve=e):Ve=t):Ve=pt?Zt(e.stateNode.nextSibling):null;return!0}function Fi(){Ve=pt=null,_e=!1}function Ed(){var e=Ea;return e!==null&&(At===null?At=e:At.push.apply(At,e),Ea=null),e}function Ji(e){Ea===null?Ea=[e]:Ea.push(e)}var Do=Q(null),wa=null,Sn=null;function qn(e,t,n){G(Do,t._currentValue),t._currentValue=n}function _n(e){e._currentValue=Do.current,I(Do)}function Lo(e,t,n){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function No(e,t,n,i){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var c=s.dependencies;if(c!==null){var h=s.child;c=c.firstContext;e:for(;c!==null;){var p=c;c=s;for(var b=0;b<t.length;b++)if(p.context===t[b]){c.lanes|=n,p=c.alternate,p!==null&&(p.lanes|=n),Lo(c.return,n,e),i||(h=null);break e}c=p.next}}else if(s.tag===18){if(h=s.return,h===null)throw Error(o(341));h.lanes|=n,c=h.alternate,c!==null&&(c.lanes|=n),Lo(h,n,e),h=null}else h=s.child;if(h!==null)h.return=s;else for(h=s;h!==null;){if(h===e){h=null;break}if(s=h.sibling,s!==null){s.return=h.return,h=s;break}h=h.return}s=h}}function el(e,t,n,i){e=null;for(var s=t,c=!1;s!==null;){if(!c){if((s.flags&524288)!==0)c=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var h=s.alternate;if(h===null)throw Error(o(387));if(h=h.memoizedProps,h!==null){var p=s.type;Tt(s.pendingProps.value,h.value)||(e!==null?e.push(p):e=[p])}}else if(s===ut.current){if(h=s.alternate,h===null)throw Error(o(387));h.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Rl):e=[Rl])}s=s.return}e!==null&&No(t,e,n,i),t.flags|=262144}function Ar(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Aa(e){wa=e,Sn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return xd(wa,e)}function Cr(e,t){return wa===null&&Aa(e),xd(e,t)}function xd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Sn===null){if(e===null)throw Error(o(308));Sn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Sn=Sn.next=t;return n}var Ly=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Ny=a.unstable_scheduleCallback,zy=a.unstable_NormalPriority,We={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function zo(){return{controller:new Ly,data:new Map,refCount:0}}function tl(e){e.refCount--,e.refCount===0&&Ny(zy,function(){e.controller.abort()})}var nl=null,qo=0,ei=0,ti=null;function qy(e,t){if(nl===null){var n=nl=[];qo=0,ei=Hu(),ti={status:"pending",value:void 0,then:function(i){n.push(i)}}}return qo++,t.then(wd,wd),t}function wd(){if(--qo===0&&nl!==null){ti!==null&&(ti.status="fulfilled");var e=nl;nl=null,ei=0,ti=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function By(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var s=0;s<n.length;s++)(0,n[s])(t)},function(s){for(i.status="rejected",i.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),i}var Ad=M.S;M.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&qy(e,t),Ad!==null&&Ad(e,t)};var Ca=Q(null);function Bo(){var e=Ca.current;return e!==null?e:Ue.pooledCache}function Tr(e,t){t===null?G(Ca,Ca.current):G(Ca,t.pool)}function Cd(){var e=Bo();return e===null?null:{parent:We._currentValue,pool:e}}var al=Error(o(460)),Td=Error(o(474)),Or=Error(o(542)),Ho={then:function(){}};function Od(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Rr(){}function Rd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Rr,Rr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,jd(e),e;default:if(typeof t.status=="string")t.then(Rr,Rr);else{if(e=Ue,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=i}},function(i){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,jd(e),e}throw il=t,al}}var il=null;function kd(){if(il===null)throw Error(o(459));var e=il;return il=null,e}function jd(e){if(e===al||e===Or)throw Error(o(483))}var Bn=!1;function Vo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Qo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Hn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Vn(e,t,n){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(we&2)!==0){var s=i.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),i.pending=t,t=_r(e),vd(e,null,n),t}return Sr(e,i,t,n),_r(e)}function ll(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,wf(e,n)}}function Po(e,t){var n=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?s=c=h:c=c.next=h,n=n.next}while(n!==null);c===null?s=c=t:c=c.next=t}else s=c=t;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Yo=!1;function rl(){if(Yo){var e=ti;if(e!==null)throw e}}function sl(e,t,n,i){Yo=!1;var s=e.updateQueue;Bn=!1;var c=s.firstBaseUpdate,h=s.lastBaseUpdate,p=s.shared.pending;if(p!==null){s.shared.pending=null;var b=p,j=b.next;b.next=null,h===null?c=j:h.next=j,h=b;var z=e.alternate;z!==null&&(z=z.updateQueue,p=z.lastBaseUpdate,p!==h&&(p===null?z.firstBaseUpdate=j:p.next=j,z.lastBaseUpdate=b))}if(c!==null){var V=s.baseState;h=0,z=j=b=null,p=c;do{var U=p.lane&-536870913,D=U!==p.lane;if(D?(ye&U)===U:(i&U)===U){U!==0&&U===ei&&(Yo=!0),z!==null&&(z=z.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var se=e,ae=p;U=t;var Re=n;switch(ae.tag){case 1:if(se=ae.payload,typeof se=="function"){V=se.call(Re,V,U);break e}V=se;break e;case 3:se.flags=se.flags&-65537|128;case 0:if(se=ae.payload,U=typeof se=="function"?se.call(Re,V,U):se,U==null)break e;V=y({},V,U);break e;case 2:Bn=!0}}U=p.callback,U!==null&&(e.flags|=64,D&&(e.flags|=8192),D=s.callbacks,D===null?s.callbacks=[U]:D.push(U))}else D={lane:U,tag:p.tag,payload:p.payload,callback:p.callback,next:null},z===null?(j=z=D,b=V):z=z.next=D,h|=U;if(p=p.next,p===null){if(p=s.shared.pending,p===null)break;D=p,p=D.next,D.next=null,s.lastBaseUpdate=D,s.shared.pending=null}}while(!0);z===null&&(b=V),s.baseState=b,s.firstBaseUpdate=j,s.lastBaseUpdate=z,c===null&&(s.shared.lanes=0),Wn|=h,e.lanes=h,e.memoizedState=V}}function Md(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Ud(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Md(n[e],t)}var ni=Q(null),kr=Q(0);function Dd(e,t){e=On,G(kr,e),G(ni,t),On=e|t.baseLanes}function $o(){G(kr,On),G(ni,ni.current)}function Io(){On=kr.current,I(ni),I(kr)}var Qn=0,de=null,Te=null,Ge=null,jr=!1,ai=!1,Ta=!1,Mr=0,ol=0,ii=null,Hy=0;function Ye(){throw Error(o(321))}function Go(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Tt(e[n],t[n]))return!1;return!0}function Xo(e,t,n,i,s,c){return Qn=c,de=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,M.H=e===null||e.memoizedState===null?vh:yh,Ta=!1,c=n(i,s),Ta=!1,ai&&(c=Nd(t,n,i,s)),Ld(e),c}function Ld(e){M.H=qr;var t=Te!==null&&Te.next!==null;if(Qn=0,Ge=Te=de=null,jr=!1,ol=0,ii=null,t)throw Error(o(300));e===null||tt||(e=e.dependencies,e!==null&&Ar(e)&&(tt=!0))}function Nd(e,t,n,i){de=e;var s=0;do{if(ai&&(ii=null),ol=0,ai=!1,25<=s)throw Error(o(301));if(s+=1,Ge=Te=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}M.H=Gy,c=t(n,i)}while(ai);return c}function Vy(){var e=M.H,t=e.useState()[0];return t=typeof t.then=="function"?ul(t):t,e=e.useState()[0],(Te!==null?Te.memoizedState:null)!==e&&(de.flags|=1024),t}function Wo(){var e=Mr!==0;return Mr=0,e}function Ko(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Zo(e){if(jr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}jr=!1}Qn=0,Ge=Te=de=null,ai=!1,ol=Mr=0,ii=null}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?de.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Xe(){if(Te===null){var e=de.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=Ge===null?de.memoizedState:Ge.next;if(t!==null)Ge=t,Te=e;else{if(e===null)throw de.alternate===null?Error(o(467)):Error(o(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},Ge===null?de.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Fo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ul(e){var t=ol;return ol+=1,ii===null&&(ii=[]),e=Rd(ii,e,t),t=de,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,M.H=t===null||t.memoizedState===null?vh:yh),e}function Ur(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ul(e);if(e.$$typeof===$)return ft(e)}throw Error(o(438,String(e)))}function Jo(e){var t=null,n=de.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var i=de.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Fo(),de.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=ne;return t.index++,n}function En(e,t){return typeof t=="function"?t(e):t}function Dr(e){var t=Xe();return eu(t,Te,e)}function eu(e,t,n){var i=e.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=n;var s=e.baseQueue,c=i.pending;if(c!==null){if(s!==null){var h=s.next;s.next=c.next,c.next=h}t.baseQueue=s=c,i.pending=null}if(c=e.baseState,s===null)e.memoizedState=c;else{t=s.next;var p=h=null,b=null,j=t,z=!1;do{var V=j.lane&-536870913;if(V!==j.lane?(ye&V)===V:(Qn&V)===V){var U=j.revertLane;if(U===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null}),V===ei&&(z=!0);else if((Qn&U)===U){j=j.next,U===ei&&(z=!0);continue}else V={lane:0,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},b===null?(p=b=V,h=c):b=b.next=V,de.lanes|=U,Wn|=U;V=j.action,Ta&&n(c,V),c=j.hasEagerState?j.eagerState:n(c,V)}else U={lane:V,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},b===null?(p=b=U,h=c):b=b.next=U,de.lanes|=V,Wn|=V;j=j.next}while(j!==null&&j!==t);if(b===null?h=c:b.next=p,!Tt(c,e.memoizedState)&&(tt=!0,z&&(n=ti,n!==null)))throw n;e.memoizedState=c,e.baseState=h,e.baseQueue=b,i.lastRenderedState=c}return s===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function tu(e){var t=Xe(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var i=n.dispatch,s=n.pending,c=t.memoizedState;if(s!==null){n.pending=null;var h=s=s.next;do c=e(c,h.action),h=h.next;while(h!==s);Tt(c,t.memoizedState)||(tt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,i]}function zd(e,t,n){var i=de,s=Xe(),c=_e;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=t();var h=!Tt((Te||s).memoizedState,n);h&&(s.memoizedState=n,tt=!0),s=s.queue;var p=Hd.bind(null,i,s,e);if(cl(2048,8,p,[e]),s.getSnapshot!==t||h||Ge!==null&&Ge.memoizedState.tag&1){if(i.flags|=2048,li(9,Lr(),Bd.bind(null,i,s,n,t),null),Ue===null)throw Error(o(349));c||(Qn&124)!==0||qd(i,t,n)}return n}function qd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=de.updateQueue,t===null?(t=Fo(),de.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bd(e,t,n,i){t.value=n,t.getSnapshot=i,Vd(t)&&Qd(e)}function Hd(e,t,n){return n(function(){Vd(t)&&Qd(e)})}function Vd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Tt(e,n)}catch{return!0}}function Qd(e){var t=Ka(e,2);t!==null&&Ut(t,e,2)}function nu(e){var t=xt();if(typeof e=="function"){var n=e;if(e=n(),Ta){an(!0);try{n()}finally{an(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:e},t}function Pd(e,t,n,i){return e.baseState=n,eu(e,Te,typeof i=="function"?i:En)}function Qy(e,t,n,i,s){if(zr(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){c.listeners.push(h)}};M.T!==null?n(!0):c.isTransition=!1,i(c),n=t.pending,n===null?(c.next=t.pending=c,Yd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Yd(e,t){var n=t.action,i=t.payload,s=e.state;if(t.isTransition){var c=M.T,h={};M.T=h;try{var p=n(s,i),b=M.S;b!==null&&b(h,p),$d(e,t,p)}catch(j){au(e,t,j)}finally{M.T=c}}else try{c=n(s,i),$d(e,t,c)}catch(j){au(e,t,j)}}function $d(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Id(e,t,i)},function(i){return au(e,t,i)}):Id(e,t,n)}function Id(e,t,n){t.status="fulfilled",t.value=n,Gd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Yd(e,n)))}function au(e,t,n){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=n,Gd(t),t=t.next;while(t!==i)}e.action=null}function Gd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Xd(e,t){return t}function Wd(e,t){if(_e){var n=Ue.formState;if(n!==null){e:{var i=de;if(_e){if(Ve){t:{for(var s=Ve,c=ln;s.nodeType!==8;){if(!c){s=null;break t}if(s=Zt(s.nextSibling),s===null){s=null;break t}}c=s.data,s=c==="F!"||c==="F"?s:null}if(s){Ve=Zt(s.nextSibling),i=s.data==="F!";break e}}xa(i)}i=!1}i&&(t=n[0])}}return n=xt(),n.memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xd,lastRenderedState:t},n.queue=i,n=mh.bind(null,de,i),i.dispatch=n,i=nu(!1),c=ou.bind(null,de,!1,i.queue),i=xt(),s={state:t,dispatch:null,action:e,pending:null},i.queue=s,n=Qy.bind(null,de,s,c,n),s.dispatch=n,i.memoizedState=e,[t,n,!1]}function Kd(e){var t=Xe();return Zd(t,Te,e)}function Zd(e,t,n){if(t=eu(e,t,Xd)[0],e=Dr(En)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=ul(t)}catch(h){throw h===al?Or:h}else i=t;t=Xe();var s=t.queue,c=s.dispatch;return n!==t.memoizedState&&(de.flags|=2048,li(9,Lr(),Py.bind(null,s,n),null)),[i,c,e]}function Py(e,t){e.action=t}function Fd(e){var t=Xe(),n=Te;if(n!==null)return Zd(t,n,e);Xe(),t=t.memoizedState,n=Xe();var i=n.queue.dispatch;return n.memoizedState=e,[t,i,!1]}function li(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},t=de.updateQueue,t===null&&(t=Fo(),de.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function Lr(){return{destroy:void 0,resource:void 0}}function Jd(){return Xe().memoizedState}function Nr(e,t,n,i){var s=xt();i=i===void 0?null:i,de.flags|=e,s.memoizedState=li(1|t,Lr(),n,i)}function cl(e,t,n,i){var s=Xe();i=i===void 0?null:i;var c=s.memoizedState.inst;Te!==null&&i!==null&&Go(i,Te.memoizedState.deps)?s.memoizedState=li(t,c,n,i):(de.flags|=e,s.memoizedState=li(1|t,c,n,i))}function eh(e,t){Nr(8390656,8,e,t)}function th(e,t){cl(2048,8,e,t)}function nh(e,t){return cl(4,2,e,t)}function ah(e,t){return cl(4,4,e,t)}function ih(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function lh(e,t,n){n=n!=null?n.concat([e]):null,cl(4,4,ih.bind(null,t,e),n)}function iu(){}function rh(e,t){var n=Xe();t=t===void 0?null:t;var i=n.memoizedState;return t!==null&&Go(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function sh(e,t){var n=Xe();t=t===void 0?null:t;var i=n.memoizedState;if(t!==null&&Go(t,i[1]))return i[0];if(i=e(),Ta){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[i,t],i}function lu(e,t,n){return n===void 0||(Qn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=cm(),de.lanes|=e,Wn|=e,n)}function oh(e,t,n,i){return Tt(n,t)?n:ni.current!==null?(e=lu(e,n,i),Tt(e,t)||(tt=!0),e):(Qn&42)===0?(tt=!0,e.memoizedState=n):(e=cm(),de.lanes|=e,Wn|=e,t)}function uh(e,t,n,i,s){var c=P.p;P.p=c!==0&&8>c?c:8;var h=M.T,p={};M.T=p,ou(e,!1,t,n);try{var b=s(),j=M.S;if(j!==null&&j(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var z=By(b,i);fl(e,t,z,Mt(e))}else fl(e,t,i,Mt(e))}catch(V){fl(e,t,{then:function(){},status:"rejected",reason:V},Mt())}finally{P.p=c,M.T=h}}function Yy(){}function ru(e,t,n,i){if(e.tag!==5)throw Error(o(476));var s=ch(e).queue;uh(e,s,t,X,n===null?Yy:function(){return fh(e),n(i)})}function ch(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:X,baseState:X,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:X},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function fh(e){var t=ch(e).next.queue;fl(e,t,{},Mt())}function su(){return ft(Rl)}function dh(){return Xe().memoizedState}function hh(){return Xe().memoizedState}function $y(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Mt();e=Hn(n);var i=Vn(t,e,n);i!==null&&(Ut(i,t,n),ll(i,t,n)),t={cache:zo()},e.payload=t;return}t=t.return}}function Iy(e,t,n){var i=Mt();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},zr(e)?gh(t,n):(n=To(e,t,n,i),n!==null&&(Ut(n,e,i),ph(n,t,i)))}function mh(e,t,n){var i=Mt();fl(e,t,n,i)}function fl(e,t,n,i){var s={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(zr(e))gh(t,s);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,p=c(h,n);if(s.hasEagerState=!0,s.eagerState=p,Tt(p,h))return Sr(e,t,s,0),Ue===null&&br(),!1}catch{}finally{}if(n=To(e,t,s,i),n!==null)return Ut(n,e,i),ph(n,t,i),!0}return!1}function ou(e,t,n,i){if(i={lane:2,revertLane:Hu(),action:i,hasEagerState:!1,eagerState:null,next:null},zr(e)){if(t)throw Error(o(479))}else t=To(e,n,i,2),t!==null&&Ut(t,e,2)}function zr(e){var t=e.alternate;return e===de||t!==null&&t===de}function gh(e,t){ai=jr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ph(e,t,n){if((n&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,n|=i,t.lanes=n,wf(e,n)}}var qr={readContext:ft,use:Ur,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useLayoutEffect:Ye,useInsertionEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useSyncExternalStore:Ye,useId:Ye,useHostTransitionStatus:Ye,useFormState:Ye,useActionState:Ye,useOptimistic:Ye,useMemoCache:Ye,useCacheRefresh:Ye},vh={readContext:ft,use:Ur,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:eh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Nr(4194308,4,ih.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Nr(4194308,4,e,t)},useInsertionEffect:function(e,t){Nr(4,2,e,t)},useMemo:function(e,t){var n=xt();t=t===void 0?null:t;var i=e();if(Ta){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=xt();if(n!==void 0){var s=n(t);if(Ta){an(!0);try{n(t)}finally{an(!1)}}}else s=t;return i.memoizedState=i.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},i.queue=e,e=e.dispatch=Iy.bind(null,de,e),[i.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:function(e){e=nu(e);var t=e.queue,n=mh.bind(null,de,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:iu,useDeferredValue:function(e,t){var n=xt();return lu(n,e,t)},useTransition:function(){var e=nu(!1);return e=uh.bind(null,de,e.queue,!0,!1),xt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=de,s=xt();if(_e){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),Ue===null)throw Error(o(349));(ye&124)!==0||qd(i,t,n)}s.memoizedState=n;var c={value:n,getSnapshot:t};return s.queue=c,eh(Hd.bind(null,i,c,e),[e]),i.flags|=2048,li(9,Lr(),Bd.bind(null,i,c,n,t),null),n},useId:function(){var e=xt(),t=Ue.identifierPrefix;if(_e){var n=bn,i=yn;n=(i&~(1<<32-lt(i)-1)).toString(32)+n,t="«"+t+"R"+n,n=Mr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Hy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:su,useFormState:Wd,useActionState:Wd,useOptimistic:function(e){var t=xt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=ou.bind(null,de,!0,n),n.dispatch=t,[e,t]},useMemoCache:Jo,useCacheRefresh:function(){return xt().memoizedState=$y.bind(null,de)}},yh={readContext:ft,use:Ur,useCallback:rh,useContext:ft,useEffect:th,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ah,useMemo:sh,useReducer:Dr,useRef:Jd,useState:function(){return Dr(En)},useDebugValue:iu,useDeferredValue:function(e,t){var n=Xe();return oh(n,Te.memoizedState,e,t)},useTransition:function(){var e=Dr(En)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:ul(e),t]},useSyncExternalStore:zd,useId:dh,useHostTransitionStatus:su,useFormState:Kd,useActionState:Kd,useOptimistic:function(e,t){var n=Xe();return Pd(n,Te,e,t)},useMemoCache:Jo,useCacheRefresh:hh},Gy={readContext:ft,use:Ur,useCallback:rh,useContext:ft,useEffect:th,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ah,useMemo:sh,useReducer:tu,useRef:Jd,useState:function(){return tu(En)},useDebugValue:iu,useDeferredValue:function(e,t){var n=Xe();return Te===null?lu(n,e,t):oh(n,Te.memoizedState,e,t)},useTransition:function(){var e=tu(En)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:ul(e),t]},useSyncExternalStore:zd,useId:dh,useHostTransitionStatus:su,useFormState:Fd,useActionState:Fd,useOptimistic:function(e,t){var n=Xe();return Te!==null?Pd(n,Te,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Jo,useCacheRefresh:hh},ri=null,dl=0;function Br(e){var t=dl;return dl+=1,ri===null&&(ri=[]),Rd(ri,e,t)}function hl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Hr(e,t){throw t.$$typeof===O?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function bh(e){var t=e._init;return t(e._payload)}function Sh(e){function t(T,E){if(e){var k=T.deletions;k===null?(T.deletions=[E],T.flags|=16):k.push(E)}}function n(T,E){if(!e)return null;for(;E!==null;)t(T,E),E=E.sibling;return null}function i(T){for(var E=new Map;T!==null;)T.key!==null?E.set(T.key,T):E.set(T.index,T),T=T.sibling;return E}function s(T,E){return T=vn(T,E),T.index=0,T.sibling=null,T}function c(T,E,k){return T.index=k,e?(k=T.alternate,k!==null?(k=k.index,k<E?(T.flags|=67108866,E):k):(T.flags|=67108866,E)):(T.flags|=1048576,E)}function h(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function p(T,E,k,B){return E===null||E.tag!==6?(E=Ro(k,T.mode,B),E.return=T,E):(E=s(E,k),E.return=T,E)}function b(T,E,k,B){var K=k.type;return K===A?z(T,E,k.props.children,B,k.key):E!==null&&(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===W&&bh(K)===E.type)?(E=s(E,k.props),hl(E,k),E.return=T,E):(E=Er(k.type,k.key,k.props,null,T.mode,B),hl(E,k),E.return=T,E)}function j(T,E,k,B){return E===null||E.tag!==4||E.stateNode.containerInfo!==k.containerInfo||E.stateNode.implementation!==k.implementation?(E=ko(k,T.mode,B),E.return=T,E):(E=s(E,k.children||[]),E.return=T,E)}function z(T,E,k,B,K){return E===null||E.tag!==7?(E=ba(k,T.mode,B,K),E.return=T,E):(E=s(E,k),E.return=T,E)}function V(T,E,k){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Ro(""+E,T.mode,k),E.return=T,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case S:return k=Er(E.type,E.key,E.props,null,T.mode,k),hl(k,E),k.return=T,k;case R:return E=ko(E,T.mode,k),E.return=T,E;case W:var B=E._init;return E=B(E._payload),V(T,E,k)}if(F(E)||ve(E))return E=ba(E,T.mode,k,null),E.return=T,E;if(typeof E.then=="function")return V(T,Br(E),k);if(E.$$typeof===$)return V(T,Cr(T,E),k);Hr(T,E)}return null}function U(T,E,k,B){var K=E!==null?E.key:null;if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return K!==null?null:p(T,E,""+k,B);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case S:return k.key===K?b(T,E,k,B):null;case R:return k.key===K?j(T,E,k,B):null;case W:return K=k._init,k=K(k._payload),U(T,E,k,B)}if(F(k)||ve(k))return K!==null?null:z(T,E,k,B,null);if(typeof k.then=="function")return U(T,E,Br(k),B);if(k.$$typeof===$)return U(T,E,Cr(T,k),B);Hr(T,k)}return null}function D(T,E,k,B,K){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return T=T.get(k)||null,p(E,T,""+B,K);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case S:return T=T.get(B.key===null?k:B.key)||null,b(E,T,B,K);case R:return T=T.get(B.key===null?k:B.key)||null,j(E,T,B,K);case W:var me=B._init;return B=me(B._payload),D(T,E,k,B,K)}if(F(B)||ve(B))return T=T.get(k)||null,z(E,T,B,K,null);if(typeof B.then=="function")return D(T,E,k,Br(B),K);if(B.$$typeof===$)return D(T,E,k,Cr(E,B),K);Hr(E,B)}return null}function se(T,E,k,B){for(var K=null,me=null,te=E,ie=E=0,at=null;te!==null&&ie<k.length;ie++){te.index>ie?(at=te,te=null):at=te.sibling;var Se=U(T,te,k[ie],B);if(Se===null){te===null&&(te=at);break}e&&te&&Se.alternate===null&&t(T,te),E=c(Se,E,ie),me===null?K=Se:me.sibling=Se,me=Se,te=at}if(ie===k.length)return n(T,te),_e&&_a(T,ie),K;if(te===null){for(;ie<k.length;ie++)te=V(T,k[ie],B),te!==null&&(E=c(te,E,ie),me===null?K=te:me.sibling=te,me=te);return _e&&_a(T,ie),K}for(te=i(te);ie<k.length;ie++)at=D(te,T,ie,k[ie],B),at!==null&&(e&&at.alternate!==null&&te.delete(at.key===null?ie:at.key),E=c(at,E,ie),me===null?K=at:me.sibling=at,me=at);return e&&te.forEach(function(ia){return t(T,ia)}),_e&&_a(T,ie),K}function ae(T,E,k,B){if(k==null)throw Error(o(151));for(var K=null,me=null,te=E,ie=E=0,at=null,Se=k.next();te!==null&&!Se.done;ie++,Se=k.next()){te.index>ie?(at=te,te=null):at=te.sibling;var ia=U(T,te,Se.value,B);if(ia===null){te===null&&(te=at);break}e&&te&&ia.alternate===null&&t(T,te),E=c(ia,E,ie),me===null?K=ia:me.sibling=ia,me=ia,te=at}if(Se.done)return n(T,te),_e&&_a(T,ie),K;if(te===null){for(;!Se.done;ie++,Se=k.next())Se=V(T,Se.value,B),Se!==null&&(E=c(Se,E,ie),me===null?K=Se:me.sibling=Se,me=Se);return _e&&_a(T,ie),K}for(te=i(te);!Se.done;ie++,Se=k.next())Se=D(te,T,ie,Se.value,B),Se!==null&&(e&&Se.alternate!==null&&te.delete(Se.key===null?ie:Se.key),E=c(Se,E,ie),me===null?K=Se:me.sibling=Se,me=Se);return e&&te.forEach(function(Xb){return t(T,Xb)}),_e&&_a(T,ie),K}function Re(T,E,k,B){if(typeof k=="object"&&k!==null&&k.type===A&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case S:e:{for(var K=k.key;E!==null;){if(E.key===K){if(K=k.type,K===A){if(E.tag===7){n(T,E.sibling),B=s(E,k.props.children),B.return=T,T=B;break e}}else if(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===W&&bh(K)===E.type){n(T,E.sibling),B=s(E,k.props),hl(B,k),B.return=T,T=B;break e}n(T,E);break}else t(T,E);E=E.sibling}k.type===A?(B=ba(k.props.children,T.mode,B,k.key),B.return=T,T=B):(B=Er(k.type,k.key,k.props,null,T.mode,B),hl(B,k),B.return=T,T=B)}return h(T);case R:e:{for(K=k.key;E!==null;){if(E.key===K)if(E.tag===4&&E.stateNode.containerInfo===k.containerInfo&&E.stateNode.implementation===k.implementation){n(T,E.sibling),B=s(E,k.children||[]),B.return=T,T=B;break e}else{n(T,E);break}else t(T,E);E=E.sibling}B=ko(k,T.mode,B),B.return=T,T=B}return h(T);case W:return K=k._init,k=K(k._payload),Re(T,E,k,B)}if(F(k))return se(T,E,k,B);if(ve(k)){if(K=ve(k),typeof K!="function")throw Error(o(150));return k=K.call(k),ae(T,E,k,B)}if(typeof k.then=="function")return Re(T,E,Br(k),B);if(k.$$typeof===$)return Re(T,E,Cr(T,k),B);Hr(T,k)}return typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint"?(k=""+k,E!==null&&E.tag===6?(n(T,E.sibling),B=s(E,k),B.return=T,T=B):(n(T,E),B=Ro(k,T.mode,B),B.return=T,T=B),h(T)):n(T,E)}return function(T,E,k,B){try{dl=0;var K=Re(T,E,k,B);return ri=null,K}catch(te){if(te===al||te===Or)throw te;var me=Ot(29,te,null,T.mode);return me.lanes=B,me.return=T,me}finally{}}}var si=Sh(!0),_h=Sh(!1),Qt=Q(null),rn=null;function Pn(e){var t=e.alternate;G(Ke,Ke.current&1),G(Qt,e),rn===null&&(t===null||ni.current!==null||t.memoizedState!==null)&&(rn=e)}function Eh(e){if(e.tag===22){if(G(Ke,Ke.current),G(Qt,e),rn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(rn=e)}}else Yn()}function Yn(){G(Ke,Ke.current),G(Qt,Qt.current)}function xn(e){I(Qt),rn===e&&(rn=null),I(Ke)}var Ke=Q(0);function Vr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Fu(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function uu(e,t,n,i){t=e.memoizedState,n=n(i,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var cu={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=Mt(),s=Hn(i);s.payload=t,n!=null&&(s.callback=n),t=Vn(e,s,i),t!==null&&(Ut(t,e,i),ll(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=Mt(),s=Hn(i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Vn(e,s,i),t!==null&&(Ut(t,e,i),ll(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Mt(),i=Hn(n);i.tag=2,t!=null&&(i.callback=t),t=Vn(e,i,n),t!==null&&(Ut(t,e,n),ll(t,e,n))}};function xh(e,t,n,i,s,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,h):t.prototype&&t.prototype.isPureReactComponent?!Wi(n,i)||!Wi(s,c):!0}function wh(e,t,n,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&cu.enqueueReplaceState(t,t.state,null)}function Oa(e,t){var n=t;if("ref"in t){n={};for(var i in t)i!=="ref"&&(n[i]=t[i])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var s in e)n[s]===void 0&&(n[s]=e[s])}return n}var Qr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ah(e){Qr(e)}function Ch(e){console.error(e)}function Th(e){Qr(e)}function Pr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function Oh(e,t,n){try{var i=e.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function fu(e,t,n){return n=Hn(n),n.tag=3,n.payload={element:null},n.callback=function(){Pr(e,t)},n}function Rh(e){return e=Hn(e),e.tag=3,e}function kh(e,t,n,i){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var c=i.value;e.payload=function(){return s(c)},e.callback=function(){Oh(t,n,i)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Oh(t,n,i),typeof s!="function"&&(Kn===null?Kn=new Set([this]):Kn.add(this));var p=i.stack;this.componentDidCatch(i.value,{componentStack:p!==null?p:""})})}function Xy(e,t,n,i,s){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=n.alternate,t!==null&&el(t,n,s,!0),n=Qt.current,n!==null){switch(n.tag){case 13:return rn===null?Lu():n.alternate===null&&Qe===0&&(Qe=3),n.flags&=-257,n.flags|=65536,n.lanes=s,i===Ho?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([i]):t.add(i),zu(e,i,s)),!1;case 22:return n.flags|=65536,i===Ho?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([i]):n.add(i)),zu(e,i,s)),!1}throw Error(o(435,n.tag))}return zu(e,i,s),Lu(),!1}if(_e)return t=Qt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,i!==Uo&&(e=Error(o(422),{cause:i}),Ji(qt(e,n)))):(i!==Uo&&(t=Error(o(423),{cause:i}),Ji(qt(t,n))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,i=qt(i,n),s=fu(e.stateNode,i,s),Po(e,s),Qe!==4&&(Qe=2)),!1;var c=Error(o(520),{cause:i});if(c=qt(c,n),Sl===null?Sl=[c]:Sl.push(c),Qe!==4&&(Qe=2),t===null)return!0;i=qt(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=s&-s,n.lanes|=e,e=fu(n.stateNode,i,e),Po(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Kn===null||!Kn.has(c))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Rh(s),kh(s,e,n,i),Po(n,s),!1}n=n.return}while(n!==null);return!1}var jh=Error(o(461)),tt=!1;function rt(e,t,n,i){t.child=e===null?_h(t,null,n,i):si(t,e.child,n,i)}function Mh(e,t,n,i,s){n=n.render;var c=t.ref;if("ref"in i){var h={};for(var p in i)p!=="ref"&&(h[p]=i[p])}else h=i;return Aa(t),i=Xo(e,t,n,h,c,s),p=Wo(),e!==null&&!tt?(Ko(e,t,s),wn(e,t,s)):(_e&&p&&jo(t),t.flags|=1,rt(e,t,i,s),t.child)}function Uh(e,t,n,i,s){if(e===null){var c=n.type;return typeof c=="function"&&!Oo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,Dh(e,t,c,i,s)):(e=Er(n.type,null,i,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!bu(e,s)){var h=c.memoizedProps;if(n=n.compare,n=n!==null?n:Wi,n(h,i)&&e.ref===t.ref)return wn(e,t,s)}return t.flags|=1,e=vn(c,i),e.ref=t.ref,e.return=t,t.child=e}function Dh(e,t,n,i,s){if(e!==null){var c=e.memoizedProps;if(Wi(c,i)&&e.ref===t.ref)if(tt=!1,t.pendingProps=i=c,bu(e,s))(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,wn(e,t,s)}return du(e,t,n,i,s)}function Lh(e,t,n){var i=t.pendingProps,s=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|n:n,e!==null){for(s=t.child=e.child,c=0;s!==null;)c=c|s.lanes|s.childLanes,s=s.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return Nh(e,t,i,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Tr(t,c!==null?c.cachePool:null),c!==null?Dd(t,c):$o(),Eh(t);else return t.lanes=t.childLanes=536870912,Nh(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Tr(t,c.cachePool),Dd(t,c),Yn(),t.memoizedState=null):(e!==null&&Tr(t,null),$o(),Yn());return rt(e,t,s,n),t.child}function Nh(e,t,n,i){var s=Bo();return s=s===null?null:{parent:We._currentValue,pool:s},t.memoizedState={baseLanes:n,cachePool:s},e!==null&&Tr(t,null),$o(),Eh(t),e!==null&&el(e,t,i,!0),null}function Yr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function du(e,t,n,i,s){return Aa(t),n=Xo(e,t,n,i,void 0,s),i=Wo(),e!==null&&!tt?(Ko(e,t,s),wn(e,t,s)):(_e&&i&&jo(t),t.flags|=1,rt(e,t,n,s),t.child)}function zh(e,t,n,i,s,c){return Aa(t),t.updateQueue=null,n=Nd(t,i,n,s),Ld(e),i=Wo(),e!==null&&!tt?(Ko(e,t,c),wn(e,t,c)):(_e&&i&&jo(t),t.flags|=1,rt(e,t,n,c),t.child)}function qh(e,t,n,i,s){if(Aa(t),t.stateNode===null){var c=Za,h=n.contextType;typeof h=="object"&&h!==null&&(c=ft(h)),c=new n(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=cu,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},Vo(t),h=n.contextType,c.context=typeof h=="object"&&h!==null?ft(h):Za,c.state=t.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(uu(t,n,h,i),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(h=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),h!==c.state&&cu.enqueueReplaceState(c,c.state,null),sl(t,i,c,s),rl(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var p=t.memoizedProps,b=Oa(n,p);c.props=b;var j=c.context,z=n.contextType;h=Za,typeof z=="object"&&z!==null&&(h=ft(z));var V=n.getDerivedStateFromProps;z=typeof V=="function"||typeof c.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,z||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(p||j!==h)&&wh(t,c,i,h),Bn=!1;var U=t.memoizedState;c.state=U,sl(t,i,c,s),rl(),j=t.memoizedState,p||U!==j||Bn?(typeof V=="function"&&(uu(t,n,V,i),j=t.memoizedState),(b=Bn||xh(t,n,b,i,U,j,h))?(z||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=j),c.props=i,c.state=j,c.context=h,i=b):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,Qo(e,t),h=t.memoizedProps,z=Oa(n,h),c.props=z,V=t.pendingProps,U=c.context,j=n.contextType,b=Za,typeof j=="object"&&j!==null&&(b=ft(j)),p=n.getDerivedStateFromProps,(j=typeof p=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h!==V||U!==b)&&wh(t,c,i,b),Bn=!1,U=t.memoizedState,c.state=U,sl(t,i,c,s),rl();var D=t.memoizedState;h!==V||U!==D||Bn||e!==null&&e.dependencies!==null&&Ar(e.dependencies)?(typeof p=="function"&&(uu(t,n,p,i),D=t.memoizedState),(z=Bn||xh(t,n,z,i,U,D,b)||e!==null&&e.dependencies!==null&&Ar(e.dependencies))?(j||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,D,b),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,D,b)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=D),c.props=i,c.state=D,c.context=b,i=z):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,Yr(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=si(t,e.child,null,s),t.child=si(t,null,n,s)):rt(e,t,n,s),t.memoizedState=c.state,e=t.child):e=wn(e,t,s),e}function Bh(e,t,n,i){return Fi(),t.flags|=256,rt(e,t,n,i),t.child}var hu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function mu(e){return{baseLanes:e,cachePool:Cd()}}function gu(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Pt),e}function Hh(e,t,n){var i=t.pendingProps,s=!1,c=(t.flags&128)!==0,h;if((h=c)||(h=e!==null&&e.memoizedState===null?!1:(Ke.current&2)!==0),h&&(s=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(s?Pn(t):Yn(),_e){var p=Ve,b;if(b=p){e:{for(b=p,p=ln;b.nodeType!==8;){if(!p){p=null;break e}if(b=Zt(b.nextSibling),b===null){p=null;break e}}p=b}p!==null?(t.memoizedState={dehydrated:p,treeContext:Sa!==null?{id:yn,overflow:bn}:null,retryLane:536870912,hydrationErrors:null},b=Ot(18,null,null,0),b.stateNode=p,b.return=t,t.child=b,pt=t,Ve=null,b=!0):b=!1}b||xa(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return Fu(p)?t.lanes=32:t.lanes=536870912,null;xn(t)}return p=i.children,i=i.fallback,s?(Yn(),s=t.mode,p=$r({mode:"hidden",children:p},s),i=ba(i,s,n,null),p.return=t,i.return=t,p.sibling=i,t.child=p,s=t.child,s.memoizedState=mu(n),s.childLanes=gu(e,h,n),t.memoizedState=hu,i):(Pn(t),pu(t,p))}if(b=e.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(c)t.flags&256?(Pn(t),t.flags&=-257,t=vu(e,t,n)):t.memoizedState!==null?(Yn(),t.child=e.child,t.flags|=128,t=null):(Yn(),s=i.fallback,p=t.mode,i=$r({mode:"visible",children:i.children},p),s=ba(s,p,n,null),s.flags|=2,i.return=t,s.return=t,i.sibling=s,t.child=i,si(t,e.child,null,n),i=t.child,i.memoizedState=mu(n),i.childLanes=gu(e,h,n),t.memoizedState=hu,t=s);else if(Pn(t),Fu(p)){if(h=p.nextSibling&&p.nextSibling.dataset,h)var j=h.dgst;h=j,i=Error(o(419)),i.stack="",i.digest=h,Ji({value:i,source:null,stack:null}),t=vu(e,t,n)}else if(tt||el(e,t,n,!1),h=(n&e.childLanes)!==0,tt||h){if(h=Ue,h!==null&&(i=n&-n,i=(i&42)!==0?1:Fs(i),i=(i&(h.suspendedLanes|n))!==0?0:i,i!==0&&i!==b.retryLane))throw b.retryLane=i,Ka(e,i),Ut(h,e,i),jh;p.data==="$?"||Lu(),t=vu(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Ve=Zt(p.nextSibling),pt=t,_e=!0,Ea=null,ln=!1,e!==null&&(Ht[Vt++]=yn,Ht[Vt++]=bn,Ht[Vt++]=Sa,yn=e.id,bn=e.overflow,Sa=t),t=pu(t,i.children),t.flags|=4096);return t}return s?(Yn(),s=i.fallback,p=t.mode,b=e.child,j=b.sibling,i=vn(b,{mode:"hidden",children:i.children}),i.subtreeFlags=b.subtreeFlags&65011712,j!==null?s=vn(j,s):(s=ba(s,p,n,null),s.flags|=2),s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,p=e.child.memoizedState,p===null?p=mu(n):(b=p.cachePool,b!==null?(j=We._currentValue,b=b.parent!==j?{parent:j,pool:j}:b):b=Cd(),p={baseLanes:p.baseLanes|n,cachePool:b}),s.memoizedState=p,s.childLanes=gu(e,h,n),t.memoizedState=hu,i):(Pn(t),n=e.child,e=n.sibling,n=vn(n,{mode:"visible",children:i.children}),n.return=t,n.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=n,t.memoizedState=null,n)}function pu(e,t){return t=$r({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function $r(e,t){return e=Ot(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function vu(e,t,n){return si(t,e.child,null,n),e=pu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Vh(e,t,n){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Lo(e.return,t,n)}function yu(e,t,n,i,s){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=n,c.tailMode=s)}function Qh(e,t,n){var i=t.pendingProps,s=i.revealOrder,c=i.tail;if(rt(e,t,i.children,n),i=Ke.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vh(e,n,t);else if(e.tag===19)Vh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(G(Ke,i),s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Vr(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),yu(t,!1,s,n,c);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Vr(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}yu(t,!0,n,null,c);break;case"together":yu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function wn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Wn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(el(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=vn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=vn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Ar(e)))}function Wy(e,t,n){switch(t.tag){case 3:Me(t,t.stateNode.containerInfo),qn(t,We,e.memoizedState.cache),Fi();break;case 27:case 5:mn(t);break;case 4:Me(t,t.stateNode.containerInfo);break;case 10:qn(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(Pn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Hh(e,t,n):(Pn(t),e=wn(e,t,n),e!==null?e.sibling:null);Pn(t);break;case 19:var s=(e.flags&128)!==0;if(i=(n&t.childLanes)!==0,i||(el(e,t,n,!1),i=(n&t.childLanes)!==0),s){if(i)return Qh(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),G(Ke,Ke.current),i)break;return null;case 22:case 23:return t.lanes=0,Lh(e,t,n);case 24:qn(t,We,e.memoizedState.cache)}return wn(e,t,n)}function Ph(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)tt=!0;else{if(!bu(e,n)&&(t.flags&128)===0)return tt=!1,Wy(e,t,n);tt=(e.flags&131072)!==0}else tt=!1,_e&&(t.flags&1048576)!==0&&bd(t,wr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,s=i._init;if(i=s(i._payload),t.type=i,typeof i=="function")Oo(i)?(e=Oa(i,e),t.tag=1,t=qh(null,t,i,e,n)):(t.tag=0,t=du(null,t,i,e,n));else{if(i!=null){if(s=i.$$typeof,s===J){t.tag=11,t=Mh(null,t,i,e,n);break e}else if(s===le){t.tag=14,t=Uh(null,t,i,e,n);break e}}throw t=De(i)||i,Error(o(306,t,""))}}return t;case 0:return du(e,t,t.type,t.pendingProps,n);case 1:return i=t.type,s=Oa(i,t.pendingProps),qh(e,t,i,s,n);case 3:e:{if(Me(t,t.stateNode.containerInfo),e===null)throw Error(o(387));i=t.pendingProps;var c=t.memoizedState;s=c.element,Qo(e,t),sl(t,i,null,n);var h=t.memoizedState;if(i=h.cache,qn(t,We,i),i!==c.cache&&No(t,[We],n,!0),rl(),i=h.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Bh(e,t,i,n);break e}else if(i!==s){s=qt(Error(o(424)),t),Ji(s),t=Bh(e,t,i,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ve=Zt(e.firstChild),pt=t,_e=!0,Ea=null,ln=!0,n=_h(t,null,i,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Fi(),i===s){t=wn(e,t,n);break e}rt(e,t,i,n)}t=t.child}return t;case 26:return Yr(e,t),e===null?(n=Gm(t.type,null,t.pendingProps,null))?t.memoizedState=n:_e||(n=t.type,e=t.pendingProps,i=ls(re.current).createElement(n),i[ct]=t,i[_t]=e,ot(i,n,e),et(i),t.stateNode=i):t.memoizedState=Gm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return mn(t),e===null&&_e&&(i=t.stateNode=Ym(t.type,t.pendingProps,re.current),pt=t,ln=!0,s=Ve,Jn(t.type)?(Ju=s,Ve=Zt(i.firstChild)):Ve=s),rt(e,t,t.pendingProps.children,n),Yr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((s=i=Ve)&&(i=xb(i,t.type,t.pendingProps,ln),i!==null?(t.stateNode=i,pt=t,Ve=Zt(i.firstChild),ln=!1,s=!0):s=!1),s||xa(t)),mn(t),s=t.type,c=t.pendingProps,h=e!==null?e.memoizedProps:null,i=c.children,Wu(s,c)?i=null:h!==null&&Wu(s,h)&&(t.flags|=32),t.memoizedState!==null&&(s=Xo(e,t,Vy,null,null,n),Rl._currentValue=s),Yr(e,t),rt(e,t,i,n),t.child;case 6:return e===null&&_e&&((e=n=Ve)&&(n=wb(n,t.pendingProps,ln),n!==null?(t.stateNode=n,pt=t,Ve=null,e=!0):e=!1),e||xa(t)),null;case 13:return Hh(e,t,n);case 4:return Me(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=si(t,null,i,n):rt(e,t,i,n),t.child;case 11:return Mh(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,qn(t,t.type,i.value),rt(e,t,i.children,n),t.child;case 9:return s=t.type._context,i=t.pendingProps.children,Aa(t),s=ft(s),i=i(s),t.flags|=1,rt(e,t,i,n),t.child;case 14:return Uh(e,t,t.type,t.pendingProps,n);case 15:return Dh(e,t,t.type,t.pendingProps,n);case 19:return Qh(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},e===null?(n=$r(i,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=vn(e.child,i),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Lh(e,t,n);case 24:return Aa(t),i=ft(We),e===null?(s=Bo(),s===null&&(s=Ue,c=zo(),s.pooledCache=c,c.refCount++,c!==null&&(s.pooledCacheLanes|=n),s=c),t.memoizedState={parent:i,cache:s},Vo(t),qn(t,We,s)):((e.lanes&n)!==0&&(Qo(e,t),sl(t,null,null,n),rl()),s=e.memoizedState,c=t.memoizedState,s.parent!==i?(s={parent:i,cache:i},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),qn(t,We,i)):(i=c.cache,qn(t,We,i),i!==s.cache&&No(t,[We],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function An(e){e.flags|=4}function Yh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Fm(t)){if(t=Qt.current,t!==null&&((ye&4194048)===ye?rn!==null:(ye&62914560)!==ye&&(ye&536870912)===0||t!==rn))throw il=Ho,Td;e.flags|=8192}}function Ir(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ef():536870912,e.lanes|=t,fi|=t)}function ml(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,i=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function Ky(e,t,n){var i=t.pendingProps;switch(Mo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return qe(t),null;case 3:return n=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),_n(We),nn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Zi(t)?An(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Ed())),qe(t),null;case 26:return n=t.memoizedState,e===null?(An(t),n!==null?(qe(t),Yh(t,n)):(qe(t),t.flags&=-16777217)):n?n!==e.memoizedState?(An(t),qe(t),Yh(t,n)):(qe(t),t.flags&=-16777217):(e.memoizedProps!==i&&An(t),qe(t),t.flags&=-16777217),null;case 27:ha(t),n=re.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&An(t);else{if(!i){if(t.stateNode===null)throw Error(o(166));return qe(t),null}e=ee.current,Zi(t)?Sd(t):(e=Ym(s,i,n),t.stateNode=e,An(t))}return qe(t),null;case 5:if(ha(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&An(t);else{if(!i){if(t.stateNode===null)throw Error(o(166));return qe(t),null}if(e=ee.current,Zi(t))Sd(t);else{switch(s=ls(re.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?s.createElement(n,{is:i.is}):s.createElement(n)}}e[ct]=t,e[_t]=i;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(ot(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&An(t)}}return qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&An(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(o(166));if(e=re.current,Zi(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,s=pt,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}e[ct]=t,e=!!(e.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||zm(e.nodeValue,n)),e||xa(t)}else e=ls(e).createTextNode(i),e[ct]=t,t.stateNode=e}return qe(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=Zi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!s)throw Error(o(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[ct]=t}else Fi(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;qe(t),s=!1}else s=Ed(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(xn(t),t):(xn(t),null)}if(xn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=i!==null,e=e!==null&&e.memoizedState!==null,n){i=t.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==s&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Ir(t,t.updateQueue),qe(t),null;case 4:return nn(),e===null&&Yu(t.stateNode.containerInfo),qe(t),null;case 10:return _n(t.type),qe(t),null;case 19:if(I(Ke),s=t.memoizedState,s===null)return qe(t),null;if(i=(t.flags&128)!==0,c=s.rendering,c===null)if(i)ml(s,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Vr(e),c!==null){for(t.flags|=128,ml(s,!1),e=c.updateQueue,t.updateQueue=e,Ir(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)yd(n,e),n=n.sibling;return G(Ke,Ke.current&1|2),t.child}e=e.sibling}s.tail!==null&&He()>Wr&&(t.flags|=128,i=!0,ml(s,!1),t.lanes=4194304)}else{if(!i)if(e=Vr(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,Ir(t,e),ml(s,!0),s.tail===null&&s.tailMode==="hidden"&&!c.alternate&&!_e)return qe(t),null}else 2*He()-s.renderingStartTime>Wr&&n!==536870912&&(t.flags|=128,i=!0,ml(s,!1),t.lanes=4194304);s.isBackwards?(c.sibling=t.child,t.child=c):(e=s.last,e!==null?e.sibling=c:t.child=c,s.last=c)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=He(),t.sibling=null,e=Ke.current,G(Ke,i?e&1|2:e&1),t):(qe(t),null);case 22:case 23:return xn(t),Io(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(n&536870912)!==0&&(t.flags&128)===0&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),n=t.updateQueue,n!==null&&Ir(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),e!==null&&I(Ca),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),_n(We),qe(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function Zy(e,t){switch(Mo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return _n(We),nn(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ha(t),null;case 13:if(xn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));Fi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(Ke),null;case 4:return nn(),null;case 10:return _n(t.type),null;case 22:case 23:return xn(t),Io(),e!==null&&I(Ca),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return _n(We),null;case 25:return null;default:return null}}function $h(e,t){switch(Mo(t),t.tag){case 3:_n(We),nn();break;case 26:case 27:case 5:ha(t);break;case 4:nn();break;case 13:xn(t);break;case 19:I(Ke);break;case 10:_n(t.type);break;case 22:case 23:xn(t),Io(),e!==null&&I(Ca);break;case 24:_n(We)}}function gl(e,t){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&e)===e){i=void 0;var c=n.create,h=n.inst;i=c(),h.destroy=i}n=n.next}while(n!==s)}}catch(p){ke(t,t.return,p)}}function $n(e,t,n){try{var i=t.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var c=s.next;i=c;do{if((i.tag&e)===e){var h=i.inst,p=h.destroy;if(p!==void 0){h.destroy=void 0,s=t;var b=n,j=p;try{j()}catch(z){ke(s,b,z)}}}i=i.next}while(i!==c)}}catch(z){ke(t,t.return,z)}}function Ih(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Ud(t,n)}catch(i){ke(e,e.return,i)}}}function Gh(e,t,n){n.props=Oa(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){ke(e,t,i)}}function pl(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof n=="function"?e.refCleanup=n(i):n.current=i}}catch(s){ke(e,t,s)}}function sn(e,t){var n=e.ref,i=e.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(s){ke(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){ke(e,t,s)}else n.current=null}function Xh(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(s){ke(e,e.return,s)}}function Su(e,t,n){try{var i=e.stateNode;yb(i,e.type,n,t),i[_t]=t}catch(s){ke(e,e.return,s)}}function Wh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Jn(e.type)||e.tag===4}function _u(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Wh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Jn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Eu(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=is));else if(i!==4&&(i===27&&Jn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Eu(e,t,n),e=e.sibling;e!==null;)Eu(e,t,n),e=e.sibling}function Gr(e,t,n){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(i!==4&&(i===27&&Jn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Gr(e,t,n),e=e.sibling;e!==null;)Gr(e,t,n),e=e.sibling}function Kh(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);ot(t,i,n),t[ct]=e,t[_t]=n}catch(c){ke(e,e.return,c)}}var Cn=!1,$e=!1,xu=!1,Zh=typeof WeakSet=="function"?WeakSet:Set,nt=null;function Fy(e,t){if(e=e.containerInfo,Gu=fs,e=od(e),_o(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var h=0,p=-1,b=-1,j=0,z=0,V=e,U=null;t:for(;;){for(var D;V!==n||s!==0&&V.nodeType!==3||(p=h+s),V!==c||i!==0&&V.nodeType!==3||(b=h+i),V.nodeType===3&&(h+=V.nodeValue.length),(D=V.firstChild)!==null;)U=V,V=D;for(;;){if(V===e)break t;if(U===n&&++j===s&&(p=h),U===c&&++z===i&&(b=h),(D=V.nextSibling)!==null)break;V=U,U=V.parentNode}V=D}n=p===-1||b===-1?null:{start:p,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xu={focusedElem:e,selectionRange:n},fs=!1,nt=t;nt!==null;)if(t=nt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,nt=e;else for(;nt!==null;){switch(t=nt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,s=c.memoizedProps,c=c.memoizedState,i=n.stateNode;try{var se=Oa(n.type,s,n.elementType===n.type);e=i.getSnapshotBeforeUpdate(se,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(ae){ke(n,n.return,ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Zu(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Zu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,nt=e;break}nt=t.return}}function Fh(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:In(e,n),i&4&&gl(5,n);break;case 1:if(In(e,n),i&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(h){ke(n,n.return,h)}else{var s=Oa(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){ke(n,n.return,h)}}i&64&&Ih(n),i&512&&pl(n,n.return);break;case 3:if(In(e,n),i&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Ud(e,t)}catch(h){ke(n,n.return,h)}}break;case 27:t===null&&i&4&&Kh(n);case 26:case 5:In(e,n),t===null&&i&4&&Xh(n),i&512&&pl(n,n.return);break;case 12:In(e,n);break;case 13:In(e,n),i&4&&tm(e,n),i&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=sb.bind(null,n),Ab(e,n))));break;case 22:if(i=n.memoizedState!==null||Cn,!i){t=t!==null&&t.memoizedState!==null||$e,s=Cn;var c=$e;Cn=i,($e=t)&&!c?Gn(e,n,(n.subtreeFlags&8772)!==0):In(e,n),Cn=s,$e=c}break;case 30:break;default:In(e,n)}}function Jh(e){var t=e.alternate;t!==null&&(e.alternate=null,Jh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&to(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Le=null,wt=!1;function Tn(e,t,n){for(n=n.child;n!==null;)em(e,t,n),n=n.sibling}function em(e,t,n){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(gt,n)}catch{}switch(n.tag){case 26:$e||sn(n,t),Tn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:$e||sn(n,t);var i=Le,s=wt;Jn(n.type)&&(Le=n.stateNode,wt=!1),Tn(e,t,n),Al(n.stateNode),Le=i,wt=s;break;case 5:$e||sn(n,t);case 6:if(i=Le,s=wt,Le=null,Tn(e,t,n),Le=i,wt=s,Le!==null)if(wt)try{(Le.nodeType===9?Le.body:Le.nodeName==="HTML"?Le.ownerDocument.body:Le).removeChild(n.stateNode)}catch(c){ke(n,t,c)}else try{Le.removeChild(n.stateNode)}catch(c){ke(n,t,c)}break;case 18:Le!==null&&(wt?(e=Le,Qm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Ul(e)):Qm(Le,n.stateNode));break;case 4:i=Le,s=wt,Le=n.stateNode.containerInfo,wt=!0,Tn(e,t,n),Le=i,wt=s;break;case 0:case 11:case 14:case 15:$e||$n(2,n,t),$e||$n(4,n,t),Tn(e,t,n);break;case 1:$e||(sn(n,t),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Gh(n,t,i)),Tn(e,t,n);break;case 21:Tn(e,t,n);break;case 22:$e=(i=$e)||n.memoizedState!==null,Tn(e,t,n),$e=i;break;default:Tn(e,t,n)}}function tm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ul(e)}catch(n){ke(t,t.return,n)}}function Jy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Zh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Zh),t;default:throw Error(o(435,e.tag))}}function wu(e,t){var n=Jy(e);t.forEach(function(i){var s=ob.bind(null,e,i);n.has(i)||(n.add(i),i.then(s,s))})}function Rt(e,t){var n=t.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i],c=e,h=t,p=h;e:for(;p!==null;){switch(p.tag){case 27:if(Jn(p.type)){Le=p.stateNode,wt=!1;break e}break;case 5:Le=p.stateNode,wt=!1;break e;case 3:case 4:Le=p.stateNode.containerInfo,wt=!0;break e}p=p.return}if(Le===null)throw Error(o(160));em(c,h,s),Le=null,wt=!1,c=s.alternate,c!==null&&(c.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)nm(t,e),t=t.sibling}var Kt=null;function nm(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rt(t,e),kt(e),i&4&&($n(3,e,e.return),gl(3,e),$n(5,e,e.return));break;case 1:Rt(t,e),kt(e),i&512&&($e||n===null||sn(n,n.return)),i&64&&Cn&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var s=Kt;if(Rt(t,e),kt(e),i&512&&($e||n===null||sn(n,n.return)),i&4){var c=n!==null?n.memoizedState:null;if(i=e.memoizedState,n===null)if(i===null)if(e.stateNode===null){e:{i=e.type,n=e.memoizedProps,s=s.ownerDocument||s;t:switch(i){case"title":c=s.getElementsByTagName("title")[0],(!c||c[Hi]||c[ct]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=s.createElement(i),s.head.insertBefore(c,s.querySelector("head > title"))),ot(c,i,n),c[ct]=e,et(c),i=c;break e;case"link":var h=Km("link","href",s).get(i+(n.href||""));if(h){for(var p=0;p<h.length;p++)if(c=h[p],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(p,1);break t}}c=s.createElement(i),ot(c,i,n),s.head.appendChild(c);break;case"meta":if(h=Km("meta","content",s).get(i+(n.content||""))){for(p=0;p<h.length;p++)if(c=h[p],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(p,1);break t}}c=s.createElement(i),ot(c,i,n),s.head.appendChild(c);break;default:throw Error(o(468,i))}c[ct]=e,et(c),i=c}e.stateNode=i}else Zm(s,e.type,e.stateNode);else e.stateNode=Wm(s,i,e.memoizedProps);else c!==i?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,i===null?Zm(s,e.type,e.stateNode):Wm(s,i,e.memoizedProps)):i===null&&e.stateNode!==null&&Su(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rt(t,e),kt(e),i&512&&($e||n===null||sn(n,n.return)),n!==null&&i&4&&Su(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rt(t,e),kt(e),i&512&&($e||n===null||sn(n,n.return)),e.flags&32){s=e.stateNode;try{Pa(s,"")}catch(D){ke(e,e.return,D)}}i&4&&e.stateNode!=null&&(s=e.memoizedProps,Su(e,s,n!==null?n.memoizedProps:s)),i&1024&&(xu=!0);break;case 6:if(Rt(t,e),kt(e),i&4){if(e.stateNode===null)throw Error(o(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(D){ke(e,e.return,D)}}break;case 3:if(os=null,s=Kt,Kt=rs(t.containerInfo),Rt(t,e),Kt=s,kt(e),i&4&&n!==null&&n.memoizedState.isDehydrated)try{Ul(t.containerInfo)}catch(D){ke(e,e.return,D)}xu&&(xu=!1,am(e));break;case 4:i=Kt,Kt=rs(e.stateNode.containerInfo),Rt(t,e),kt(e),Kt=i;break;case 12:Rt(t,e),kt(e);break;case 13:Rt(t,e),kt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(ku=He()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,wu(e,i)));break;case 22:s=e.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,j=Cn,z=$e;if(Cn=j||s,$e=z||b,Rt(t,e),$e=z,Cn=j,kt(e),i&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(n===null||b||Cn||$e||Ra(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){b=n=t;try{if(c=b.stateNode,s)h=c.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{p=b.stateNode;var V=b.memoizedProps.style,U=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(D){ke(b,b.return,D)}}}else if(t.tag===6){if(n===null){b=t;try{b.stateNode.nodeValue=s?"":b.memoizedProps}catch(D){ke(b,b.return,D)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,wu(e,n))));break;case 19:Rt(t,e),kt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,wu(e,i)));break;case 30:break;case 21:break;default:Rt(t,e),kt(e)}}function kt(e){var t=e.flags;if(t&2){try{for(var n,i=e.return;i!==null;){if(Wh(i)){n=i;break}i=i.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var s=n.stateNode,c=_u(e);Gr(e,c,s);break;case 5:var h=n.stateNode;n.flags&32&&(Pa(h,""),n.flags&=-33);var p=_u(e);Gr(e,p,h);break;case 3:case 4:var b=n.stateNode.containerInfo,j=_u(e);Eu(e,j,b);break;default:throw Error(o(161))}}catch(z){ke(e,e.return,z)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function am(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;am(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function In(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Fh(e,t.alternate,t),t=t.sibling}function Ra(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:$n(4,t,t.return),Ra(t);break;case 1:sn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Gh(t,t.return,n),Ra(t);break;case 27:Al(t.stateNode);case 26:case 5:sn(t,t.return),Ra(t);break;case 22:t.memoizedState===null&&Ra(t);break;case 30:Ra(t);break;default:Ra(t)}e=e.sibling}}function Gn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,s=e,c=t,h=c.flags;switch(c.tag){case 0:case 11:case 15:Gn(s,c,n),gl(4,c);break;case 1:if(Gn(s,c,n),i=c,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(j){ke(i,i.return,j)}if(i=c,s=i.updateQueue,s!==null){var p=i.stateNode;try{var b=s.shared.hiddenCallbacks;if(b!==null)for(s.shared.hiddenCallbacks=null,s=0;s<b.length;s++)Md(b[s],p)}catch(j){ke(i,i.return,j)}}n&&h&64&&Ih(c),pl(c,c.return);break;case 27:Kh(c);case 26:case 5:Gn(s,c,n),n&&i===null&&h&4&&Xh(c),pl(c,c.return);break;case 12:Gn(s,c,n);break;case 13:Gn(s,c,n),n&&h&4&&tm(s,c);break;case 22:c.memoizedState===null&&Gn(s,c,n),pl(c,c.return);break;case 30:break;default:Gn(s,c,n)}t=t.sibling}}function Au(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&tl(n))}function Cu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tl(e))}function on(e,t,n,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)im(e,t,n,i),t=t.sibling}function im(e,t,n,i){var s=t.flags;switch(t.tag){case 0:case 11:case 15:on(e,t,n,i),s&2048&&gl(9,t);break;case 1:on(e,t,n,i);break;case 3:on(e,t,n,i),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&tl(e)));break;case 12:if(s&2048){on(e,t,n,i),e=t.stateNode;try{var c=t.memoizedProps,h=c.id,p=c.onPostCommit;typeof p=="function"&&p(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){ke(t,t.return,b)}}else on(e,t,n,i);break;case 13:on(e,t,n,i);break;case 23:break;case 22:c=t.stateNode,h=t.alternate,t.memoizedState!==null?c._visibility&2?on(e,t,n,i):vl(e,t):c._visibility&2?on(e,t,n,i):(c._visibility|=2,oi(e,t,n,i,(t.subtreeFlags&10256)!==0)),s&2048&&Au(h,t);break;case 24:on(e,t,n,i),s&2048&&Cu(t.alternate,t);break;default:on(e,t,n,i)}}function oi(e,t,n,i,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,h=t,p=n,b=i,j=h.flags;switch(h.tag){case 0:case 11:case 15:oi(c,h,p,b,s),gl(8,h);break;case 23:break;case 22:var z=h.stateNode;h.memoizedState!==null?z._visibility&2?oi(c,h,p,b,s):vl(c,h):(z._visibility|=2,oi(c,h,p,b,s)),s&&j&2048&&Au(h.alternate,h);break;case 24:oi(c,h,p,b,s),s&&j&2048&&Cu(h.alternate,h);break;default:oi(c,h,p,b,s)}t=t.sibling}}function vl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,i=t,s=i.flags;switch(i.tag){case 22:vl(n,i),s&2048&&Au(i.alternate,i);break;case 24:vl(n,i),s&2048&&Cu(i.alternate,i);break;default:vl(n,i)}t=t.sibling}}var yl=8192;function ui(e){if(e.subtreeFlags&yl)for(e=e.child;e!==null;)lm(e),e=e.sibling}function lm(e){switch(e.tag){case 26:ui(e),e.flags&yl&&e.memoizedState!==null&&qb(Kt,e.memoizedState,e.memoizedProps);break;case 5:ui(e);break;case 3:case 4:var t=Kt;Kt=rs(e.stateNode.containerInfo),ui(e),Kt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=yl,yl=16777216,ui(e),yl=t):ui(e));break;default:ui(e)}}function rm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function bl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];nt=i,om(i,e)}rm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sm(e),e=e.sibling}function sm(e){switch(e.tag){case 0:case 11:case 15:bl(e),e.flags&2048&&$n(9,e,e.return);break;case 3:bl(e);break;case 12:bl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Xr(e)):bl(e);break;default:bl(e)}}function Xr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var i=t[n];nt=i,om(i,e)}rm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:$n(8,t,t.return),Xr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Xr(t));break;default:Xr(t)}e=e.sibling}}function om(e,t){for(;nt!==null;){var n=nt;switch(n.tag){case 0:case 11:case 15:$n(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:tl(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,nt=i;else e:for(n=e;nt!==null;){i=nt;var s=i.sibling,c=i.return;if(Jh(i),i===n){nt=null;break e}if(s!==null){s.return=c,nt=s;break e}nt=c}}}var eb={getCacheForType:function(e){var t=ft(We),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},tb=typeof WeakMap=="function"?WeakMap:Map,we=0,Ue=null,ge=null,ye=0,Ae=0,jt=null,Xn=!1,ci=!1,Tu=!1,On=0,Qe=0,Wn=0,ka=0,Ou=0,Pt=0,fi=0,Sl=null,At=null,Ru=!1,ku=0,Wr=1/0,Kr=null,Kn=null,st=0,Zn=null,di=null,hi=0,ju=0,Mu=null,um=null,_l=0,Uu=null;function Mt(){if((we&2)!==0&&ye!==0)return ye&-ye;if(M.T!==null){var e=ei;return e!==0?e:Hu()}return Af()}function cm(){Pt===0&&(Pt=(ye&536870912)===0||_e?_f():536870912);var e=Qt.current;return e!==null&&(e.flags|=32),Pt}function Ut(e,t,n){(e===Ue&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)&&(mi(e,0),Fn(e,ye,Pt,!1)),Bi(e,n),((we&2)===0||e!==Ue)&&(e===Ue&&((we&2)===0&&(ka|=n),Qe===4&&Fn(e,ye,Pt,!1)),un(e))}function fm(e,t,n){if((we&6)!==0)throw Error(o(327));var i=!n&&(t&124)===0&&(t&e.expiredLanes)===0||qi(e,t),s=i?ib(e,t):Nu(e,t,!0),c=i;do{if(s===0){ci&&!i&&Fn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!nb(n)){s=Nu(e,t,!1),c=!1;continue}if(s===2){if(c=t,e.errorRecoveryDisabledLanes&c)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var p=e;s=Sl;var b=p.current.memoizedState.isDehydrated;if(b&&(mi(p,h).flags|=256),h=Nu(p,h,!1),h!==2){if(Tu&&!b){p.errorRecoveryDisabledLanes|=c,ka|=c,s=4;break e}c=At,At=s,c!==null&&(At===null?At=c:At.push.apply(At,c))}s=h}if(c=!1,s!==2)continue}}if(s===1){mi(e,0),Fn(e,t,0,!0);break}e:{switch(i=e,c=s,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Fn(i,t,Pt,!Xn);break e;case 2:At=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(s=ku+300-He(),10<s)){if(Fn(i,t,Pt,!Xn),sr(i,0,!0)!==0)break e;i.timeoutHandle=Hm(dm.bind(null,i,n,At,Kr,Ru,t,Pt,ka,fi,Xn,c,2,-0,0),s);break e}dm(i,n,At,Kr,Ru,t,Pt,ka,fi,Xn,c,0,-0,0)}}break}while(!0);un(e)}function dm(e,t,n,i,s,c,h,p,b,j,z,V,U,D){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(Ol={stylesheets:null,count:0,unsuspend:zb},lm(t),V=Bb(),V!==null)){e.cancelPendingCommit=V(bm.bind(null,e,t,c,n,i,s,h,p,b,z,1,U,D)),Fn(e,c,h,!j);return}bm(e,t,c,n,i,s,h,p,b)}function nb(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var s=n[i],c=s.getSnapshot;s=s.value;try{if(!Tt(c(),s))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fn(e,t,n,i){t&=~Ou,t&=~ka,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var s=t;0<s;){var c=31-lt(s),h=1<<c;i[c]=-1,s&=~h}n!==0&&xf(e,n,t)}function Zr(){return(we&6)===0?(El(0),!1):!0}function Du(){if(ge!==null){if(Ae===0)var e=ge.return;else e=ge,Sn=wa=null,Zo(e),ri=null,dl=0,e=ge;for(;e!==null;)$h(e.alternate,e),e=e.return;ge=null}}function mi(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,Sb(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Du(),Ue=e,ge=n=vn(e.current,null),ye=t,Ae=0,jt=null,Xn=!1,ci=qi(e,t),Tu=!1,fi=Pt=Ou=ka=Wn=Qe=0,At=Sl=null,Ru=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var s=31-lt(i),c=1<<s;t|=e[s],i&=~c}return On=t,br(),n}function hm(e,t){de=null,M.H=qr,t===al||t===Or?(t=kd(),Ae=3):t===Td?(t=kd(),Ae=4):Ae=t===jh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,jt=t,ge===null&&(Qe=1,Pr(e,qt(t,e.current)))}function mm(){var e=M.H;return M.H=qr,e===null?qr:e}function gm(){var e=M.A;return M.A=eb,e}function Lu(){Qe=4,Xn||(ye&4194048)!==ye&&Qt.current!==null||(ci=!0),(Wn&134217727)===0&&(ka&134217727)===0||Ue===null||Fn(Ue,ye,Pt,!1)}function Nu(e,t,n){var i=we;we|=2;var s=mm(),c=gm();(Ue!==e||ye!==t)&&(Kr=null,mi(e,t)),t=!1;var h=Qe;e:do try{if(Ae!==0&&ge!==null){var p=ge,b=jt;switch(Ae){case 8:Du(),h=6;break e;case 3:case 2:case 9:case 6:Qt.current===null&&(t=!0);var j=Ae;if(Ae=0,jt=null,gi(e,p,b,j),n&&ci){h=0;break e}break;default:j=Ae,Ae=0,jt=null,gi(e,p,b,j)}}ab(),h=Qe;break}catch(z){hm(e,z)}while(!0);return t&&e.shellSuspendCounter++,Sn=wa=null,we=i,M.H=s,M.A=c,ge===null&&(Ue=null,ye=0,br()),h}function ab(){for(;ge!==null;)pm(ge)}function ib(e,t){var n=we;we|=2;var i=mm(),s=gm();Ue!==e||ye!==t?(Kr=null,Wr=He()+500,mi(e,t)):ci=qi(e,t);e:do try{if(Ae!==0&&ge!==null){t=ge;var c=jt;t:switch(Ae){case 1:Ae=0,jt=null,gi(e,t,c,1);break;case 2:case 9:if(Od(c)){Ae=0,jt=null,vm(t);break}t=function(){Ae!==2&&Ae!==9||Ue!==e||(Ae=7),un(e)},c.then(t,t);break e;case 3:Ae=7;break e;case 4:Ae=5;break e;case 7:Od(c)?(Ae=0,jt=null,vm(t)):(Ae=0,jt=null,gi(e,t,c,7));break;case 5:var h=null;switch(ge.tag){case 26:h=ge.memoizedState;case 5:case 27:var p=ge;if(!h||Fm(h)){Ae=0,jt=null;var b=p.sibling;if(b!==null)ge=b;else{var j=p.return;j!==null?(ge=j,Fr(j)):ge=null}break t}}Ae=0,jt=null,gi(e,t,c,5);break;case 6:Ae=0,jt=null,gi(e,t,c,6);break;case 8:Du(),Qe=6;break e;default:throw Error(o(462))}}lb();break}catch(z){hm(e,z)}while(!0);return Sn=wa=null,M.H=i,M.A=s,we=n,ge!==null?0:(Ue=null,ye=0,br(),Qe)}function lb(){for(;ge!==null&&!Fe();)pm(ge)}function pm(e){var t=Ph(e.alternate,e,On);e.memoizedProps=e.pendingProps,t===null?Fr(e):ge=t}function vm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zh(n,t,t.pendingProps,t.type,void 0,ye);break;case 11:t=zh(n,t,t.pendingProps,t.type.render,t.ref,ye);break;case 5:Zo(t);default:$h(n,t),t=ge=yd(t,On),t=Ph(n,t,On)}e.memoizedProps=e.pendingProps,t===null?Fr(e):ge=t}function gi(e,t,n,i){Sn=wa=null,Zo(t),ri=null,dl=0;var s=t.return;try{if(Xy(e,s,t,n,ye)){Qe=1,Pr(e,qt(n,e.current)),ge=null;return}}catch(c){if(s!==null)throw ge=s,c;Qe=1,Pr(e,qt(n,e.current)),ge=null;return}t.flags&32768?(_e||i===1?e=!0:ci||(ye&536870912)!==0?e=!1:(Xn=e=!0,(i===2||i===9||i===3||i===6)&&(i=Qt.current,i!==null&&i.tag===13&&(i.flags|=16384))),ym(t,e)):Fr(t)}function Fr(e){var t=e;do{if((t.flags&32768)!==0){ym(t,Xn);return}e=t.return;var n=Ky(t.alternate,t,On);if(n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);Qe===0&&(Qe=5)}function ym(e,t){do{var n=Zy(e.alternate,e);if(n!==null){n.flags&=32767,ge=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){ge=e;return}ge=e=n}while(e!==null);Qe=6,ge=null}function bm(e,t,n,i,s,c,h,p,b){e.cancelPendingCommit=null;do Jr();while(st!==0);if((we&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=Co,zv(e,n,c,h,p,b),e===Ue&&(ge=Ue=null,ye=0),di=t,Zn=e,hi=n,ju=c,Mu=s,um=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ub(bt,function(){return wm(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=M.T,M.T=null,s=P.p,P.p=2,h=we,we|=4;try{Fy(e,t,n)}finally{we=h,P.p=s,M.T=i}}st=1,Sm(),_m(),Em()}}function Sm(){if(st===1){st=0;var e=Zn,t=di,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=M.T,M.T=null;var i=P.p;P.p=2;var s=we;we|=4;try{nm(t,e);var c=Xu,h=od(e.containerInfo),p=c.focusedElem,b=c.selectionRange;if(h!==p&&p&&p.ownerDocument&&sd(p.ownerDocument.documentElement,p)){if(b!==null&&_o(p)){var j=b.start,z=b.end;if(z===void 0&&(z=j),"selectionStart"in p)p.selectionStart=j,p.selectionEnd=Math.min(z,p.value.length);else{var V=p.ownerDocument||document,U=V&&V.defaultView||window;if(U.getSelection){var D=U.getSelection(),se=p.textContent.length,ae=Math.min(b.start,se),Re=b.end===void 0?ae:Math.min(b.end,se);!D.extend&&ae>Re&&(h=Re,Re=ae,ae=h);var T=rd(p,ae),E=rd(p,Re);if(T&&E&&(D.rangeCount!==1||D.anchorNode!==T.node||D.anchorOffset!==T.offset||D.focusNode!==E.node||D.focusOffset!==E.offset)){var k=V.createRange();k.setStart(T.node,T.offset),D.removeAllRanges(),ae>Re?(D.addRange(k),D.extend(E.node,E.offset)):(k.setEnd(E.node,E.offset),D.addRange(k))}}}}for(V=[],D=p;D=D.parentNode;)D.nodeType===1&&V.push({element:D,left:D.scrollLeft,top:D.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var B=V[p];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}fs=!!Gu,Xu=Gu=null}finally{we=s,P.p=i,M.T=n}}e.current=t,st=2}}function _m(){if(st===2){st=0;var e=Zn,t=di,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=M.T,M.T=null;var i=P.p;P.p=2;var s=we;we|=4;try{Fh(e,t.alternate,t)}finally{we=s,P.p=i,M.T=n}}st=3}}function Em(){if(st===4||st===3){st=0,ze();var e=Zn,t=di,n=hi,i=um;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?st=5:(st=0,di=Zn=null,xm(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Kn=null),Js(n),t=t.stateNode,it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(gt,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=M.T,s=P.p,P.p=2,M.T=null;try{for(var c=e.onRecoverableError,h=0;h<i.length;h++){var p=i[h];c(p.value,{componentStack:p.stack})}}finally{M.T=t,P.p=s}}(hi&3)!==0&&Jr(),un(e),s=e.pendingLanes,(n&4194090)!==0&&(s&42)!==0?e===Uu?_l++:(_l=0,Uu=e):_l=0,El(0)}}function xm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,tl(t)))}function Jr(e){return Sm(),_m(),Em(),wm()}function wm(){if(st!==5)return!1;var e=Zn,t=ju;ju=0;var n=Js(hi),i=M.T,s=P.p;try{P.p=32>n?32:n,M.T=null,n=Mu,Mu=null;var c=Zn,h=hi;if(st=0,di=Zn=null,hi=0,(we&6)!==0)throw Error(o(331));var p=we;if(we|=4,sm(c.current),im(c,c.current,h,n),we=p,El(0,!1),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(gt,c)}catch{}return!0}finally{P.p=s,M.T=i,xm(e,t)}}function Am(e,t,n){t=qt(n,t),t=fu(e.stateNode,t,2),e=Vn(e,t,2),e!==null&&(Bi(e,2),un(e))}function ke(e,t,n){if(e.tag===3)Am(e,e,n);else for(;t!==null;){if(t.tag===3){Am(t,e,n);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Kn===null||!Kn.has(i))){e=qt(n,e),n=Rh(2),i=Vn(t,n,2),i!==null&&(kh(n,i,t,e),Bi(i,2),un(i));break}}t=t.return}}function zu(e,t,n){var i=e.pingCache;if(i===null){i=e.pingCache=new tb;var s=new Set;i.set(t,s)}else s=i.get(t),s===void 0&&(s=new Set,i.set(t,s));s.has(n)||(Tu=!0,s.add(n),e=rb.bind(null,e,t,n),t.then(e,e))}function rb(e,t,n){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Ue===e&&(ye&n)===n&&(Qe===4||Qe===3&&(ye&62914560)===ye&&300>He()-ku?(we&2)===0&&mi(e,0):Ou|=n,fi===ye&&(fi=0)),un(e)}function Cm(e,t){t===0&&(t=Ef()),e=Ka(e,t),e!==null&&(Bi(e,t),un(e))}function sb(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Cm(e,n)}function ob(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(o(314))}i!==null&&i.delete(t),Cm(e,n)}function ub(e,t){return xe(e,t)}var es=null,pi=null,qu=!1,ts=!1,Bu=!1,ja=0;function un(e){e!==pi&&e.next===null&&(pi===null?es=pi=e:pi=pi.next=e),ts=!0,qu||(qu=!0,fb())}function El(e,t){if(!Bu&&ts){Bu=!0;do for(var n=!1,i=es;i!==null;){if(e!==0){var s=i.pendingLanes;if(s===0)var c=0;else{var h=i.suspendedLanes,p=i.pingedLanes;c=(1<<31-lt(42|e)+1)-1,c&=s&~(h&~p),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,km(i,c))}else c=ye,c=sr(i,i===Ue?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||qi(i,c)||(n=!0,km(i,c));i=i.next}while(n);Bu=!1}}function cb(){Tm()}function Tm(){ts=qu=!1;var e=0;ja!==0&&(bb()&&(e=ja),ja=0);for(var t=He(),n=null,i=es;i!==null;){var s=i.next,c=Om(i,t);c===0?(i.next=null,n===null?es=s:n.next=s,s===null&&(pi=n)):(n=i,(e!==0||(c&3)!==0)&&(ts=!0)),i=s}El(e)}function Om(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,s=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var h=31-lt(c),p=1<<h,b=s[h];b===-1?((p&n)===0||(p&i)!==0)&&(s[h]=Nv(p,t)):b<=t&&(e.expiredLanes|=p),c&=~p}if(t=Ue,n=ye,n=sr(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,n===0||e===t&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&Ee(i),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||qi(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(i!==null&&Ee(i),Js(n)){case 2:case 8:n=Je;break;case 32:n=bt;break;case 268435456:n=zi;break;default:n=bt}return i=Rm.bind(null,e),n=xe(n,i),e.callbackPriority=t,e.callbackNode=n,t}return i!==null&&i!==null&&Ee(i),e.callbackPriority=2,e.callbackNode=null,2}function Rm(e,t){if(st!==0&&st!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Jr()&&e.callbackNode!==n)return null;var i=ye;return i=sr(e,e===Ue?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(fm(e,i,t),Om(e,He()),e.callbackNode!=null&&e.callbackNode===n?Rm.bind(null,e):null)}function km(e,t){if(Jr())return null;fm(e,t,!0)}function fb(){_b(function(){(we&6)!==0?xe(Ie,cb):Tm()})}function Hu(){return ja===0&&(ja=_f()),ja}function jm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:dr(""+e)}function Mm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function db(e,t,n,i,s){if(t==="submit"&&n&&n.stateNode===s){var c=jm((s[_t]||null).action),h=i.submitter;h&&(t=(t=h[_t]||null)?jm(t.formAction):h.getAttribute("formAction"),t!==null&&(c=t,h=null));var p=new pr("action","action",null,i,s);e.push({event:p,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(ja!==0){var b=h?Mm(s,h):new FormData(s);ru(n,{pending:!0,data:b,method:s.method,action:c},null,b)}}else typeof c=="function"&&(p.preventDefault(),b=h?Mm(s,h):new FormData(s),ru(n,{pending:!0,data:b,method:s.method,action:c},c,b))},currentTarget:s}]})}}for(var Vu=0;Vu<Ao.length;Vu++){var Qu=Ao[Vu],hb=Qu.toLowerCase(),mb=Qu[0].toUpperCase()+Qu.slice(1);Wt(hb,"on"+mb)}Wt(fd,"onAnimationEnd"),Wt(dd,"onAnimationIteration"),Wt(hd,"onAnimationStart"),Wt("dblclick","onDoubleClick"),Wt("focusin","onFocus"),Wt("focusout","onBlur"),Wt(jy,"onTransitionRun"),Wt(My,"onTransitionStart"),Wt(Uy,"onTransitionCancel"),Wt(md,"onTransitionEnd"),Ha("onMouseEnter",["mouseout","mouseover"]),Ha("onMouseLeave",["mouseout","mouseover"]),Ha("onPointerEnter",["pointerout","pointerover"]),Ha("onPointerLeave",["pointerout","pointerover"]),ga("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ga("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ga("onBeforeInput",["compositionend","keypress","textInput","paste"]),ga("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ga("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ga("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),gb=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(xl));function Um(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var i=e[n],s=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var h=i.length-1;0<=h;h--){var p=i[h],b=p.instance,j=p.currentTarget;if(p=p.listener,b!==c&&s.isPropagationStopped())break e;c=p,s.currentTarget=j;try{c(s)}catch(z){Qr(z)}s.currentTarget=null,c=b}else for(h=0;h<i.length;h++){if(p=i[h],b=p.instance,j=p.currentTarget,p=p.listener,b!==c&&s.isPropagationStopped())break e;c=p,s.currentTarget=j;try{c(s)}catch(z){Qr(z)}s.currentTarget=null,c=b}}}}function pe(e,t){var n=t[eo];n===void 0&&(n=t[eo]=new Set);var i=e+"__bubble";n.has(i)||(Dm(t,e,2,!1),n.add(i))}function Pu(e,t,n){var i=0;t&&(i|=4),Dm(n,e,i,t)}var ns="_reactListening"+Math.random().toString(36).slice(2);function Yu(e){if(!e[ns]){e[ns]=!0,Tf.forEach(function(n){n!=="selectionchange"&&(gb.has(n)||Pu(n,!1,e),Pu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ns]||(t[ns]=!0,Pu("selectionchange",!1,t))}}function Dm(e,t,n,i){switch(ig(t)){case 2:var s=Qb;break;case 8:s=Pb;break;default:s=ic}n=s.bind(null,t,n,e),s=void 0,!fo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),i?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function $u(e,t,n,i,s){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var h=i.tag;if(h===3||h===4){var p=i.stateNode.containerInfo;if(p===s)break;if(h===4)for(h=i.return;h!==null;){var b=h.tag;if((b===3||b===4)&&h.stateNode.containerInfo===s)return;h=h.return}for(;p!==null;){if(h=za(p),h===null)return;if(b=h.tag,b===5||b===6||b===26||b===27){i=c=h;continue e}p=p.parentNode}}i=i.return}Vf(function(){var j=c,z=uo(n),V=[];e:{var U=gd.get(e);if(U!==void 0){var D=pr,se=e;switch(e){case"keypress":if(mr(n)===0)break e;case"keydown":case"keyup":D=uy;break;case"focusin":se="focus",D=po;break;case"focusout":se="blur",D=po;break;case"beforeblur":case"afterblur":D=po;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":D=Yf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":D=Zv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":D=dy;break;case fd:case dd:case hd:D=ey;break;case md:D=my;break;case"scroll":case"scrollend":D=Wv;break;case"wheel":D=py;break;case"copy":case"cut":case"paste":D=ny;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":D=If;break;case"toggle":case"beforetoggle":D=yy}var ae=(t&4)!==0,Re=!ae&&(e==="scroll"||e==="scrollend"),T=ae?U!==null?U+"Capture":null:U;ae=[];for(var E=j,k;E!==null;){var B=E;if(k=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||k===null||T===null||(B=Qi(E,T),B!=null&&ae.push(wl(E,B,k))),Re)break;E=E.return}0<ae.length&&(U=new D(U,se,null,n,z),V.push({event:U,listeners:ae}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",D=e==="mouseout"||e==="pointerout",U&&n!==oo&&(se=n.relatedTarget||n.fromElement)&&(za(se)||se[Na]))break e;if((D||U)&&(U=z.window===z?z:(U=z.ownerDocument)?U.defaultView||U.parentWindow:window,D?(se=n.relatedTarget||n.toElement,D=j,se=se?za(se):null,se!==null&&(Re=f(se),ae=se.tag,se!==Re||ae!==5&&ae!==27&&ae!==6)&&(se=null)):(D=null,se=j),D!==se)){if(ae=Yf,B="onMouseLeave",T="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(ae=If,B="onPointerLeave",T="onPointerEnter",E="pointer"),Re=D==null?U:Vi(D),k=se==null?U:Vi(se),U=new ae(B,E+"leave",D,n,z),U.target=Re,U.relatedTarget=k,B=null,za(z)===j&&(ae=new ae(T,E+"enter",se,n,z),ae.target=k,ae.relatedTarget=Re,B=ae),Re=B,D&&se)t:{for(ae=D,T=se,E=0,k=ae;k;k=vi(k))E++;for(k=0,B=T;B;B=vi(B))k++;for(;0<E-k;)ae=vi(ae),E--;for(;0<k-E;)T=vi(T),k--;for(;E--;){if(ae===T||T!==null&&ae===T.alternate)break t;ae=vi(ae),T=vi(T)}ae=null}else ae=null;D!==null&&Lm(V,U,D,ae,!1),se!==null&&Re!==null&&Lm(V,Re,se,ae,!0)}}e:{if(U=j?Vi(j):window,D=U.nodeName&&U.nodeName.toLowerCase(),D==="select"||D==="input"&&U.type==="file")var K=ed;else if(Ff(U))if(td)K=Oy;else{K=Cy;var me=Ay}else D=U.nodeName,!D||D.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?j&&so(j.elementType)&&(K=ed):K=Ty;if(K&&(K=K(e,j))){Jf(V,K,n,z);break e}me&&me(e,U,j),e==="focusout"&&j&&U.type==="number"&&j.memoizedProps.value!=null&&ro(U,"number",U.value)}switch(me=j?Vi(j):window,e){case"focusin":(Ff(me)||me.contentEditable==="true")&&(Ga=me,Eo=j,Ki=null);break;case"focusout":Ki=Eo=Ga=null;break;case"mousedown":xo=!0;break;case"contextmenu":case"mouseup":case"dragend":xo=!1,ud(V,n,z);break;case"selectionchange":if(ky)break;case"keydown":case"keyup":ud(V,n,z)}var te;if(yo)e:{switch(e){case"compositionstart":var ie="onCompositionStart";break e;case"compositionend":ie="onCompositionEnd";break e;case"compositionupdate":ie="onCompositionUpdate";break e}ie=void 0}else Ia?Kf(e,n)&&(ie="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ie="onCompositionStart");ie&&(Gf&&n.locale!=="ko"&&(Ia||ie!=="onCompositionStart"?ie==="onCompositionEnd"&&Ia&&(te=Qf()):(zn=z,ho="value"in zn?zn.value:zn.textContent,Ia=!0)),me=as(j,ie),0<me.length&&(ie=new $f(ie,e,null,n,z),V.push({event:ie,listeners:me}),te?ie.data=te:(te=Zf(n),te!==null&&(ie.data=te)))),(te=Sy?_y(e,n):Ey(e,n))&&(ie=as(j,"onBeforeInput"),0<ie.length&&(me=new $f("onBeforeInput","beforeinput",null,n,z),V.push({event:me,listeners:ie}),me.data=te)),db(V,e,j,n,z)}Um(V,t)})}function wl(e,t,n){return{instance:e,listener:t,currentTarget:n}}function as(e,t){for(var n=t+"Capture",i=[];e!==null;){var s=e,c=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||c===null||(s=Qi(e,n),s!=null&&i.unshift(wl(e,s,c)),s=Qi(e,t),s!=null&&i.push(wl(e,s,c))),e.tag===3)return i;e=e.return}return[]}function vi(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Lm(e,t,n,i,s){for(var c=t._reactName,h=[];n!==null&&n!==i;){var p=n,b=p.alternate,j=p.stateNode;if(p=p.tag,b!==null&&b===i)break;p!==5&&p!==26&&p!==27||j===null||(b=j,s?(j=Qi(n,c),j!=null&&h.unshift(wl(n,j,b))):s||(j=Qi(n,c),j!=null&&h.push(wl(n,j,b)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var pb=/\r\n?/g,vb=/\u0000|\uFFFD/g;function Nm(e){return(typeof e=="string"?e:""+e).replace(pb,`
`).replace(vb,"")}function zm(e,t){return t=Nm(t),Nm(e)===t}function is(){}function Oe(e,t,n,i,s,c){switch(n){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||Pa(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&Pa(e,""+i);break;case"className":ur(e,"class",i);break;case"tabIndex":ur(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":ur(e,n,i);break;case"style":Bf(e,i,c);break;case"data":if(t!=="object"){ur(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=dr(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Oe(e,t,"name",s.name,s,null),Oe(e,t,"formEncType",s.formEncType,s,null),Oe(e,t,"formMethod",s.formMethod,s,null),Oe(e,t,"formTarget",s.formTarget,s,null)):(Oe(e,t,"encType",s.encType,s,null),Oe(e,t,"method",s.method,s,null),Oe(e,t,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(n);break}i=dr(""+i),e.setAttribute(n,i);break;case"onClick":i!=null&&(e.onclick=is);break;case"onScroll":i!=null&&pe("scroll",e);break;case"onScrollEnd":i!=null&&pe("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}n=dr(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":i===!0?e.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":pe("beforetoggle",e),pe("toggle",e),or(e,"popover",i);break;case"xlinkActuate":gn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":gn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":gn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":gn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":gn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":gn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":gn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":gn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":gn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":or(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Gv.get(n)||n,or(e,n,i))}}function Iu(e,t,n,i,s,c){switch(n){case"style":Bf(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof i=="string"?Pa(e,i):(typeof i=="number"||typeof i=="bigint")&&Pa(e,""+i);break;case"onScroll":i!=null&&pe("scroll",e);break;case"onScrollEnd":i!=null&&pe("scrollend",e);break;case"onClick":i!=null&&(e.onclick=is);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Of.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),t=n.slice(2,s?n.length-7:void 0),c=e[_t]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,s),typeof i=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,s);break e}n in e?e[n]=i:i===!0?e.setAttribute(n,""):or(e,n,i)}}}function ot(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":pe("error",e),pe("load",e);var i=!1,s=!1,c;for(c in n)if(n.hasOwnProperty(c)){var h=n[c];if(h!=null)switch(c){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Oe(e,t,c,h,n,null)}}s&&Oe(e,t,"srcSet",n.srcSet,n,null),i&&Oe(e,t,"src",n.src,n,null);return;case"input":pe("invalid",e);var p=c=h=s=null,b=null,j=null;for(i in n)if(n.hasOwnProperty(i)){var z=n[i];if(z!=null)switch(i){case"name":s=z;break;case"type":h=z;break;case"checked":b=z;break;case"defaultChecked":j=z;break;case"value":c=z;break;case"defaultValue":p=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(o(137,t));break;default:Oe(e,t,i,z,n,null)}}Lf(e,c,p,b,j,h,s,!1),cr(e);return;case"select":pe("invalid",e),i=h=c=null;for(s in n)if(n.hasOwnProperty(s)&&(p=n[s],p!=null))switch(s){case"value":c=p;break;case"defaultValue":h=p;break;case"multiple":i=p;default:Oe(e,t,s,p,n,null)}t=c,n=h,e.multiple=!!i,t!=null?Qa(e,!!i,t,!1):n!=null&&Qa(e,!!i,n,!0);return;case"textarea":pe("invalid",e),c=s=i=null;for(h in n)if(n.hasOwnProperty(h)&&(p=n[h],p!=null))switch(h){case"value":i=p;break;case"defaultValue":s=p;break;case"children":c=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(91));break;default:Oe(e,t,h,p,n,null)}zf(e,i,s,c),cr(e);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(i=n[b],i!=null))switch(b){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Oe(e,t,b,i,n,null)}return;case"dialog":pe("beforetoggle",e),pe("toggle",e),pe("cancel",e),pe("close",e);break;case"iframe":case"object":pe("load",e);break;case"video":case"audio":for(i=0;i<xl.length;i++)pe(xl[i],e);break;case"image":pe("error",e),pe("load",e);break;case"details":pe("toggle",e);break;case"embed":case"source":case"link":pe("error",e),pe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(j in n)if(n.hasOwnProperty(j)&&(i=n[j],i!=null))switch(j){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Oe(e,t,j,i,n,null)}return;default:if(so(t)){for(z in n)n.hasOwnProperty(z)&&(i=n[z],i!==void 0&&Iu(e,t,z,i,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(i=n[p],i!=null&&Oe(e,t,p,i,n,null))}function yb(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,c=null,h=null,p=null,b=null,j=null,z=null;for(D in n){var V=n[D];if(n.hasOwnProperty(D)&&V!=null)switch(D){case"checked":break;case"value":break;case"defaultValue":b=V;default:i.hasOwnProperty(D)||Oe(e,t,D,null,i,V)}}for(var U in i){var D=i[U];if(V=n[U],i.hasOwnProperty(U)&&(D!=null||V!=null))switch(U){case"type":c=D;break;case"name":s=D;break;case"checked":j=D;break;case"defaultChecked":z=D;break;case"value":h=D;break;case"defaultValue":p=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(o(137,t));break;default:D!==V&&Oe(e,t,U,D,i,V)}}lo(e,h,p,b,j,z,c,s);return;case"select":D=h=p=U=null;for(c in n)if(b=n[c],n.hasOwnProperty(c)&&b!=null)switch(c){case"value":break;case"multiple":D=b;default:i.hasOwnProperty(c)||Oe(e,t,c,null,i,b)}for(s in i)if(c=i[s],b=n[s],i.hasOwnProperty(s)&&(c!=null||b!=null))switch(s){case"value":U=c;break;case"defaultValue":p=c;break;case"multiple":h=c;default:c!==b&&Oe(e,t,s,c,i,b)}t=p,n=h,i=D,U!=null?Qa(e,!!n,U,!1):!!i!=!!n&&(t!=null?Qa(e,!!n,t,!0):Qa(e,!!n,n?[]:"",!1));return;case"textarea":D=U=null;for(p in n)if(s=n[p],n.hasOwnProperty(p)&&s!=null&&!i.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Oe(e,t,p,null,i,s)}for(h in i)if(s=i[h],c=n[h],i.hasOwnProperty(h)&&(s!=null||c!=null))switch(h){case"value":U=s;break;case"defaultValue":D=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==c&&Oe(e,t,h,s,i,c)}Nf(e,U,D);return;case"option":for(var se in n)if(U=n[se],n.hasOwnProperty(se)&&U!=null&&!i.hasOwnProperty(se))switch(se){case"selected":e.selected=!1;break;default:Oe(e,t,se,null,i,U)}for(b in i)if(U=i[b],D=n[b],i.hasOwnProperty(b)&&U!==D&&(U!=null||D!=null))switch(b){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Oe(e,t,b,U,i,D)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in n)U=n[ae],n.hasOwnProperty(ae)&&U!=null&&!i.hasOwnProperty(ae)&&Oe(e,t,ae,null,i,U);for(j in i)if(U=i[j],D=n[j],i.hasOwnProperty(j)&&U!==D&&(U!=null||D!=null))switch(j){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(o(137,t));break;default:Oe(e,t,j,U,i,D)}return;default:if(so(t)){for(var Re in n)U=n[Re],n.hasOwnProperty(Re)&&U!==void 0&&!i.hasOwnProperty(Re)&&Iu(e,t,Re,void 0,i,U);for(z in i)U=i[z],D=n[z],!i.hasOwnProperty(z)||U===D||U===void 0&&D===void 0||Iu(e,t,z,U,i,D);return}}for(var T in n)U=n[T],n.hasOwnProperty(T)&&U!=null&&!i.hasOwnProperty(T)&&Oe(e,t,T,null,i,U);for(V in i)U=i[V],D=n[V],!i.hasOwnProperty(V)||U===D||U==null&&D==null||Oe(e,t,V,U,i,D)}var Gu=null,Xu=null;function ls(e){return e.nodeType===9?e:e.ownerDocument}function qm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Bm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Wu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ku=null;function bb(){var e=window.event;return e&&e.type==="popstate"?e===Ku?!1:(Ku=e,!0):(Ku=null,!1)}var Hm=typeof setTimeout=="function"?setTimeout:void 0,Sb=typeof clearTimeout=="function"?clearTimeout:void 0,Vm=typeof Promise=="function"?Promise:void 0,_b=typeof queueMicrotask=="function"?queueMicrotask:typeof Vm<"u"?function(e){return Vm.resolve(null).then(e).catch(Eb)}:Hm;function Eb(e){setTimeout(function(){throw e})}function Jn(e){return e==="head"}function Qm(e,t){var n=t,i=0,s=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<i&&8>i){n=i;var h=e.ownerDocument;if(n&1&&Al(h.documentElement),n&2&&Al(h.body),n&4)for(n=h.head,Al(n),h=n.firstChild;h;){var p=h.nextSibling,b=h.nodeName;h[Hi]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=p}}if(s===0){e.removeChild(c),Ul(t);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:i=n.charCodeAt(0)-48;else i=0;n=c}while(n);Ul(t)}function Zu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Zu(n),to(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function xb(e,t,n,i){for(;e.nodeType===1;){var s=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[Hi])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Zt(e.nextSibling),e===null)break}return null}function wb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Zt(e.nextSibling),e===null))return null;return e}function Fu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Ab(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ju=null;function Pm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Ym(e,t,n){switch(t=ls(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Al(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);to(e)}var Yt=new Map,$m=new Set;function rs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Rn=P.d;P.d={f:Cb,r:Tb,D:Ob,C:Rb,L:kb,m:jb,X:Ub,S:Mb,M:Db};function Cb(){var e=Rn.f(),t=Zr();return e||t}function Tb(e){var t=qa(e);t!==null&&t.tag===5&&t.type==="form"?fh(t):Rn.r(e)}var yi=typeof document>"u"?null:document;function Im(e,t,n){var i=yi;if(i&&typeof t=="string"&&t){var s=zt(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),$m.has(s)||($m.add(s),e={rel:e,crossOrigin:n,href:t},i.querySelector(s)===null&&(t=i.createElement("link"),ot(t,"link",e),et(t),i.head.appendChild(t)))}}function Ob(e){Rn.D(e),Im("dns-prefetch",e,null)}function Rb(e,t){Rn.C(e,t),Im("preconnect",e,t)}function kb(e,t,n){Rn.L(e,t,n);var i=yi;if(i&&e&&t){var s='link[rel="preload"][as="'+zt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+zt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+zt(n.imageSizes)+'"]')):s+='[href="'+zt(e)+'"]';var c=s;switch(t){case"style":c=bi(e);break;case"script":c=Si(e)}Yt.has(c)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Yt.set(c,e),i.querySelector(s)!==null||t==="style"&&i.querySelector(Cl(c))||t==="script"&&i.querySelector(Tl(c))||(t=i.createElement("link"),ot(t,"link",e),et(t),i.head.appendChild(t)))}}function jb(e,t){Rn.m(e,t);var n=yi;if(n&&e){var i=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+zt(i)+'"][href="'+zt(e)+'"]',c=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Si(e)}if(!Yt.has(c)&&(e=y({rel:"modulepreload",href:e},t),Yt.set(c,e),n.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Tl(c)))return}i=n.createElement("link"),ot(i,"link",e),et(i),n.head.appendChild(i)}}}function Mb(e,t,n){Rn.S(e,t,n);var i=yi;if(i&&e){var s=Ba(i).hoistableStyles,c=bi(e);t=t||"default";var h=s.get(c);if(!h){var p={loading:0,preload:null};if(h=i.querySelector(Cl(c)))p.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Yt.get(c))&&ec(e,n);var b=h=i.createElement("link");et(b),ot(b,"link",e),b._p=new Promise(function(j,z){b.onload=j,b.onerror=z}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,ss(h,t,i)}h={type:"stylesheet",instance:h,count:1,state:p},s.set(c,h)}}}function Ub(e,t){Rn.X(e,t);var n=yi;if(n&&e){var i=Ba(n).hoistableScripts,s=Si(e),c=i.get(s);c||(c=n.querySelector(Tl(s)),c||(e=y({src:e,async:!0},t),(t=Yt.get(s))&&tc(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(s,c))}}function Db(e,t){Rn.M(e,t);var n=yi;if(n&&e){var i=Ba(n).hoistableScripts,s=Si(e),c=i.get(s);c||(c=n.querySelector(Tl(s)),c||(e=y({src:e,async:!0,type:"module"},t),(t=Yt.get(s))&&tc(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(s,c))}}function Gm(e,t,n,i){var s=(s=re.current)?rs(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=bi(n.href),n=Ba(s).hoistableStyles,i=n.get(t),i||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=bi(n.href);var c=Ba(s).hoistableStyles,h=c.get(e);if(h||(s=s.ownerDocument||s,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,h),(c=s.querySelector(Cl(e)))&&!c._p&&(h.instance=c,h.state.loading=5),Yt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Yt.set(e,n),c||Lb(s,e,n,h.state))),t&&i===null)throw Error(o(528,""));return h}if(t&&i!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Si(n),n=Ba(s).hoistableScripts,i=n.get(t),i||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function bi(e){return'href="'+zt(e)+'"'}function Cl(e){return'link[rel="stylesheet"]['+e+"]"}function Xm(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function Lb(e,t,n,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),ot(t,"link",n),et(t),e.head.appendChild(t))}function Si(e){return'[src="'+zt(e)+'"]'}function Tl(e){return"script[async]"+e}function Wm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+zt(n.href)+'"]');if(i)return t.instance=i,et(i),i;var s=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),et(i),ot(i,"style",s),ss(i,n.precedence,e),t.instance=i;case"stylesheet":s=bi(n.href);var c=e.querySelector(Cl(s));if(c)return t.state.loading|=4,t.instance=c,et(c),c;i=Xm(n),(s=Yt.get(s))&&ec(i,s),c=(e.ownerDocument||e).createElement("link"),et(c);var h=c;return h._p=new Promise(function(p,b){h.onload=p,h.onerror=b}),ot(c,"link",i),t.state.loading|=4,ss(c,n.precedence,e),t.instance=c;case"script":return c=Si(n.src),(s=e.querySelector(Tl(c)))?(t.instance=s,et(s),s):(i=n,(s=Yt.get(c))&&(i=y({},n),tc(i,s)),e=e.ownerDocument||e,s=e.createElement("script"),et(s),ot(s,"link",i),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,ss(i,n.precedence,e));return t.instance}function ss(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,c=s,h=0;h<i.length;h++){var p=i[h];if(p.dataset.precedence===t)c=p;else if(c!==s)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function ec(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function tc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var os=null;function Km(e,t,n){if(os===null){var i=new Map,s=os=new Map;s.set(n,i)}else s=os,i=s.get(n),i||(i=new Map,s.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),s=0;s<n.length;s++){var c=n[s];if(!(c[Hi]||c[ct]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var h=c.getAttribute(t)||"";h=e+h;var p=i.get(h);p?p.push(c):i.set(h,[c])}}return i}function Zm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Nb(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Fm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ol=null;function zb(){}function qb(e,t,n){if(Ol===null)throw Error(o(475));var i=Ol;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=bi(n.href),c=e.querySelector(Cl(s));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=us.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,et(c);return}c=e.ownerDocument||e,n=Xm(n),(s=Yt.get(s))&&ec(n,s),c=c.createElement("link"),et(c);var h=c;h._p=new Promise(function(p,b){h.onload=p,h.onerror=b}),ot(c,"link",n),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=us.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function Bb(){if(Ol===null)throw Error(o(475));var e=Ol;return e.stylesheets&&e.count===0&&nc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&nc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function us(){if(this.count--,this.count===0){if(this.stylesheets)nc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var cs=null;function nc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,cs=new Map,t.forEach(Hb,e),cs=null,us.call(e))}function Hb(e,t){if(!(t.state.loading&4)){var n=cs.get(e);if(n)var i=n.get(null);else{n=new Map,cs.set(e,n);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<s.length;c++){var h=s[c];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),i=h)}i&&n.set(null,i)}s=t.instance,h=s.getAttribute("data-precedence"),c=n.get(h)||i,c===i&&n.set(null,s),n.set(h,s),this.count++,i=us.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),c?c.parentNode.insertBefore(s,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var Rl={$$typeof:$,Provider:null,Consumer:null,_currentValue:X,_currentValue2:X,_threadCount:0};function Vb(e,t,n,i,s,c,h,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.hiddenUpdates=Zs(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=c,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function Jm(e,t,n,i,s,c,h,p,b,j,z,V){return e=new Vb(e,t,n,h,p,b,j,V),t=1,c===!0&&(t|=24),c=Ot(3,null,null,t),e.current=c,c.stateNode=e,t=zo(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:n,cache:t},Vo(c),e}function eg(e){return e?(e=Za,e):Za}function tg(e,t,n,i,s,c){s=eg(s),i.context===null?i.context=s:i.pendingContext=s,i=Hn(t),i.payload={element:n},c=c===void 0?null:c,c!==null&&(i.callback=c),n=Vn(e,i,t),n!==null&&(Ut(n,e,t),ll(n,e,t))}function ng(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ac(e,t){ng(e,t),(e=e.alternate)&&ng(e,t)}function ag(e){if(e.tag===13){var t=Ka(e,67108864);t!==null&&Ut(t,e,67108864),ac(e,67108864)}}var fs=!0;function Qb(e,t,n,i){var s=M.T;M.T=null;var c=P.p;try{P.p=2,ic(e,t,n,i)}finally{P.p=c,M.T=s}}function Pb(e,t,n,i){var s=M.T;M.T=null;var c=P.p;try{P.p=8,ic(e,t,n,i)}finally{P.p=c,M.T=s}}function ic(e,t,n,i){if(fs){var s=lc(i);if(s===null)$u(e,t,i,ds,n),lg(e,i);else if($b(s,e,t,n,i))i.stopPropagation();else if(lg(e,i),t&4&&-1<Yb.indexOf(e)){for(;s!==null;){var c=qa(s);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var h=ma(c.pendingLanes);if(h!==0){var p=c;for(p.pendingLanes|=2,p.entangledLanes|=2;h;){var b=1<<31-lt(h);p.entanglements[1]|=b,h&=~b}un(c),(we&6)===0&&(Wr=He()+500,El(0))}}break;case 13:p=Ka(c,2),p!==null&&Ut(p,c,2),Zr(),ac(c,2)}if(c=lc(i),c===null&&$u(e,t,i,ds,n),c===s)break;s=c}s!==null&&i.stopPropagation()}else $u(e,t,i,null,n)}}function lc(e){return e=uo(e),rc(e)}var ds=null;function rc(e){if(ds=null,e=za(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ds=e,null}function ig(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ln()){case Ie:return 2;case Je:return 8;case bt:case ar:return 32;case zi:return 268435456;default:return 32}default:return 32}}var sc=!1,ea=null,ta=null,na=null,kl=new Map,jl=new Map,aa=[],Yb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lg(e,t){switch(e){case"focusin":case"focusout":ea=null;break;case"dragenter":case"dragleave":ta=null;break;case"mouseover":case"mouseout":na=null;break;case"pointerover":case"pointerout":kl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jl.delete(t.pointerId)}}function Ml(e,t,n,i,s,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:c,targetContainers:[s]},t!==null&&(t=qa(t),t!==null&&ag(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function $b(e,t,n,i,s){switch(t){case"focusin":return ea=Ml(ea,e,t,n,i,s),!0;case"dragenter":return ta=Ml(ta,e,t,n,i,s),!0;case"mouseover":return na=Ml(na,e,t,n,i,s),!0;case"pointerover":var c=s.pointerId;return kl.set(c,Ml(kl.get(c)||null,e,t,n,i,s)),!0;case"gotpointercapture":return c=s.pointerId,jl.set(c,Ml(jl.get(c)||null,e,t,n,i,s)),!0}return!1}function rg(e){var t=za(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,qv(e.priority,function(){if(n.tag===13){var i=Mt();i=Fs(i);var s=Ka(n,i);s!==null&&Ut(s,n,i),ac(n,i)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function hs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=lc(e.nativeEvent);if(n===null){n=e.nativeEvent;var i=new n.constructor(n.type,n);oo=i,n.target.dispatchEvent(i),oo=null}else return t=qa(n),t!==null&&ag(t),e.blockedOn=n,!1;t.shift()}return!0}function sg(e,t,n){hs(e)&&n.delete(t)}function Ib(){sc=!1,ea!==null&&hs(ea)&&(ea=null),ta!==null&&hs(ta)&&(ta=null),na!==null&&hs(na)&&(na=null),kl.forEach(sg),jl.forEach(sg)}function ms(e,t){e.blockedOn===t&&(e.blockedOn=null,sc||(sc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ib)))}var gs=null;function og(e){gs!==e&&(gs=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){gs===e&&(gs=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],s=e[t+2];if(typeof i!="function"){if(rc(i||n)===null)continue;break}var c=qa(n);c!==null&&(e.splice(t,3),t-=3,ru(c,{pending:!0,data:s,method:n.method,action:i},i,s))}}))}function Ul(e){function t(b){return ms(b,e)}ea!==null&&ms(ea,e),ta!==null&&ms(ta,e),na!==null&&ms(na,e),kl.forEach(t),jl.forEach(t);for(var n=0;n<aa.length;n++){var i=aa[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<aa.length&&(n=aa[0],n.blockedOn===null);)rg(n),n.blockedOn===null&&aa.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var s=n[i],c=n[i+1],h=s[_t]||null;if(typeof c=="function")h||og(n);else if(h){var p=null;if(c&&c.hasAttribute("formAction")){if(s=c,h=c[_t]||null)p=h.formAction;else if(rc(s)!==null)continue}else p=h.action;typeof p=="function"?n[i+1]=p:(n.splice(i,3),i-=3),og(n)}}}function oc(e){this._internalRoot=e}ps.prototype.render=oc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,i=Mt();tg(n,i,e,t,null,null)},ps.prototype.unmount=oc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tg(e.current,2,null,e,null,null),Zr(),t[Na]=null}};function ps(e){this._internalRoot=e}ps.prototype.unstable_scheduleHydration=function(e){if(e){var t=Af();e={blockedOn:null,target:e,priority:t};for(var n=0;n<aa.length&&t!==0&&t<aa[n].priority;n++);aa.splice(n,0,e),n===0&&rg(e)}};var ug=l.version;if(ug!=="19.1.0")throw Error(o(527,ug,"19.1.0"));P.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=m(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var Gb={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var vs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!vs.isDisabled&&vs.supportsFiber)try{gt=vs.inject(Gb),it=vs}catch{}}return Ll.createRoot=function(e,t){if(!u(e))throw Error(o(299));var n=!1,i="",s=Ah,c=Ch,h=Th,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=Jm(e,1,!1,null,null,n,i,s,c,h,p,null),e[Na]=t.current,Yu(e),new oc(t)},Ll.hydrateRoot=function(e,t,n){if(!u(e))throw Error(o(299));var i=!1,s="",c=Ah,h=Ch,p=Th,b=null,j=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(j=n.formState)),t=Jm(e,1,!0,t,n??null,i,s,c,h,p,b,j),t.context=eg(null),n=t.current,i=Mt(),i=Fs(i),s=Hn(i),s.callback=null,Vn(n,s,i),n=i,t.current.lanes=n,Bi(t,n),un(t),e[Na]=t.current,Yu(e),new ps(t)},Ll.version="19.1.0",Ll}var bg;function a0(){if(bg)return fc.exports;bg=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),fc.exports=n0(),fc.exports}var i0=a0(),dn=[],It=[],l0=Uint8Array,gc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var _i=0,r0=gc.length;_i<r0;++_i)dn[_i]=gc[_i],It[gc.charCodeAt(_i)]=_i;It[45]=62;It[95]=63;function s0(a){var l=a.length;if(l%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=a.indexOf("=");r===-1&&(r=l);var o=r===l?0:4-r%4;return[r,o]}function o0(a,l,r){return(l+r)*3/4-r}function Yl(a){var l,r=s0(a),o=r[0],u=r[1],f=new l0(o0(a,o,u)),d=0,v=u>0?o-4:o,m;for(m=0;m<v;m+=4)l=It[a.charCodeAt(m)]<<18|It[a.charCodeAt(m+1)]<<12|It[a.charCodeAt(m+2)]<<6|It[a.charCodeAt(m+3)],f[d++]=l>>16&255,f[d++]=l>>8&255,f[d++]=l&255;return u===2&&(l=It[a.charCodeAt(m)]<<2|It[a.charCodeAt(m+1)]>>4,f[d++]=l&255),u===1&&(l=It[a.charCodeAt(m)]<<10|It[a.charCodeAt(m+1)]<<4|It[a.charCodeAt(m+2)]>>2,f[d++]=l>>8&255,f[d++]=l&255),f}function u0(a){return dn[a>>18&63]+dn[a>>12&63]+dn[a>>6&63]+dn[a&63]}function c0(a,l,r){for(var o,u=[],f=l;f<r;f+=3)o=(a[f]<<16&16711680)+(a[f+1]<<8&65280)+(a[f+2]&255),u.push(u0(o));return u.join("")}function $l(a){for(var l,r=a.length,o=r%3,u=[],f=16383,d=0,v=r-o;d<v;d+=f)u.push(c0(a,d,d+f>v?v:d+f));return o===1?(l=a[r-1],u.push(dn[l>>2]+dn[l<<4&63]+"==")):o===2&&(l=(a[r-2]<<8)+a[r-1],u.push(dn[l>>10]+dn[l>>4&63]+dn[l<<2&63]+"=")),u.join("")}function jn(a){if(a===void 0)return{};if(!pp(a))throw new Error(`The arguments to a Convex function must be an object. Received: ${a}`);return a}function f0(a){if(typeof a>"u")throw new Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");if(typeof a!="string")throw new Error(`Invalid deployment address: found ${a}".`);if(!(a.startsWith("http:")||a.startsWith("https:")))throw new Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${a}".`);try{new URL(a)}catch{throw new Error(`Invalid deployment address: "${a}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}if(a.endsWith(".convex.site"))throw new Error(`Invalid deployment address: "${a}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}function pp(a){const l=typeof a=="object",r=Object.getPrototypeOf(a),o=r===null||r===Object.prototype||r?.constructor?.name==="Object";return l&&o}const vp=!0,Oi=BigInt("-9223372036854775808"),sf=BigInt("9223372036854775807"),Hc=BigInt("0"),d0=BigInt("8"),h0=BigInt("256");function yp(a){return Number.isNaN(a)||!Number.isFinite(a)||Object.is(a,-0)}function m0(a){a<Hc&&(a-=Oi+Oi);let l=a.toString(16);l.length%2===1&&(l="0"+l);const r=new Uint8Array(new ArrayBuffer(8));let o=0;for(const u of l.match(/.{2}/g).reverse())r.set([parseInt(u,16)],o++),a>>=d0;return $l(r)}function g0(a){const l=Yl(a);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);let r=Hc,o=Hc;for(const u of l)r+=BigInt(u)*h0**o,o++;return r>sf&&(r+=Oi+Oi),r}function p0(a){if(a<Oi||sf<a)throw new Error(`BigInt ${a} does not fit into a 64-bit signed integer.`);const l=new ArrayBuffer(8);return new DataView(l).setBigInt64(0,a,!0),$l(new Uint8Array(l))}function v0(a){const l=Yl(a);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);return new DataView(l.buffer).getBigInt64(0,!0)}const y0=DataView.prototype.setBigInt64?p0:m0,b0=DataView.prototype.getBigInt64?v0:g0,Sg=1024;function bp(a){if(a.length>Sg)throw new Error(`Field name ${a} exceeds maximum field name length ${Sg}.`);if(a.startsWith("$"))throw new Error(`Field name ${a} starts with a '$', which is reserved.`);for(let l=0;l<a.length;l+=1){const r=a.charCodeAt(l);if(r<32||r>=127)throw new Error(`Field name ${a} has invalid character '${a[l]}': Field names can only contain non-control ASCII characters`)}}function Ri(a){if(a===null||typeof a=="boolean"||typeof a=="number"||typeof a=="string")return a;if(Array.isArray(a))return a.map(o=>Ri(o));if(typeof a!="object")throw new Error(`Unexpected type of ${a}`);const l=Object.entries(a);if(l.length===1){const o=l[0][0];if(o==="$bytes"){if(typeof a.$bytes!="string")throw new Error(`Malformed $bytes field on ${a}`);return Yl(a.$bytes).buffer}if(o==="$integer"){if(typeof a.$integer!="string")throw new Error(`Malformed $integer field on ${a}`);return b0(a.$integer)}if(o==="$float"){if(typeof a.$float!="string")throw new Error(`Malformed $float field on ${a}`);const u=Yl(a.$float);if(u.byteLength!==8)throw new Error(`Received ${u.byteLength} bytes, expected 8 for $float`);const d=new DataView(u.buffer).getFloat64(0,vp);if(!yp(d))throw new Error(`Float ${d} should be encoded as a number`);return d}if(o==="$set")throw new Error("Received a Set which is no longer supported as a Convex type.");if(o==="$map")throw new Error("Received a Map which is no longer supported as a Convex type.")}const r={};for(const[o,u]of Object.entries(a))bp(o),r[o]=Ri(u);return r}function Bl(a){return JSON.stringify(a,(l,r)=>r===void 0?"undefined":typeof r=="bigint"?`${r.toString()}n`:r)}function Vc(a,l,r,o){if(a===void 0){const d=r&&` (present at path ${r} in original object ${Bl(l)})`;throw new Error(`undefined is not a valid Convex value${d}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`)}if(a===null)return a;if(typeof a=="bigint"){if(a<Oi||sf<a)throw new Error(`BigInt ${a} does not fit into a 64-bit signed integer.`);return{$integer:y0(a)}}if(typeof a=="number")if(yp(a)){const d=new ArrayBuffer(8);return new DataView(d).setFloat64(0,a,vp),{$float:$l(new Uint8Array(d))}}else return a;if(typeof a=="boolean"||typeof a=="string")return a;if(a instanceof ArrayBuffer)return{$bytes:$l(new Uint8Array(a))};if(Array.isArray(a))return a.map((d,v)=>Vc(d,l,r+`[${v}]`));if(a instanceof Set)throw new Error(pc(r,"Set",[...a],l));if(a instanceof Map)throw new Error(pc(r,"Map",[...a],l));if(!pp(a)){const d=a?.constructor?.name,v=d?`${d} `:"";throw new Error(pc(r,v,a,l))}const u={},f=Object.entries(a);f.sort(([d,v],[m,g])=>d===m?0:d<m?-1:1);for(const[d,v]of f)v!==void 0&&(bp(d),u[d]=Vc(v,l,r+`.${d}`));return u}function pc(a,l,r,o){return a?`${l}${Bl(r)} is not a supported Convex type (present at path ${a} in original object ${Bl(o)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`:`${l}${Bl(r)} is not a supported Convex type.`}function ca(a){return Vc(a,a,"")}var S0=Object.defineProperty,_0=(a,l,r)=>l in a?S0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,vc=(a,l,r)=>_0(a,typeof l!="symbol"?l+"":l,r),_g,Eg;const E0=Symbol.for("ConvexError");class Qc extends(Eg=Error,_g=E0,Eg){constructor(l){super(typeof l=="string"?l:Bl(l)),vc(this,"name","ConvexError"),vc(this,"data"),vc(this,_g,!0),this.data=l}}const Sp=()=>Array.from({length:4},()=>0);Sp();Sp();const xg="1.25.0";var x0=Object.defineProperty,w0=(a,l,r)=>l in a?x0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,wg=(a,l,r)=>w0(a,typeof l!="symbol"?l+"":l,r);const A0="color:rgb(0, 145, 255)";function _p(a){switch(a){case"query":return"Q";case"mutation":return"M";case"action":return"A";case"any":return"?"}}class Ep{constructor(l){wg(this,"_onLogLineFuncs"),wg(this,"_verbose"),this._onLogLineFuncs={},this._verbose=l.verbose}addLogLineListener(l){let r=Math.random().toString(36).substring(2,15);for(let o=0;o<10&&this._onLogLineFuncs[r]!==void 0;o++)r=Math.random().toString(36).substring(2,15);return this._onLogLineFuncs[r]=l,()=>{delete this._onLogLineFuncs[r]}}logVerbose(...l){if(this._verbose)for(const r of Object.values(this._onLogLineFuncs))r("debug",`${new Date().toISOString()}`,...l)}log(...l){for(const r of Object.values(this._onLogLineFuncs))r("info",...l)}warn(...l){for(const r of Object.values(this._onLogLineFuncs))r("warn",...l)}error(...l){for(const r of Object.values(this._onLogLineFuncs))r("error",...l)}}function xp(a){const l=new Ep(a);return l.addLogLineListener((r,...o)=>{switch(r){case"debug":console.debug(...o);break;case"info":console.log(...o);break;case"warn":console.warn(...o);break;case"error":console.error(...o);break;default:console.log(...o)}}),l}function wp(a){return new Ep(a)}function Ls(a,l,r,o,u){const f=_p(r);if(typeof u=="object"&&(u=`ConvexError ${JSON.stringify(u.errorData,null,2)}`),l==="info"){const d=u.match(/^\[.*?\] /);if(d===null){a.error(`[CONVEX ${f}(${o})] Could not parse console.log`);return}const v=u.slice(1,d[0].length-2),m=u.slice(d[0].length);a.log(`%c[CONVEX ${f}(${o})] [${v}]`,A0,m)}else a.error(`[CONVEX ${f}(${o})] ${u}`)}function C0(a,l){const r=`[CONVEX FATAL ERROR] ${l}`;return a.error(r),new Error(r)}function Ci(a,l,r){return`[CONVEX ${_p(a)}(${l})] ${r.errorMessage}
  Called by client`}function Pc(a,l){return l.data=a.errorData,l}function Ns(a){const l=a.split(":");let r,o;return l.length===1?(r=l[0],o="default"):(r=l.slice(0,l.length-1).join(":"),o=l[l.length-1]),r.endsWith(".js")&&(r=r.slice(0,-3)),`${r}:${o}`}function La(a,l){return JSON.stringify({udfPath:Ns(a),args:ca(l)})}var T0=Object.defineProperty,O0=(a,l,r)=>l in a?T0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,cn=(a,l,r)=>O0(a,typeof l!="symbol"?l+"":l,r);class R0{constructor(){cn(this,"nextQueryId"),cn(this,"querySetVersion"),cn(this,"querySet"),cn(this,"queryIdToToken"),cn(this,"identityVersion"),cn(this,"auth"),cn(this,"outstandingQueriesOlderThanRestart"),cn(this,"outstandingAuthOlderThanRestart"),cn(this,"paused"),cn(this,"pendingQuerySetModifications"),this.nextQueryId=0,this.querySetVersion=0,this.identityVersion=0,this.querySet=new Map,this.queryIdToToken=new Map,this.outstandingQueriesOlderThanRestart=new Set,this.outstandingAuthOlderThanRestart=!1,this.paused=!1,this.pendingQuerySetModifications=new Map}hasSyncedPastLastReconnect(){return this.outstandingQueriesOlderThanRestart.size===0&&!this.outstandingAuthOlderThanRestart}markAuthCompletion(){this.outstandingAuthOlderThanRestart=!1}subscribe(l,r,o,u){const f=Ns(l),d=La(f,r),v=this.querySet.get(d);if(v!==void 0)return v.numSubscribers+=1,{queryToken:d,modification:null,unsubscribe:()=>this.removeSubscriber(d)};{const m=this.nextQueryId++,g={id:m,canonicalizedUdfPath:f,args:r,numSubscribers:1,journal:o,componentPath:u};this.querySet.set(d,g),this.queryIdToToken.set(m,d);const y=this.querySetVersion,O=this.querySetVersion+1,S={type:"Add",queryId:m,udfPath:f,args:[ca(r)],journal:o,componentPath:u};return this.paused?this.pendingQuerySetModifications.set(m,S):this.querySetVersion=O,{queryToken:d,modification:{type:"ModifyQuerySet",baseVersion:y,newVersion:O,modifications:[S]},unsubscribe:()=>this.removeSubscriber(d)}}}transition(l){for(const r of l.modifications)switch(r.type){case"QueryUpdated":case"QueryFailed":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);const o=r.journal;if(o!==void 0){const u=this.queryIdToToken.get(r.queryId);u!==void 0&&(this.querySet.get(u).journal=o)}break}case"QueryRemoved":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);break}default:throw new Error(`Invalid modification ${r.type}`)}}queryId(l,r){const o=Ns(l),u=La(o,r),f=this.querySet.get(u);return f!==void 0?f.id:null}isCurrentOrNewerAuthVersion(l){return l>=this.identityVersion}setAuth(l){this.auth={tokenType:"User",value:l};const r=this.identityVersion;return this.paused||(this.identityVersion=r+1),{type:"Authenticate",baseVersion:r,...this.auth}}setAdminAuth(l,r){const o={tokenType:"Admin",value:l,impersonating:r};this.auth=o;const u=this.identityVersion;return this.paused||(this.identityVersion=u+1),{type:"Authenticate",baseVersion:u,...o}}clearAuth(){this.auth=void 0,this.markAuthCompletion();const l=this.identityVersion;return this.paused||(this.identityVersion=l+1),{type:"Authenticate",tokenType:"None",baseVersion:l}}hasAuth(){return!!this.auth}isNewAuth(l){return this.auth?.value!==l}queryPath(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).canonicalizedUdfPath:null}queryArgs(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).args:null}queryToken(l){return this.queryIdToToken.get(l)??null}queryJournal(l){return this.querySet.get(l)?.journal}restart(l){this.unpause(),this.outstandingQueriesOlderThanRestart.clear();const r=[];for(const f of this.querySet.values()){const d={type:"Add",queryId:f.id,udfPath:f.canonicalizedUdfPath,args:[ca(f.args)],journal:f.journal,componentPath:f.componentPath};r.push(d),l.has(f.id)||this.outstandingQueriesOlderThanRestart.add(f.id)}this.querySetVersion=1;const o={type:"ModifyQuerySet",baseVersion:0,newVersion:1,modifications:r};if(!this.auth)return this.identityVersion=0,[o,void 0];this.outstandingAuthOlderThanRestart=!0;const u={type:"Authenticate",baseVersion:0,...this.auth};return this.identityVersion=1,[o,u]}pause(){this.paused=!0}resume(){const l=this.pendingQuerySetModifications.size>0?{type:"ModifyQuerySet",baseVersion:this.querySetVersion,newVersion:++this.querySetVersion,modifications:Array.from(this.pendingQuerySetModifications.values())}:void 0,r=this.auth!==void 0?{type:"Authenticate",baseVersion:this.identityVersion++,...this.auth}:void 0;return this.unpause(),[l,r]}unpause(){this.paused=!1,this.pendingQuerySetModifications.clear()}removeSubscriber(l){const r=this.querySet.get(l);if(r.numSubscribers>1)return r.numSubscribers-=1,null;{this.querySet.delete(l),this.queryIdToToken.delete(r.id),this.outstandingQueriesOlderThanRestart.delete(r.id);const o=this.querySetVersion,u=this.querySetVersion+1,f={type:"Remove",queryId:r.id};return this.paused?this.pendingQuerySetModifications.has(r.id)?this.pendingQuerySetModifications.delete(r.id):this.pendingQuerySetModifications.set(r.id,f):this.querySetVersion=u,{type:"ModifyQuerySet",baseVersion:o,newVersion:u,modifications:[f]}}}}var k0=Object.defineProperty,j0=(a,l,r)=>l in a?k0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,ys=(a,l,r)=>j0(a,typeof l!="symbol"?l+"":l,r);class M0{constructor(l){this.logger=l,ys(this,"inflightRequests"),ys(this,"requestsOlderThanRestart"),ys(this,"inflightMutationsCount",0),ys(this,"inflightActionsCount",0),this.inflightRequests=new Map,this.requestsOlderThanRestart=new Set}request(l,r){return new Promise(u=>{const f=r?"Requested":"NotSent";this.inflightRequests.set(l.requestId,{message:l,status:{status:f,requestedAt:new Date,onResult:u}}),l.type==="Mutation"?this.inflightMutationsCount++:l.type==="Action"&&this.inflightActionsCount++})}onResponse(l){const r=this.inflightRequests.get(l.requestId);if(r===void 0||r.status.status==="Completed")return null;const o=r.message.type==="Mutation"?"mutation":"action",u=r.message.udfPath;for(const m of l.logLines)Ls(this.logger,"info",o,u,m);const f=r.status;let d,v;if(l.success)d={success:!0,logLines:l.logLines,value:Ri(l.result)},v=()=>f.onResult(d);else{const m=l.result,{errorData:g}=l;Ls(this.logger,"error",o,u,m),d={success:!1,errorMessage:m,errorData:g!==void 0?Ri(g):void 0,logLines:l.logLines},v=()=>f.onResult(d)}return l.type==="ActionResponse"||!l.success?(v(),this.inflightRequests.delete(l.requestId),this.requestsOlderThanRestart.delete(l.requestId),r.message.type==="Action"?this.inflightActionsCount--:r.message.type==="Mutation"&&this.inflightMutationsCount--,{requestId:l.requestId,result:d}):(r.status={status:"Completed",result:d,ts:l.ts,onResolve:v},null)}removeCompleted(l){const r=new Map;for(const[o,u]of this.inflightRequests.entries()){const f=u.status;f.status==="Completed"&&f.ts.lessThanOrEqual(l)&&(f.onResolve(),r.set(o,f.result),u.message.type==="Mutation"?this.inflightMutationsCount--:u.message.type==="Action"&&this.inflightActionsCount--,this.inflightRequests.delete(o),this.requestsOlderThanRestart.delete(o))}return r}restart(){this.requestsOlderThanRestart=new Set(this.inflightRequests.keys());const l=[];for(const[r,o]of this.inflightRequests){if(o.status.status==="NotSent"){o.status.status="Requested",l.push(o.message);continue}if(o.message.type==="Mutation")l.push(o.message);else if(o.message.type==="Action"){if(this.inflightRequests.delete(r),this.requestsOlderThanRestart.delete(r),this.inflightActionsCount--,o.status.status==="Completed")throw new Error("Action should never be in 'Completed' state");o.status.onResult({success:!1,errorMessage:"Connection lost while action was in flight",logLines:[]})}}return l}resume(){const l=[];for(const[,r]of this.inflightRequests)if(r.status.status==="NotSent"){r.status.status="Requested",l.push(r.message);continue}return l}hasIncompleteRequests(){for(const l of this.inflightRequests.values())if(l.status.status==="Requested")return!0;return!1}hasInflightRequests(){return this.inflightRequests.size>0}hasSyncedPastLastReconnect(){return this.requestsOlderThanRestart.size===0}timeOfOldestInflightRequest(){if(this.inflightRequests.size===0)return null;let l=Date.now();for(const r of this.inflightRequests.values())r.status.status!=="Completed"&&r.status.requestedAt.getTime()<l&&(l=r.status.requestedAt.getTime());return new Date(l)}inflightMutations(){return this.inflightMutationsCount}inflightActions(){return this.inflightActionsCount}}const Il=Symbol.for("functionName"),U0=Symbol.for("toReferencePath");function D0(a){return a[U0]??null}function L0(a){return a.startsWith("function://")}function N0(a){let l;if(typeof a=="string")L0(a)?l={functionHandle:a}:l={name:a};else if(a[Il])l={name:a[Il]};else{const r=D0(a);if(!r)throw new Error(`${a} is not a functionReference`);l={reference:r}}return l}function Lt(a){const l=N0(a);if(l.name===void 0)throw l.functionHandle!==void 0?new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${l.functionHandle}`):l.reference!==void 0?new Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${l.reference}`):new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(l)}`);if(typeof a=="string")return a;const r=a[Il];if(!r)throw new Error(`${a} is not a functionReference`);return r}function Ap(a){return{[Il]:a}}function Cp(a=[]){const l={get(r,o){if(typeof o=="string"){const u=[...a,o];return Cp(u)}else if(o===Il){if(a.length<2){const d=["api",...a].join(".");throw new Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${d}\``)}const u=a.slice(0,-1).join("/"),f=a[a.length-1];return f==="default"?u:u+":"+f}else return o===Symbol.toStringTag?"FunctionReference":void 0}};return new Proxy({},l)}const z0=Cp();var q0=Object.defineProperty,B0=(a,l,r)=>l in a?q0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,zs=(a,l,r)=>B0(a,typeof l!="symbol"?l+"":l,r);class Gl{constructor(l){zs(this,"queryResults"),zs(this,"modifiedQueries"),this.queryResults=l,this.modifiedQueries=[]}getQuery(l,...r){const o=jn(r[0]),u=Lt(l),f=this.queryResults.get(La(u,o));if(f!==void 0)return Gl.queryValue(f.result)}getAllQueries(l){const r=[],o=Lt(l);for(const u of this.queryResults.values())u.udfPath===Ns(o)&&r.push({args:u.args,value:Gl.queryValue(u.result)});return r}setQuery(l,r,o){const u=jn(r),f=Lt(l),d=La(f,u);let v;o===void 0?v=void 0:v={success:!0,value:o,logLines:[]};const m={udfPath:f,args:u,result:v};this.queryResults.set(d,m),this.modifiedQueries.push(d)}static queryValue(l){if(l!==void 0)return l.success?l.value:void 0}}class H0{constructor(){zs(this,"queryResults"),zs(this,"optimisticUpdates"),this.queryResults=new Map,this.optimisticUpdates=[]}ingestQueryResultsFromServer(l,r){this.optimisticUpdates=this.optimisticUpdates.filter(d=>!r.has(d.mutationId));const o=this.queryResults;this.queryResults=new Map(l);const u=new Gl(this.queryResults);for(const d of this.optimisticUpdates)d.update(u);const f=[];for(const[d,v]of this.queryResults){const m=o.get(d);(m===void 0||m.result!==v.result)&&f.push(d)}return f}applyOptimisticUpdate(l,r){this.optimisticUpdates.push({update:l,mutationId:r});const o=new Gl(this.queryResults);return l(o),o.modifiedQueries}rawQueryResult(l){return this.queryResults.get(l)}queryResult(l){const r=this.queryResults.get(l);if(r===void 0)return;const o=r.result;if(o!==void 0){if(o.success)return o.value;throw o.errorData!==void 0?Pc(o,new Qc(Ci("query",r.udfPath,o))):new Error(Ci("query",r.udfPath,o))}}hasQueryResult(l){return this.queryResults.get(l)!==void 0}queryLogs(l){return this.queryResults.get(l)?.result?.logLines}}var V0=Object.defineProperty,Q0=(a,l,r)=>l in a?V0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,yc=(a,l,r)=>Q0(a,typeof l!="symbol"?l+"":l,r);class Dt{constructor(l,r){yc(this,"low"),yc(this,"high"),yc(this,"__isUnsignedLong__"),this.low=l|0,this.high=r|0,this.__isUnsignedLong__=!0}static isLong(l){return(l&&l.__isUnsignedLong__)===!0}static fromBytesLE(l){return new Dt(l[0]|l[1]<<8|l[2]<<16|l[3]<<24,l[4]|l[5]<<8|l[6]<<16|l[7]<<24)}toBytesLE(){const l=this.high,r=this.low;return[r&255,r>>>8&255,r>>>16&255,r>>>24,l&255,l>>>8&255,l>>>16&255,l>>>24]}static fromNumber(l){return isNaN(l)||l<0?Ag:l>=P0?Y0:new Dt(l%Hl|0,l/Hl|0)}toString(){return(BigInt(this.high)*BigInt(Hl)+BigInt(this.low)).toString()}equals(l){return Dt.isLong(l)||(l=Dt.fromValue(l)),this.high>>>31===1&&l.high>>>31===1?!1:this.high===l.high&&this.low===l.low}notEquals(l){return!this.equals(l)}comp(l){return Dt.isLong(l)||(l=Dt.fromValue(l)),this.equals(l)?0:l.high>>>0>this.high>>>0||l.high===this.high&&l.low>>>0>this.low>>>0?-1:1}lessThanOrEqual(l){return this.comp(l)<=0}static fromValue(l){return typeof l=="number"?Dt.fromNumber(l):new Dt(l.low,l.high)}}const Ag=new Dt(0,0),Cg=65536,Hl=Cg*Cg,P0=Hl*Hl,Y0=new Dt(-1,-1);var $0=Object.defineProperty,I0=(a,l,r)=>l in a?$0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,bs=(a,l,r)=>I0(a,typeof l!="symbol"?l+"":l,r);class Tg{constructor(l,r){bs(this,"version"),bs(this,"remoteQuerySet"),bs(this,"queryPath"),bs(this,"logger"),this.version={querySet:0,ts:Dt.fromNumber(0),identity:0},this.remoteQuerySet=new Map,this.queryPath=l,this.logger=r}transition(l){const r=l.startVersion;if(this.version.querySet!==r.querySet||this.version.ts.notEquals(r.ts)||this.version.identity!==r.identity)throw new Error(`Invalid start version: ${r.ts.toString()}:${r.querySet}`);for(const o of l.modifications)switch(o.type){case"QueryUpdated":{const u=this.queryPath(o.queryId);if(u)for(const d of o.logLines)Ls(this.logger,"info","query",u,d);const f=Ri(o.value??null);this.remoteQuerySet.set(o.queryId,{success:!0,value:f,logLines:o.logLines});break}case"QueryFailed":{const u=this.queryPath(o.queryId);if(u)for(const d of o.logLines)Ls(this.logger,"info","query",u,d);const{errorData:f}=o;this.remoteQuerySet.set(o.queryId,{success:!1,errorMessage:o.errorMessage,errorData:f!==void 0?Ri(f):void 0,logLines:o.logLines});break}case"QueryRemoved":{this.remoteQuerySet.delete(o.queryId);break}default:throw new Error(`Invalid modification ${o.type}`)}this.version=l.endVersion}remoteQueryResults(){return this.remoteQuerySet}timestamp(){return this.version.ts}}function bc(a){const l=Yl(a);return Dt.fromBytesLE(Array.from(l))}function G0(a){const l=new Uint8Array(a.toBytesLE());return $l(l)}function X0(a){switch(a.type){case"FatalError":case"AuthError":case"ActionResponse":case"Ping":return{...a};case"MutationResponse":return a.success?{...a,ts:bc(a.ts)}:{...a};case"Transition":return{...a,startVersion:{...a.startVersion,ts:bc(a.startVersion.ts)},endVersion:{...a.endVersion,ts:bc(a.endVersion.ts)}}}}function W0(a){switch(a.type){case"Authenticate":case"ModifyQuerySet":case"Mutation":case"Action":case"Event":return{...a};case"Connect":return a.maxObservedTimestamp!==void 0?{...a,maxObservedTimestamp:G0(a.maxObservedTimestamp)}:{...a,maxObservedTimestamp:void 0}}}var K0=Object.defineProperty,Z0=(a,l,r)=>l in a?K0(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,ht=(a,l,r)=>Z0(a,typeof l!="symbol"?l+"":l,r);const F0=1e3,J0=1001,eS=1005,tS=4040,Tp={InternalServerError:{timeout:1e3},SubscriptionsWorkerFullError:{timeout:3e3},TooManyConcurrentRequests:{timeout:3e3},CommitterFullError:{timeout:3e3},AwsTooManyRequestsException:{timeout:3e3},ExecuteFullError:{timeout:3e3},SystemTimeoutError:{timeout:3e3},ExpiredInQueue:{timeout:3e3},VectorIndexesUnavailable:{timeout:1e3},SearchIndexesUnavailable:{timeout:1e3},VectorIndexTooLarge:{timeout:3e3},SearchIndexTooLarge:{timeout:3e3},TooManyWritesInTimePeriod:{timeout:3e3}};function nS(a){if(a===void 0)return"Unknown";for(const l of Object.keys(Tp))if(a.startsWith(l))return l;return"Unknown"}class aS{constructor(l,r,o,u){ht(this,"socket"),ht(this,"connectionCount"),ht(this,"_hasEverConnected",!1),ht(this,"lastCloseReason"),ht(this,"defaultInitialBackoff"),ht(this,"maxBackoff"),ht(this,"retries"),ht(this,"serverInactivityThreshold"),ht(this,"reconnectDueToServerInactivityTimeout"),ht(this,"uri"),ht(this,"onOpen"),ht(this,"onResume"),ht(this,"onMessage"),ht(this,"webSocketConstructor"),ht(this,"logger"),ht(this,"onServerDisconnectError"),this.webSocketConstructor=o,this.socket={state:"disconnected"},this.connectionCount=0,this.lastCloseReason="InitialConnect",this.defaultInitialBackoff=1e3,this.maxBackoff=16e3,this.retries=0,this.serverInactivityThreshold=3e4,this.reconnectDueToServerInactivityTimeout=null,this.uri=l,this.onOpen=r.onOpen,this.onResume=r.onResume,this.onMessage=r.onMessage,this.onServerDisconnectError=r.onServerDisconnectError,this.logger=u,this.connect()}setSocketState(l){this.socket=l,this._logVerbose(`socket state changed: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}`)}connect(){if(this.socket.state==="terminated")return;if(this.socket.state!=="disconnected"&&this.socket.state!=="stopped")throw new Error("Didn't start connection from disconnected state: "+this.socket.state);const l=new this.webSocketConstructor(this.uri);this._logVerbose("constructed WebSocket"),this.setSocketState({state:"connecting",ws:l,paused:"no"}),this.resetServerInactivityTimeout(),l.onopen=()=>{if(this.logger.logVerbose("begin ws.onopen"),this.socket.state!=="connecting")throw new Error("onopen called with socket not in connecting state");this.setSocketState({state:"ready",ws:l,paused:this.socket.paused==="yes"?"uninitialized":"no"}),this.resetServerInactivityTimeout(),this.socket.paused==="no"&&(this._hasEverConnected=!0,this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})),this.lastCloseReason!=="InitialConnect"&&this.logger.log("WebSocket reconnected"),this.connectionCount+=1,this.lastCloseReason=null},l.onerror=r=>{const o=r.message;this.logger.log(`WebSocket error: ${o}`)},l.onmessage=r=>{this.resetServerInactivityTimeout();const o=X0(JSON.parse(r.data));this._logVerbose(`received ws message with type ${o.type}`),this.onMessage(o).hasSyncedPastLastReconnect&&(this.retries=0)},l.onclose=r=>{if(this._logVerbose("begin ws.onclose"),this.lastCloseReason===null&&(this.lastCloseReason=r.reason??"OnCloseInvoked"),r.code!==F0&&r.code!==J0&&r.code!==eS&&r.code!==tS){let u=`WebSocket closed with code ${r.code}`;r.reason&&(u+=`: ${r.reason}`),this.logger.log(u),this.onServerDisconnectError&&r.reason&&this.onServerDisconnectError(u)}const o=nS(r.reason);this.scheduleReconnect(o)}}socketState(){return this.socket.state}sendMessage(l){const r={type:l.type,...l.type==="Authenticate"&&l.tokenType==="User"?{value:`...${l.value.slice(-7)}`}:{}};if(this.socket.state==="ready"&&this.socket.paused==="no"){const o=W0(l),u=JSON.stringify(o);try{this.socket.ws.send(u)}catch(f){this.logger.log(`Failed to send message on WebSocket, reconnecting: ${f}`),this.closeAndReconnect("FailedToSendMessage")}return this._logVerbose(`sent message with type ${l.type}: ${JSON.stringify(r)}`),!0}return this._logVerbose(`message not sent (socket state: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}): ${JSON.stringify(r)}`),!1}resetServerInactivityTimeout(){this.socket.state!=="terminated"&&(this.reconnectDueToServerInactivityTimeout!==null&&(clearTimeout(this.reconnectDueToServerInactivityTimeout),this.reconnectDueToServerInactivityTimeout=null),this.reconnectDueToServerInactivityTimeout=setTimeout(()=>{this.closeAndReconnect("InactiveServer")},this.serverInactivityThreshold))}scheduleReconnect(l){this.socket={state:"disconnected"};const r=this.nextBackoff(l);this.logger.log(`Attempting reconnect in ${r}ms`),setTimeout(()=>this.connect(),r)}closeAndReconnect(l){switch(this._logVerbose(`begin closeAndReconnect with reason ${l}`),this.socket.state){case"disconnected":case"terminated":case"stopped":return;case"connecting":case"ready":{this.lastCloseReason=l,this.close(),this.scheduleReconnect("client");return}default:this.socket}}close(){switch(this.socket.state){case"disconnected":case"terminated":case"stopped":return Promise.resolve();case"connecting":{const l=this.socket.ws;return new Promise(r=>{l.onclose=()=>{this._logVerbose("Closed after connecting"),r()},l.onopen=()=>{this._logVerbose("Opened after connecting"),l.close()}})}case"ready":{this._logVerbose("ws.close called");const l=this.socket.ws,r=new Promise(o=>{l.onclose=()=>{o()}});return l.close(),r}default:return this.socket,Promise.resolve()}}terminate(){switch(this.reconnectDueToServerInactivityTimeout&&clearTimeout(this.reconnectDueToServerInactivityTimeout),this.socket.state){case"terminated":case"stopped":case"disconnected":case"connecting":case"ready":{const l=this.close();return this.setSocketState({state:"terminated"}),l}default:throw this.socket,new Error(`Invalid websocket state: ${this.socket.state}`)}}stop(){switch(this.socket.state){case"terminated":return Promise.resolve();case"connecting":case"stopped":case"disconnected":case"ready":{const l=this.close();return this.socket={state:"stopped"},l}default:return this.socket,Promise.resolve()}}tryRestart(){switch(this.socket.state){case"stopped":break;case"terminated":case"connecting":case"ready":case"disconnected":this.logger.logVerbose("Restart called without stopping first");return;default:this.socket}this.connect()}pause(){switch(this.socket.state){case"disconnected":case"stopped":case"terminated":return;case"connecting":case"ready":{this.socket={...this.socket,paused:"yes"};return}default:{this.socket;return}}}resume(){switch(this.socket.state){case"connecting":this.socket={...this.socket,paused:"no"};return;case"ready":this.socket.paused==="uninitialized"?(this.socket={...this.socket,paused:"no"},this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})):this.socket.paused==="yes"&&(this.socket={...this.socket,paused:"no"},this.onResume());return;case"terminated":case"stopped":case"disconnected":return;default:this.socket}this.connect()}connectionState(){return{isConnected:this.socket.state==="ready",hasEverConnected:this._hasEverConnected,connectionCount:this.connectionCount,connectionRetries:this.retries}}_logVerbose(l){this.logger.logVerbose(l)}nextBackoff(l){const o=(l==="client"?100:l==="Unknown"?this.defaultInitialBackoff:Tp[l].timeout)*Math.pow(2,this.retries);this.retries+=1;const u=Math.min(o,this.maxBackoff),f=u*(Math.random()-.5);return u+f}}function iS(){return lS()}function lS(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,a=>{const l=Math.random()*16|0;return(a==="x"?l:l&3|8).toString(16)})}class ql extends Error{}ql.prototype.name="InvalidTokenError";function rS(a){return decodeURIComponent(atob(a).replace(/(.)/g,(l,r)=>{let o=r.charCodeAt(0).toString(16).toUpperCase();return o.length<2&&(o="0"+o),"%"+o}))}function sS(a){let l=a.replace(/-/g,"+").replace(/_/g,"/");switch(l.length%4){case 0:break;case 2:l+="==";break;case 3:l+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return rS(l)}catch{return atob(l)}}function oS(a,l){if(typeof a!="string")throw new ql("Invalid token specified: must be a string");l||(l={});const r=l.header===!0?0:1,o=a.split(".")[r];if(typeof o!="string")throw new ql(`Invalid token specified: missing part #${r+1}`);let u;try{u=sS(o)}catch(f){throw new ql(`Invalid token specified: invalid base64 for part #${r+1} (${f.message})`)}try{return JSON.parse(u)}catch(f){throw new ql(`Invalid token specified: invalid json for part #${r+1} (${f.message})`)}}var uS=Object.defineProperty,cS=(a,l,r)=>l in a?uS(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,$t=(a,l,r)=>cS(a,typeof l!="symbol"?l+"":l,r);const fS=20*24*60*60*1e3,Og=2;class dS{constructor(l,r,o){$t(this,"authState",{state:"noAuth"}),$t(this,"configVersion",0),$t(this,"syncState"),$t(this,"authenticate"),$t(this,"stopSocket"),$t(this,"tryRestartSocket"),$t(this,"pauseSocket"),$t(this,"resumeSocket"),$t(this,"clearAuth"),$t(this,"logger"),$t(this,"refreshTokenLeewaySeconds"),$t(this,"tokenConfirmationAttempts",0),this.syncState=l,this.authenticate=r.authenticate,this.stopSocket=r.stopSocket,this.tryRestartSocket=r.tryRestartSocket,this.pauseSocket=r.pauseSocket,this.resumeSocket=r.resumeSocket,this.clearAuth=r.clearAuth,this.logger=o.logger,this.refreshTokenLeewaySeconds=o.refreshTokenLeewaySeconds}async setConfig(l,r){this.resetAuthState(),this._logVerbose("pausing WS for auth token fetch"),this.pauseSocket();const o=await this.fetchTokenAndGuardAgainstRace(l,{forceRefreshToken:!1});o.isFromOutdatedConfig||(o.value?(this.setAuthState({state:"waitingForServerConfirmationOfCachedToken",config:{fetchToken:l,onAuthChange:r},hasRetried:!1}),this.authenticate(o.value)):(this.setAuthState({state:"initialRefetch",config:{fetchToken:l,onAuthChange:r}}),await this.refetchToken()),this._logVerbose("resuming WS after auth token fetch"),this.resumeSocket())}onTransition(l){if(this.syncState.isCurrentOrNewerAuthVersion(l.endVersion.identity)&&!(l.endVersion.identity<=l.startVersion.identity)){if(this.authState.state==="waitingForServerConfirmationOfCachedToken"){this._logVerbose("server confirmed auth token is valid"),this.refetchToken(),this.authState.config.onAuthChange(!0);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this._logVerbose("server confirmed new auth token is valid"),this.scheduleTokenRefetch(this.authState.token),this.tokenConfirmationAttempts=0,this.authState.hadAuth||this.authState.config.onAuthChange(!0))}}onAuthError(l){if(l.authUpdateAttempted===!1&&(this.authState.state==="waitingForServerConfirmationOfFreshToken"||this.authState.state==="waitingForServerConfirmationOfCachedToken")){this._logVerbose("ignoring non-auth token expired error");return}const{baseVersion:r}=l;if(!this.syncState.isCurrentOrNewerAuthVersion(r+1)){this._logVerbose("ignoring auth error for previous auth attempt");return}this.tryToReauthenticate(l)}async tryToReauthenticate(l){if(this._logVerbose(`attempting to reauthenticate: ${l.error}`),this.authState.state==="noAuth"||this.authState.state==="waitingForServerConfirmationOfFreshToken"&&this.tokenConfirmationAttempts>=Og){this.logger.error(`Failed to authenticate: "${l.error}", check your server auth config`),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.authState.state!=="noAuth"&&this.setAndReportAuthFailed(this.authState.config.onAuthChange);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this.tokenConfirmationAttempts++,this._logVerbose(`retrying reauthentication, ${Og-this.tokenConfirmationAttempts} attempts remaining`)),await this.stopSocket();const r=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});r.isFromOutdatedConfig||(r.value&&this.syncState.isNewAuth(r.value)?(this.authenticate(r.value),this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",config:this.authState.config,token:r.value,hadAuth:this.authState.state==="notRefetching"||this.authState.state==="waitingForScheduledRefetch"})):(this._logVerbose("reauthentication failed, could not fetch a new token"),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this.tryRestartSocket())}async refetchToken(){if(this.authState.state==="noAuth")return;this._logVerbose("refetching auth token");const l=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});l.isFromOutdatedConfig||(l.value?this.syncState.isNewAuth(l.value)?(this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",hadAuth:this.syncState.hasAuth(),token:l.value,config:this.authState.config}),this.authenticate(l.value)):this.setAuthState({state:"notRefetching",config:this.authState.config}):(this._logVerbose("refetching token failed"),this.syncState.hasAuth()&&this.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this._logVerbose("restarting WS after auth token fetch (if currently stopped)"),this.tryRestartSocket())}scheduleTokenRefetch(l){if(this.authState.state==="noAuth")return;const r=this.decodeToken(l);if(!r){this.logger.error("Auth token is not a valid JWT, cannot refetch the token");return}const{iat:o,exp:u}=r;if(!o||!u){this.logger.error("Auth token does not have required fields, cannot refetch the token");return}const f=u-o;if(f<=2){this.logger.error("Auth token does not live long enough, cannot refetch the token");return}let d=Math.min(fS,(f-this.refreshTokenLeewaySeconds)*1e3);d<=0&&(this.logger.warn(`Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${f}s`),d=0);const v=setTimeout(()=>{this._logVerbose("running scheduled token refetch"),this.refetchToken()},d);this.setAuthState({state:"waitingForScheduledRefetch",refetchTokenTimeoutId:v,config:this.authState.config}),this._logVerbose(`scheduled preemptive auth token refetching in ${d}ms`)}async fetchTokenAndGuardAgainstRace(l,r){const o=++this.configVersion;this._logVerbose(`fetching token with config version ${o}`);const u=await l(r);return this.configVersion!==o?(this._logVerbose(`stale config version, expected ${o}, got ${this.configVersion}`),{isFromOutdatedConfig:!0}):{isFromOutdatedConfig:!1,value:u}}stop(){this.resetAuthState(),this.configVersion++,this._logVerbose(`config version bumped to ${this.configVersion}`)}setAndReportAuthFailed(l){l(!1),this.resetAuthState()}resetAuthState(){this.setAuthState({state:"noAuth"})}setAuthState(l){const r=l.state==="waitingForServerConfirmationOfFreshToken"?{hadAuth:l.hadAuth,state:l.state,token:`...${l.token.slice(-7)}`}:{state:l.state};switch(this._logVerbose(`setting auth state to ${JSON.stringify(r)}`),l.state){case"waitingForScheduledRefetch":case"notRefetching":case"noAuth":this.tokenConfirmationAttempts=0;break}this.authState.state==="waitingForScheduledRefetch"&&(clearTimeout(this.authState.refetchTokenTimeoutId),this.syncState.markAuthCompletion()),this.authState=l}decodeToken(l){try{return oS(l)}catch(r){return this._logVerbose(`Error decoding token: ${r instanceof Error?r.message:"Unknown error"}`),null}}_logVerbose(l){this.logger.logVerbose(`${l} [v${this.configVersion}]`)}}const hS=["convexClientConstructed","convexWebSocketOpen","convexFirstMessageReceived"];function mS(a,l){const r={sessionId:l};typeof performance>"u"||!performance.mark||performance.mark(a,{detail:r})}function gS(a){let l=a.name.slice(6);return l=l.charAt(0).toLowerCase()+l.slice(1),{name:l,startTime:a.startTime}}function pS(a){if(typeof performance>"u"||!performance.getEntriesByName)return[];const l=[];for(const r of hS){const o=performance.getEntriesByName(r).filter(u=>u.entryType==="mark").filter(u=>u.detail.sessionId===a);l.push(...o)}return l.map(gS)}var vS=Object.defineProperty,yS=(a,l,r)=>l in a?vS(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,mt=(a,l,r)=>yS(a,typeof l!="symbol"?l+"":l,r);class bS{constructor(l,r,o){if(mt(this,"address"),mt(this,"state"),mt(this,"requestManager"),mt(this,"webSocketManager"),mt(this,"authenticationManager"),mt(this,"remoteQuerySet"),mt(this,"optimisticQueryResults"),mt(this,"_transitionHandlerCounter",0),mt(this,"_nextRequestId"),mt(this,"_onTransitionFns",new Map),mt(this,"_sessionId"),mt(this,"firstMessageReceived",!1),mt(this,"debug"),mt(this,"logger"),mt(this,"maxObservedTimestamp"),mt(this,"mark",S=>{this.debug&&mS(S,this.sessionId)}),typeof l=="object")throw new Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");o?.skipConvexDeploymentUrlCheck!==!0&&f0(l),o={...o};const u=o.authRefreshTokenLeewaySeconds??2;let f=o.webSocketConstructor;if(!f&&typeof WebSocket>"u")throw new Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");f=f||WebSocket,this.debug=o.reportDebugInfoToConvex??!1,this.address=l,this.logger=o.logger===!1?wp({verbose:o.verbose??!1}):o.logger!==!0&&o.logger?o.logger:xp({verbose:o.verbose??!1});const d=l.search("://");if(d===-1)throw new Error("Provided address was not an absolute URL.");const v=l.substring(d+3),m=l.substring(0,d);let g;if(m==="http")g="ws";else if(m==="https")g="wss";else throw new Error(`Unknown parent protocol ${m}`);const y=`${g}://${v}/api/${xg}/sync`;this.state=new R0,this.remoteQuerySet=new Tg(S=>this.state.queryPath(S),this.logger),this.requestManager=new M0(this.logger),this.authenticationManager=new dS(this.state,{authenticate:S=>{const R=this.state.setAuth(S);return this.webSocketManager.sendMessage(R),R.baseVersion},stopSocket:()=>this.webSocketManager.stop(),tryRestartSocket:()=>this.webSocketManager.tryRestart(),pauseSocket:()=>{this.webSocketManager.pause(),this.state.pause()},resumeSocket:()=>this.webSocketManager.resume(),clearAuth:()=>{this.clearAuth()}},{logger:this.logger,refreshTokenLeewaySeconds:u}),this.optimisticQueryResults=new H0,this.addOnTransitionHandler(S=>{r(S.queries.map(R=>R.token))}),this._nextRequestId=0,this._sessionId=iS();const{unsavedChangesWarning:O}=o;if(typeof window>"u"||typeof window.addEventListener>"u"){if(O===!0)throw new Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.")}else O!==!1&&window.addEventListener("beforeunload",S=>{if(this.requestManager.hasIncompleteRequests()){S.preventDefault();const R="Are you sure you want to leave? Your changes may not be saved.";return(S||window.event).returnValue=R,R}});this.webSocketManager=new aS(y,{onOpen:S=>{this.mark("convexWebSocketOpen"),this.webSocketManager.sendMessage({...S,type:"Connect",sessionId:this._sessionId,maxObservedTimestamp:this.maxObservedTimestamp});const R=new Set(this.remoteQuerySet.remoteQueryResults().keys());this.remoteQuerySet=new Tg(N=>this.state.queryPath(N),this.logger);const[A,L]=this.state.restart(R);L&&this.webSocketManager.sendMessage(L),this.webSocketManager.sendMessage(A);for(const N of this.requestManager.restart())this.webSocketManager.sendMessage(N)},onResume:()=>{const[S,R]=this.state.resume();R&&this.webSocketManager.sendMessage(R),S&&this.webSocketManager.sendMessage(S);for(const A of this.requestManager.resume())this.webSocketManager.sendMessage(A)},onMessage:S=>{switch(this.firstMessageReceived||(this.firstMessageReceived=!0,this.mark("convexFirstMessageReceived"),this.reportMarks()),S.type){case"Transition":{this.observedTimestamp(S.endVersion.ts),this.authenticationManager.onTransition(S),this.remoteQuerySet.transition(S),this.state.transition(S);const R=this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());this.notifyOnQueryResultChanges(R);break}case"MutationResponse":{S.success&&this.observedTimestamp(S.ts);const R=this.requestManager.onResponse(S);R!==null&&this.notifyOnQueryResultChanges(new Map([[R.requestId,R.result]]));break}case"ActionResponse":{this.requestManager.onResponse(S);break}case"AuthError":{this.authenticationManager.onAuthError(S);break}case"FatalError":{const R=C0(this.logger,S.error);throw this.webSocketManager.terminate(),R}}return{hasSyncedPastLastReconnect:this.hasSyncedPastLastReconnect()}},onServerDisconnectError:o.onServerDisconnectError},f,this.logger),this.mark("convexClientConstructed")}hasSyncedPastLastReconnect(){return this.requestManager.hasSyncedPastLastReconnect()||this.state.hasSyncedPastLastReconnect()}observedTimestamp(l){(this.maxObservedTimestamp===void 0||this.maxObservedTimestamp.lessThanOrEqual(l))&&(this.maxObservedTimestamp=l)}getMaxObservedTimestamp(){return this.maxObservedTimestamp}notifyOnQueryResultChanges(l){const r=this.remoteQuerySet.remoteQueryResults(),o=new Map;for(const[f,d]of r){const v=this.state.queryToken(f);if(v!==null){const m={result:d,udfPath:this.state.queryPath(f),args:this.state.queryArgs(f)};o.set(v,m)}}const u=this.optimisticQueryResults.ingestQueryResultsFromServer(o,new Set(l.keys()));this.handleTransition({queries:u.map(f=>{const d=this.optimisticQueryResults.rawQueryResult(f);return{token:f,modification:{kind:"Updated",result:d.result}}}),reflectedMutations:Array.from(l).map(([f,d])=>({requestId:f,result:d})),timestamp:this.remoteQuerySet.timestamp()})}handleTransition(l){for(const r of this._onTransitionFns.values())r(l)}addOnTransitionHandler(l){const r=this._transitionHandlerCounter++;return this._onTransitionFns.set(r,l),()=>this._onTransitionFns.delete(r)}setAuth(l,r){this.authenticationManager.setConfig(l,r)}hasAuth(){return this.state.hasAuth()}setAdminAuth(l,r){const o=this.state.setAdminAuth(l,r);this.webSocketManager.sendMessage(o)}clearAuth(){const l=this.state.clearAuth();this.webSocketManager.sendMessage(l)}subscribe(l,r,o){const u=jn(r),{modification:f,queryToken:d,unsubscribe:v}=this.state.subscribe(l,u,o?.journal,o?.componentPath);return f!==null&&this.webSocketManager.sendMessage(f),{queryToken:d,unsubscribe:()=>{const m=v();m&&this.webSocketManager.sendMessage(m)}}}localQueryResult(l,r){const o=jn(r),u=La(l,o);return this.optimisticQueryResults.queryResult(u)}localQueryResultByToken(l){return this.optimisticQueryResults.queryResult(l)}hasLocalQueryResultByToken(l){return this.optimisticQueryResults.hasQueryResult(l)}localQueryLogs(l,r){const o=jn(r),u=La(l,o);return this.optimisticQueryResults.queryLogs(u)}queryJournal(l,r){const o=jn(r),u=La(l,o);return this.state.queryJournal(u)}connectionState(){const l=this.webSocketManager.connectionState();return{hasInflightRequests:this.requestManager.hasInflightRequests(),isWebSocketConnected:l.isConnected,hasEverConnected:l.hasEverConnected,connectionCount:l.connectionCount,connectionRetries:l.connectionRetries,timeOfOldestInflightRequest:this.requestManager.timeOfOldestInflightRequest(),inflightMutations:this.requestManager.inflightMutations(),inflightActions:this.requestManager.inflightActions()}}async mutation(l,r,o){const u=await this.mutationInternal(l,r,o);if(!u.success)throw u.errorData!==void 0?Pc(u,new Qc(Ci("mutation",l,u))):new Error(Ci("mutation",l,u));return u.value}async mutationInternal(l,r,o,u){const{mutationPromise:f}=this.enqueueMutation(l,r,o,u);return f}enqueueMutation(l,r,o,u){const f=jn(r);this.tryReportLongDisconnect();const d=this.nextRequestId;if(this._nextRequestId++,o!==void 0){const y=o.optimisticUpdate;if(y!==void 0){const O=A=>{y(A,f)instanceof Promise&&this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.")},R=this.optimisticQueryResults.applyOptimisticUpdate(O,d).map(A=>{const L=this.localQueryResultByToken(A);return{token:A,modification:{kind:"Updated",result:L===void 0?void 0:{success:!0,value:L,logLines:[]}}}});this.handleTransition({queries:R,reflectedMutations:[],timestamp:this.remoteQuerySet.timestamp()})}}const v={type:"Mutation",requestId:d,udfPath:l,componentPath:u,args:[ca(f)]},m=this.webSocketManager.sendMessage(v),g=this.requestManager.request(v,m);return{requestId:d,mutationPromise:g}}async action(l,r){const o=await this.actionInternal(l,r);if(!o.success)throw o.errorData!==void 0?Pc(o,new Qc(Ci("action",l,o))):new Error(Ci("action",l,o));return o.value}async actionInternal(l,r,o){const u=jn(r),f=this.nextRequestId;this._nextRequestId++,this.tryReportLongDisconnect();const d={type:"Action",requestId:f,udfPath:l,componentPath:o,args:[ca(u)]},v=this.webSocketManager.sendMessage(d);return this.requestManager.request(d,v)}async close(){return this.authenticationManager.stop(),this.webSocketManager.terminate()}get url(){return this.address}get nextRequestId(){return this._nextRequestId}get sessionId(){return this._sessionId}reportMarks(){if(this.debug){const l=pS(this.sessionId);this.webSocketManager.sendMessage({type:"Event",eventType:"ClientConnect",event:l})}}tryReportLongDisconnect(){if(!this.debug)return;const l=this.connectionState().timeOfOldestInflightRequest;if(l===null||Date.now()-l.getTime()<=60*1e3)return;const r=`${this.address}/api/debug_event`;fetch(r,{method:"POST",headers:{"Content-Type":"application/json","Convex-Client":`npm-${xg}`},body:JSON.stringify({event:"LongWebsocketDisconnect"})}).then(o=>{o.ok||this.logger.warn("Analytics request failed with response:",o.body)}).catch(o=>{this.logger.warn("Analytics response failed with error:",o)})}}var SS=Object.defineProperty,_S=(a,l,r)=>l in a?SS(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,la=(a,l,r)=>_S(a,typeof l!="symbol"?l+"":l,r);if(typeof q>"u")throw new Error("Required dependency 'react' not found");function Op(a,l,r){function o(u){return AS(u),l.mutation(a,u,{optimisticUpdate:r})}return o.withOptimisticUpdate=function(f){if(r!==void 0)throw new Error(`Already specified optimistic update for mutation ${Lt(a)}`);return Op(a,l,f)},o}class ES{constructor(l,r){if(la(this,"address"),la(this,"cachedSync"),la(this,"listeners"),la(this,"options"),la(this,"closed",!1),la(this,"_logger"),la(this,"adminAuth"),la(this,"fakeUserIdentity"),l===void 0)throw new Error("No address provided to ConvexReactClient.\nIf trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\nIf running locally, make sure to run `convex dev` and ensure the .env.local file is populated.");if(typeof l!="string")throw new Error(`ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof l} instead.`);if(!l.includes("://"))throw new Error("Provided address was not an absolute URL.");this.address=l,this.listeners=new Map,this._logger=r?.logger===!1?wp({verbose:r?.verbose??!1}):r?.logger!==!0&&r?.logger?r.logger:xp({verbose:r?.verbose??!1}),this.options={...r,logger:this._logger}}get url(){return this.address}get sync(){if(this.closed)throw new Error("ConvexReactClient has already been closed.");return this.cachedSync?this.cachedSync:(this.cachedSync=new bS(this.address,l=>this.transition(l),this.options),this.adminAuth&&this.cachedSync.setAdminAuth(this.adminAuth,this.fakeUserIdentity),this.cachedSync)}setAuth(l,r){if(typeof l=="string")throw new Error("Passing a string to ConvexReactClient.setAuth is no longer supported, please upgrade to passing in an async function to handle reauthentication.");this.sync.setAuth(l,r??(()=>{}))}clearAuth(){this.sync.clearAuth()}setAdminAuth(l,r){if(this.adminAuth=l,this.fakeUserIdentity=r,this.closed)throw new Error("ConvexReactClient has already been closed.");this.cachedSync&&this.sync.setAdminAuth(l,r)}watchQuery(l,...r){const[o,u]=r,f=Lt(l);return{onUpdate:d=>{const{queryToken:v,unsubscribe:m}=this.sync.subscribe(f,o,u),g=this.listeners.get(v);return g!==void 0?g.add(d):this.listeners.set(v,new Set([d])),()=>{if(this.closed)return;const y=this.listeners.get(v);y.delete(d),y.size===0&&this.listeners.delete(v),m()}},localQueryResult:()=>{if(this.cachedSync)return this.cachedSync.localQueryResult(f,o)},localQueryLogs:()=>{if(this.cachedSync)return this.cachedSync.localQueryLogs(f,o)},journal:()=>{if(this.cachedSync)return this.cachedSync.queryJournal(f,o)}}}mutation(l,...r){const[o,u]=r,f=Lt(l);return this.sync.mutation(f,o,u)}action(l,...r){const o=Lt(l);return this.sync.action(o,...r)}query(l,...r){const o=this.watchQuery(l,...r),u=o.localQueryResult();return u!==void 0?Promise.resolve(u):new Promise((f,d)=>{const v=o.onUpdate(()=>{v();try{f(o.localQueryResult())}catch(m){d(m)}})})}connectionState(){return this.sync.connectionState()}get logger(){return this._logger}async close(){if(this.closed=!0,this.listeners=new Map,this.cachedSync){const l=this.cachedSync;this.cachedSync=void 0,await l.close()}}transition(l){for(const r of l){const o=this.listeners.get(r);if(o)for(const u of o)u()}}}const of=q.createContext(void 0);function xS(){return w.useContext(of)}const wS=({client:a,children:l})=>q.createElement(of.Provider,{value:a},l);function uw(a,...l){const r=l[0]==="skip",o=l[0]==="skip"?{}:jn(l[0]),u=typeof a=="string"?Ap(a):a,f=Lt(u),d=w.useMemo(()=>r?{}:{query:{query:u,args:o}},[JSON.stringify(ca(o)),f,r]),m=kS(d).query;if(m instanceof Error)throw m;return m}function Rg(a){const l=typeof a=="string"?Ap(a):a,r=w.useContext(of);if(r===void 0)throw new Error("Could not find Convex client! `useMutation` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return w.useMemo(()=>Op(l,r),[r,Lt(l)])}function AS(a){if(typeof a=="object"&&a!==null&&"bubbles"in a&&"persist"in a&&"isDefaultPrevented"in a)throw new Error("Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like `const handler = () => myMutation();` and using `handler` in the event handler.")}var CS=Object.defineProperty,TS=(a,l,r)=>l in a?CS(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,Sc=(a,l,r)=>TS(a,typeof l!="symbol"?l+"":l,r);class OS{constructor(l){Sc(this,"createWatch"),Sc(this,"queries"),Sc(this,"listeners"),this.createWatch=l,this.queries={},this.listeners=new Set}setQueries(l){for(const r of Object.keys(l)){const{query:o,args:u}=l[r];if(Lt(o),this.queries[r]===void 0)this.addQuery(r,o,u);else{const f=this.queries[r];(Lt(o)!==Lt(f.query)||JSON.stringify(ca(u))!==JSON.stringify(ca(f.args)))&&(this.removeQuery(r),this.addQuery(r,o,u))}}for(const r of Object.keys(this.queries))l[r]===void 0&&this.removeQuery(r)}subscribe(l){return this.listeners.add(l),()=>{this.listeners.delete(l)}}getLocalResults(l){const r={};for(const o of Object.keys(l)){const{query:u,args:f}=l[o];Lt(u);const d=this.createWatch(u,f);let v;try{v=d.localQueryResult()}catch(m){if(m instanceof Error)v=m;else throw m}r[o]=v}return r}setCreateWatch(l){this.createWatch=l;for(const r of Object.keys(this.queries)){const{query:o,args:u,watch:f}=this.queries[r],d=f.journal();this.removeQuery(r),this.addQuery(r,o,u,d)}}destroy(){for(const l of Object.keys(this.queries))this.removeQuery(l);this.listeners=new Set}addQuery(l,r,o,u){if(this.queries[l]!==void 0)throw new Error(`Tried to add a new query with identifier ${l} when it already exists.`);const f=this.createWatch(r,o,u),d=f.onUpdate(()=>this.notifyListeners());this.queries[l]={query:r,args:o,watch:f,unsubscribe:d}}removeQuery(l){const r=this.queries[l];if(r===void 0)throw new Error(`No query found with identifier ${l}.`);r.unsubscribe(),delete this.queries[l]}notifyListeners(){for(const l of this.listeners)l()}}function RS({getCurrentValue:a,subscribe:l}){const[r,o]=w.useState(()=>({getCurrentValue:a,subscribe:l,value:a()}));let u=r.value;return(r.getCurrentValue!==a||r.subscribe!==l)&&(u=a(),o({getCurrentValue:a,subscribe:l,value:u})),w.useEffect(()=>{let f=!1;const d=()=>{f||o(m=>{if(m.getCurrentValue!==a||m.subscribe!==l)return m;const g=a();return m.value===g?m:{...m,value:g}})},v=l(d);return d(),()=>{f=!0,v()}},[a,l]),u}function kS(a){const l=xS();if(l===void 0)throw new Error("Could not find Convex client! `useQuery` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");const r=w.useMemo(()=>(o,u,f)=>l.watchQuery(o,u,{journal:f}),[l]);return jS(a,r)}function jS(a,l){const[r]=w.useState(()=>new OS(l));r.createWatch!==l&&r.setCreateWatch(l),w.useEffect(()=>()=>r.destroy(),[r]);const o=w.useMemo(()=>({getCurrentValue:()=>r.getLocalResults(a),subscribe:u=>(r.setQueries(a),r.subscribe(u))}),[r,a]);return RS(o)}var MS=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function Rp({packageName:a,customMessages:l}){let r=a;const o={...MS,...l};function u(f,d){if(!d)return`${r}: ${f}`;let v=f;const m=f.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const g of m){const y=(d[g[1]]||"").toString();v=v.replace(`{{${g[1]}}}`,y)}return`${r}: ${v}`}return{setPackageName({packageName:f}){return typeof f=="string"&&(r=f),this},setMessages({customMessages:f}){return Object.assign(o,f||{}),this},throwInvalidPublishableKeyError(f){throw new Error(u(o.InvalidPublishableKeyErrorMessage,f))},throwInvalidProxyUrl(f){throw new Error(u(o.InvalidProxyUrlErrorMessage,f))},throwMissingPublishableKeyError(){throw new Error(u(o.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(u(o.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(f){throw new Error(u(o.MissingClerkProvider,f))},throw(f){throw new Error(u(f))}}}var kp=Object.defineProperty,US=Object.getOwnPropertyDescriptor,DS=Object.getOwnPropertyNames,LS=Object.prototype.hasOwnProperty,NS=(a,l)=>{for(var r in l)kp(a,r,{get:l[r],enumerable:!0})},zS=(a,l,r,o)=>{if(l&&typeof l=="object"||typeof l=="function")for(let u of DS(l))!LS.call(a,u)&&u!==r&&kp(a,u,{get:()=>l[u],enumerable:!(o=US(l,u))||o.enumerable});return a},qS=(a,l,r)=>(zS(a,l,"default"),r),BS={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},HS=new Set(["first_factor","second_factor","multi_factor"]),VS=new Set(["strict_mfa","strict","moderate","lax"]),QS=a=>typeof a=="number"&&a>0,PS=a=>HS.has(a),YS=a=>VS.has(a),_c=a=>a.replace(/^(org:)*/,"org:"),$S=(a,l)=>{const{orgId:r,orgRole:o,orgPermissions:u}=l;return!a.role&&!a.permission||!r||!o||!u?null:a.permission?u.includes(_c(a.permission)):a.role?_c(o)===_c(a.role):null},kg=(a,l)=>{const{org:r,user:o}=GS(a),[u,f]=l.split(":"),d=f||u;return u==="org"?r.includes(d):u==="user"?o.includes(d):[...r,...o].includes(d)},IS=(a,l)=>{const{features:r,plans:o}=l;return a.feature&&r?kg(r,a.feature):a.plan&&o?kg(o,a.plan):null},GS=a=>{const l=a?a.split(",").map(r=>r.trim()):[];return{org:l.filter(r=>r.split(":")[0].includes("o")).map(r=>r.split(":")[1]),user:l.filter(r=>r.split(":")[0].includes("u")).map(r=>r.split(":")[1])}},XS=a=>{if(!a)return!1;const l=u=>typeof u=="string"?BS[u]:u,r=typeof a=="string"&&YS(a),o=typeof a=="object"&&PS(a.level)&&QS(a.afterMinutes);return r||o?l.bind(null,a):!1},WS=(a,{factorVerificationAge:l})=>{if(!a.reverification||!l)return null;const r=XS(a.reverification);if(!r)return null;const{level:o,afterMinutes:u}=r(),[f,d]=l,v=f!==-1?u>f:null,m=d!==-1?u>d:null;switch(o){case"first_factor":return v;case"second_factor":return d!==-1?m:v;case"multi_factor":return d===-1?v:v&&m}},KS=a=>l=>{if(!a.userId)return!1;const r=IS(l,a),o=$S(l,a),u=WS(l,a);return[r||o,u].some(f=>f===null)?[r||o,u].some(f=>f===!0):[r||o,u].every(f=>f===!0)},ZS=({authObject:{sessionId:a,sessionStatus:l,userId:r,actor:o,orgId:u,orgRole:f,orgSlug:d,signOut:v,getToken:m,has:g,sessionClaims:y},options:{treatPendingAsSignedOut:O=!0}})=>{if(a===void 0&&r===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:a,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:v,getToken:m};if(a===null&&r===null)return{isLoaded:!0,isSignedIn:!1,sessionId:a,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:v,getToken:m};if(O&&l==="pending")return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:v,getToken:m};if(a&&y&&r&&u&&f)return{isLoaded:!0,isSignedIn:!0,sessionId:a,sessionClaims:y,userId:r,actor:o||null,orgId:u,orgRole:f,orgSlug:d||null,has:g,signOut:v,getToken:m};if(a&&y&&r&&!u)return{isLoaded:!0,isSignedIn:!0,sessionId:a,sessionClaims:y,userId:r,actor:o||null,orgId:null,orgRole:null,orgSlug:null,has:g,signOut:v,getToken:m}},jp=a=>typeof atob<"u"&&typeof atob=="function"?atob(a):typeof global<"u"&&global.Buffer?new global.Buffer(a,"base64").toString():a,FS=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],Mp="pk_live_",JS="pk_test_";function jg(a,l={}){if(a=a||"",!a||!Yc(a)){if(l.fatal&&!a)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(l.fatal&&!Yc(a))throw new Error("Publishable key not valid.");return null}const r=a.startsWith(Mp)?"production":"development";let o=jp(a.split("_")[2]);return o=o.slice(0,-1),l.proxyUrl?o=l.proxyUrl:r!=="development"&&l.domain&&l.isSatellite&&(o=`clerk.${l.domain}`),{instanceType:r,frontendApi:o}}function Yc(a=""){try{const l=a.startsWith(Mp)||a.startsWith(JS),r=jp(a.split("_")[2]||"").endsWith("$");return l&&r}catch{return!1}}function e1(){const a=new Map;return{isDevOrStagingUrl:l=>{if(!l)return!1;const r=typeof l=="string"?l:l.hostname;let o=a.get(r);return o===void 0&&(o=FS.some(u=>r.endsWith(u)),a.set(r,o)),o}}}var t1="METHOD_CALLED";function Up(a,l){return{event:t1,payload:{method:a,...l}}}var Ec={exports:{}},xc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mg;function n1(){if(Mg)return xc;Mg=1;var a=Hs();function l(O,S){return O===S&&(O!==0||1/O===1/S)||O!==O&&S!==S}var r=typeof Object.is=="function"?Object.is:l,o=a.useState,u=a.useEffect,f=a.useLayoutEffect,d=a.useDebugValue;function v(O,S){var R=S(),A=o({inst:{value:R,getSnapshot:S}}),L=A[0].inst,N=A[1];return f(function(){L.value=R,L.getSnapshot=S,m(L)&&N({inst:L})},[O,R,S]),u(function(){return m(L)&&N({inst:L}),O(function(){m(L)&&N({inst:L})})},[O]),d(R),R}function m(O){var S=O.getSnapshot;O=O.value;try{var R=S();return!r(O,R)}catch{return!0}}function g(O,S){return S()}var y=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?g:v;return xc.useSyncExternalStore=a.useSyncExternalStore!==void 0?a.useSyncExternalStore:y,xc}var Ug;function a1(){return Ug||(Ug=1,Ec.exports=n1()),Ec.exports}var Dp=a1();const Lp=0,Np=1,zp=2,Dg=3;var Lg=Object.prototype.hasOwnProperty;function $c(a,l){var r,o;if(a===l)return!0;if(a&&l&&(r=a.constructor)===l.constructor){if(r===Date)return a.getTime()===l.getTime();if(r===RegExp)return a.toString()===l.toString();if(r===Array){if((o=a.length)===l.length)for(;o--&&$c(a[o],l[o]););return o===-1}if(!r||typeof a=="object"){o=0;for(r in a)if(Lg.call(a,r)&&++o&&!Lg.call(l,r)||!(r in l)||!$c(a[r],l[r]))return!1;return Object.keys(l).length===o}}return a!==a&&l!==l}const Ft=new WeakMap,ua=()=>{},Ze=ua(),qs=Object,fe=a=>a===Ze,Gt=a=>typeof a=="function",Un=(a,l)=>({...a,...l}),qp=a=>Gt(a.then),wc={},Ss={},uf="undefined",Kl=typeof window!=uf,Ic=typeof document!=uf,i1=Kl&&"Deno"in window,l1=()=>Kl&&typeof window.requestAnimationFrame!=uf,sa=(a,l)=>{const r=Ft.get(a);return[()=>!fe(l)&&a.get(l)||wc,o=>{if(!fe(l)){const u=a.get(l);l in Ss||(Ss[l]=u),r[5](l,Un(u,o),u||wc)}},r[6],()=>!fe(l)&&l in Ss?Ss[l]:!fe(l)&&a.get(l)||wc]};let Gc=!0;const r1=()=>Gc,[Xc,Wc]=Kl&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[ua,ua],s1=()=>{const a=Ic&&document.visibilityState;return fe(a)||a!=="hidden"},o1=a=>(Ic&&document.addEventListener("visibilitychange",a),Xc("focus",a),()=>{Ic&&document.removeEventListener("visibilitychange",a),Wc("focus",a)}),u1=a=>{const l=()=>{Gc=!0,a()},r=()=>{Gc=!1};return Xc("online",l),Xc("offline",r),()=>{Wc("online",l),Wc("offline",r)}},c1={isOnline:r1,isVisible:s1},f1={initFocus:o1,initReconnect:u1},Ng=!q.useId,Xl=!Kl||i1,d1=a=>l1()?window.requestAnimationFrame(a):setTimeout(a,1),Vl=Xl?w.useEffect:w.useLayoutEffect,Ac=typeof navigator<"u"&&navigator.connection,zg=!Xl&&Ac&&(["slow-2g","2g"].includes(Ac.effectiveType)||Ac.saveData),_s=new WeakMap,Cc=(a,l)=>qs.prototype.toString.call(a)===`[object ${l}]`;let h1=0;const Kc=a=>{const l=typeof a,r=Cc(a,"Date"),o=Cc(a,"RegExp"),u=Cc(a,"Object");let f,d;if(qs(a)===a&&!r&&!o){if(f=_s.get(a),f)return f;if(f=++h1+"~",_s.set(a,f),Array.isArray(a)){for(f="@",d=0;d<a.length;d++)f+=Kc(a[d])+",";_s.set(a,f)}if(u){f="#";const v=qs.keys(a).sort();for(;!fe(d=v.pop());)fe(a[d])||(f+=d+":"+Kc(a[d])+",");_s.set(a,f)}}else f=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return f},ki=a=>{if(Gt(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?Kc(a):"",[a,l]};let m1=0;const Zc=()=>++m1;async function Bp(...a){const[l,r,o,u]=a,f=Un({populateCache:!0,throwOnError:!0},typeof u=="boolean"?{revalidate:u}:u||{});let d=f.populateCache;const v=f.rollbackOnError;let m=f.optimisticData;const g=S=>typeof v=="function"?v(S):v!==!1,y=f.throwOnError;if(Gt(r)){const S=r,R=[],A=l.keys();for(const L of A)!/^\$(inf|sub)\$/.test(L)&&S(l.get(L)._k)&&R.push(L);return Promise.all(R.map(O))}return O(r);async function O(S){const[R]=ki(S);if(!R)return;const[A,L]=sa(l,R),[N,C,H,$]=Ft.get(l),J=()=>{const Ce=N[R];return(Gt(f.revalidate)?f.revalidate(A().data,S):f.revalidate!==!1)&&(delete H[R],delete $[R],Ce&&Ce[0])?Ce[0](zp).then(()=>A().data):A().data};if(a.length<3)return J();let Y=o,Z;const le=Zc();C[R]=[le,0];const W=!fe(m),oe=A(),ne=oe.data,be=oe._c,ve=fe(be)?ne:be;if(W&&(m=Gt(m)?m(ve,ne):m,L({data:m,_c:ve})),Gt(Y))try{Y=Y(ve)}catch(Ce){Z=Ce}if(Y&&qp(Y))if(Y=await Y.catch(Ce=>{Z=Ce}),le!==C[R][0]){if(Z)throw Z;return Y}else Z&&W&&g(Z)&&(d=!0,L({data:ve,_c:Ze}));if(d&&!Z)if(Gt(d)){const Ce=d(Y,ve);L({data:Ce,error:Ze,_c:Ze})}else L({data:Y,error:Ze,_c:Ze});if(C[R][1]=Zc(),Promise.resolve(J()).then(()=>{L({_c:Ze})}),Z){if(y)throw Z;return}return Y}}const qg=(a,l)=>{for(const r in a)a[r][0]&&a[r][0](l)},Hp=(a,l)=>{if(!Ft.has(a)){const r=Un(f1,l),o=Object.create(null),u=Bp.bind(Ze,a);let f=ua;const d=Object.create(null),v=(y,O)=>{const S=d[y]||[];return d[y]=S,S.push(O),()=>S.splice(S.indexOf(O),1)},m=(y,O,S)=>{a.set(y,O);const R=d[y];if(R)for(const A of R)A(O,S)},g=()=>{if(!Ft.has(a)&&(Ft.set(a,[o,Object.create(null),Object.create(null),Object.create(null),u,m,v]),!Xl)){const y=r.initFocus(setTimeout.bind(Ze,qg.bind(Ze,o,Lp))),O=r.initReconnect(setTimeout.bind(Ze,qg.bind(Ze,o,Np)));f=()=>{y&&y(),O&&O(),Ft.delete(a)}}};return g(),[a,u,g,f]}return[a,Ft.get(a)[4]]},g1=(a,l,r,o,u)=>{const f=r.errorRetryCount,d=u.retryCount,v=~~((Math.random()+.5)*(1<<(d<8?d:8)))*r.errorRetryInterval;!fe(f)&&d>f||setTimeout(o,v,u)},p1=$c,[Zl,Vp]=Hp(new Map),Qp=Un({onLoadingSlow:ua,onSuccess:ua,onError:ua,onErrorRetry:g1,onDiscarded:ua,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:zg?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:zg?5e3:3e3,compare:p1,isPaused:()=>!1,cache:Zl,mutate:Vp,fallback:{}},c1),Pp=(a,l)=>{const r=Un(a,l);if(l){const{use:o,fallback:u}=a,{use:f,fallback:d}=l;o&&f&&(r.use=o.concat(f)),u&&d&&(r.fallback=Un(u,d))}return r},Fc=w.createContext({}),v1=a=>{const{value:l}=a,r=w.useContext(Fc),o=Gt(l),u=w.useMemo(()=>o?l(r):l,[o,r,l]),f=w.useMemo(()=>o?u:Pp(r,u),[o,r,u]),d=u&&u.provider,v=w.useRef(Ze);d&&!v.current&&(v.current=Hp(d(f.cache||Zl),u));const m=v.current;return m&&(f.cache=m[0],f.mutate=m[1]),Vl(()=>{if(m)return m[2]&&m[2](),m[3]},[]),w.createElement(Fc.Provider,Un(a,{value:f}))},Yp="$inf$",$p=Kl&&window.__SWR_DEVTOOLS_USE__,y1=$p?window.__SWR_DEVTOOLS_USE__:[],b1=()=>{$p&&(window.__SWR_DEVTOOLS_REACT__=q)},Ip=a=>Gt(a[1])?[a[0],a[1],a[2]||{}]:[a[0],null,(a[1]===null?a[2]:a[1])||{}],Gp=()=>Un(Qp,w.useContext(Fc)),S1=(a,l)=>{const[r,o]=ki(a),[,,,u]=Ft.get(Zl);if(u[r])return u[r];const f=l(o);return u[r]=f,f},_1=a=>(l,r,o)=>a(l,r&&((...f)=>{const[d]=ki(l),[,,,v]=Ft.get(Zl);if(d.startsWith(Yp))return r(...f);const m=v[d];return fe(m)?r(...f):(delete v[d],m)}),o),E1=y1.concat(_1),x1=a=>function(...r){const o=Gp(),[u,f,d]=Ip(r),v=Pp(o,d);let m=a;const{use:g}=v,y=(g||[]).concat(E1);for(let O=y.length;O--;)m=y[O](m);return m(u,f||v.fetcher||null,v)},w1=(a,l,r)=>{const o=l[a]||(l[a]=[]);return o.push(r),()=>{const u=o.indexOf(r);u>=0&&(o[u]=o[o.length-1],o.pop())}},A1=(a,l)=>(...r)=>{const[o,u,f]=Ip(r),d=(f.use||[]).concat(l);return a(o,u,{...f,use:d})};b1();const C1=()=>{},T1=C1(),Jc=Object,Bg=a=>a===T1,O1=a=>typeof a=="function",Es=new WeakMap,Tc=(a,l)=>Jc.prototype.toString.call(a)===`[object ${l}]`;let R1=0;const ef=a=>{const l=typeof a,r=Tc(a,"Date"),o=Tc(a,"RegExp"),u=Tc(a,"Object");let f,d;if(Jc(a)===a&&!r&&!o){if(f=Es.get(a),f)return f;if(f=++R1+"~",Es.set(a,f),Array.isArray(a)){for(f="@",d=0;d<a.length;d++)f+=ef(a[d])+",";Es.set(a,f)}if(u){f="#";const v=Jc.keys(a).sort();for(;!Bg(d=v.pop());)Bg(a[d])||(f+=d+":"+ef(a[d])+",");Es.set(a,f)}}else f=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return f},k1=a=>{if(O1(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?ef(a):"",[a,l]},j1=a=>k1(a)[0],Oc=q.use||(a=>{switch(a.status){case"pending":throw a;case"fulfilled":return a.value;case"rejected":throw a.reason;default:throw a.status="pending",a.then(l=>{a.status="fulfilled",a.value=l},l=>{a.status="rejected",a.reason=l}),a}}),Rc={dedupe:!0},M1=(a,l,r)=>{const{cache:o,compare:u,suspense:f,fallbackData:d,revalidateOnMount:v,revalidateIfStale:m,refreshInterval:g,refreshWhenHidden:y,refreshWhenOffline:O,keepPreviousData:S}=r,[R,A,L,N]=Ft.get(o),[C,H]=ki(a),$=w.useRef(!1),J=w.useRef(!1),Y=w.useRef(C),Z=w.useRef(l),le=w.useRef(r),W=()=>le.current,oe=()=>W().isVisible()&&W().isOnline(),[ne,be,ve,Ce]=sa(o,C),De=w.useRef({}).current,F=fe(d)?fe(r.fallback)?Ze:r.fallback[C]:d,M=(xe,Ee)=>{for(const Fe in De){const ze=Fe;if(ze==="data"){if(!u(xe[ze],Ee[ze])&&(!fe(xe[ze])||!u(he,Ee[ze])))return!1}else if(Ee[ze]!==xe[ze])return!1}return!0},P=w.useMemo(()=>{const xe=!C||!l?!1:fe(v)?W().isPaused()||f?!1:m!==!1:v,Ee=Je=>{const bt=Un(Je);return delete bt._k,xe?{isValidating:!0,isLoading:!0,...bt}:bt},Fe=ne(),ze=Ce(),He=Ee(Fe),Ln=Fe===ze?He:Ee(ze);let Ie=He;return[()=>{const Je=Ee(ne());return M(Je,Ie)?(Ie.data=Je.data,Ie.isLoading=Je.isLoading,Ie.isValidating=Je.isValidating,Ie.error=Je.error,Ie):(Ie=Je,Je)},()=>Ln]},[o,C]),X=Dp.useSyncExternalStore(w.useCallback(xe=>ve(C,(Ee,Fe)=>{M(Fe,Ee)||xe()}),[o,C]),P[0],P[1]),ue=!$.current,_=R[C]&&R[C].length>0,Q=X.data,I=fe(Q)?F&&qp(F)?Oc(F):F:Q,G=X.error,ee=w.useRef(I),he=S?fe(Q)?fe(ee.current)?I:ee.current:Q:I,re=_&&!fe(G)?!1:ue&&!fe(v)?v:W().isPaused()?!1:f?fe(I)?!1:m:fe(I)||m,ut=!!(C&&l&&ue&&re),Me=fe(X.isValidating)?ut:X.isValidating,nn=fe(X.isLoading)?ut:X.isLoading,mn=w.useCallback(async xe=>{const Ee=Z.current;if(!C||!Ee||J.current||W().isPaused())return!1;let Fe,ze,He=!0;const Ln=xe||{},Ie=!L[C]||!Ln.dedupe,Je=()=>Ng?!J.current&&C===Y.current&&$.current:C===Y.current,bt={isValidating:!1,isLoading:!1},ar=()=>{be(bt)},zi=()=>{const St=L[C];St&&St[1]===ze&&delete L[C]},ir={isValidating:!0};fe(ne().data)&&(ir.isLoading=!0);try{if(Ie&&(be(ir),r.loadingTimeout&&fe(ne().data)&&setTimeout(()=>{He&&Je()&&W().onLoadingSlow(C,r)},r.loadingTimeout),L[C]=[Ee(H),Zc()]),[Fe,ze]=L[C],Fe=await Fe,Ie&&setTimeout(zi,r.dedupingInterval),!L[C]||L[C][1]!==ze)return Ie&&Je()&&W().onDiscarded(C),!1;bt.error=Ze;const St=A[C];if(!fe(St)&&(ze<=St[0]||ze<=St[1]||St[1]===0))return ar(),Ie&&Je()&&W().onDiscarded(C),!1;const gt=ne().data;bt.data=u(gt,Fe)?gt:Fe,Ie&&Je()&&W().onSuccess(Fe,C,r)}catch(St){zi();const gt=W(),{shouldRetryOnError:it}=gt;gt.isPaused()||(bt.error=St,Ie&&Je()&&(gt.onError(St,C,gt),(it===!0||Gt(it)&&it(St))&&(!W().revalidateOnFocus||!W().revalidateOnReconnect||oe())&&gt.onErrorRetry(St,C,gt,an=>{const lt=R[C];lt&&lt[0]&&lt[0](Dg,an)},{retryCount:(Ln.retryCount||0)+1,dedupe:!0})))}return He=!1,ar(),!0},[C,o]),ha=w.useCallback((...xe)=>Bp(o,Y.current,...xe),[]);if(Vl(()=>{Z.current=l,le.current=r,fe(Q)||(ee.current=Q)}),Vl(()=>{if(!C)return;const xe=mn.bind(Ze,Rc);let Ee=0;W().revalidateOnFocus&&(Ee=Date.now()+W().focusThrottleInterval);const ze=w1(C,R,(He,Ln={})=>{if(He==Lp){const Ie=Date.now();W().revalidateOnFocus&&Ie>Ee&&oe()&&(Ee=Ie+W().focusThrottleInterval,xe())}else if(He==Np)W().revalidateOnReconnect&&oe()&&xe();else{if(He==zp)return mn();if(He==Dg)return mn(Ln)}});return J.current=!1,Y.current=C,$.current=!0,be({_k:H}),re&&(fe(I)||Xl?xe():d1(xe)),()=>{J.current=!0,ze()}},[C]),Vl(()=>{let xe;function Ee(){const ze=Gt(g)?g(ne().data):g;ze&&xe!==-1&&(xe=setTimeout(Fe,ze))}function Fe(){!ne().error&&(y||W().isVisible())&&(O||W().isOnline())?mn(Rc).then(Ee):Ee()}return Ee(),()=>{xe&&(clearTimeout(xe),xe=-1)}},[g,y,O,C]),w.useDebugValue(he),f&&fe(I)&&C){if(!Ng&&Xl)throw new Error("Fallback data is required when using Suspense in SSR.");Z.current=l,le.current=r,J.current=!1;const xe=N[C];if(!fe(xe)){const Ee=ha(xe);Oc(Ee)}if(fe(G)){const Ee=mn(Rc);fe(he)||(Ee.status="fulfilled",Ee.value=!0),Oc(Ee)}else throw G}return{mutate:ha,get data(){return De.data=!0,he},get error(){return De.error=!0,G},get isValidating(){return De.isValidating=!0,Me},get isLoading(){return De.isLoading=!0,nn}}},U1=qs.defineProperty(v1,"defaultValue",{value:Qp}),cf=x1(M1),D1=Object.freeze(Object.defineProperty({__proto__:null,SWRConfig:U1,default:cf,mutate:Vp,preload:S1,unstable_serialize:j1,useSWRConfig:Gp},Symbol.toStringTag,{value:"Module"})),L1=()=>{},N1=L1(),tf=Object,Hg=a=>a===N1,z1=a=>typeof a=="function",xs=new WeakMap,kc=(a,l)=>tf.prototype.toString.call(a)===`[object ${l}]`;let q1=0;const nf=a=>{const l=typeof a,r=kc(a,"Date"),o=kc(a,"RegExp"),u=kc(a,"Object");let f,d;if(tf(a)===a&&!r&&!o){if(f=xs.get(a),f)return f;if(f=++q1+"~",xs.set(a,f),Array.isArray(a)){for(f="@",d=0;d<a.length;d++)f+=nf(a[d])+",";xs.set(a,f)}if(u){f="#";const v=tf.keys(a).sort();for(;!Hg(d=v.pop());)Hg(a[d])||(f+=d+":"+nf(a[d])+",");xs.set(a,f)}}else f=r?a.toJSON():l=="symbol"?a.toString():l=="string"?JSON.stringify(a):""+a;return f},B1=a=>{if(z1(a))try{a=a()}catch{a=""}const l=a;return a=typeof a=="string"?a:(Array.isArray(a)?a.length:a)?nf(a):"",[a,l]},H1=a=>B1(a?a(0,null):null)[0],jc=Promise.resolve(),V1=a=>(l,r,o)=>{const u=w.useRef(!1),{cache:f,initialSize:d=1,revalidateAll:v=!1,persistSize:m=!1,revalidateFirstPage:g=!0,revalidateOnMount:y=!1,parallel:O=!1}=o,[,,,S]=Ft.get(Zl);let R;try{R=H1(l),R&&(R=Yp+R)}catch{}const[A,L,N]=sa(f,R),C=w.useCallback(()=>fe(A()._l)?d:A()._l,[f,R,d]);Dp.useSyncExternalStore(w.useCallback(W=>R?N(R,()=>{W()}):()=>{},[f,R]),C,C);const H=w.useCallback(()=>{const W=A()._l;return fe(W)?d:W},[R,d]),$=w.useRef(H());Vl(()=>{if(!u.current){u.current=!0;return}R&&L({_l:m?$.current:H()})},[R,f]);const J=y&&!u.current,Y=a(R,async W=>{const oe=A()._i,ne=A()._r;L({_r:Ze});const be=[],ve=H(),[Ce]=sa(f,W),De=Ce().data,F=[];let M=null;for(let P=0;P<ve;++P){const[X,ue]=ki(l(P,O?null:M));if(!X)break;const[_,Q]=sa(f,X);let I=_().data;const G=v||oe||fe(I)||g&&!P&&!fe(De)||J||De&&!fe(De[P])&&!o.compare(De[P],I);if(r&&(typeof ne=="function"?ne(I,ue):G)){const ee=async()=>{if(!(X in S))I=await r(ue);else{const re=S[X];delete S[X],I=await re}Q({data:I,_k:ue}),be[P]=I};O?F.push(ee):await ee()}else be[P]=I;O||(M=I)}return O&&await Promise.all(F.map(P=>P())),L({_i:Ze}),be},o),Z=w.useCallback(function(W,oe){const ne=typeof oe=="boolean"?{revalidate:oe}:oe||{},be=ne.revalidate!==!1;return R?(be&&(fe(W)?L({_i:!0,_r:ne.revalidate}):L({_i:!1,_r:ne.revalidate})),arguments.length?Y.mutate(W,{...ne,revalidate:be}):Y.mutate()):jc},[R,f]),le=w.useCallback(W=>{if(!R)return jc;const[,oe]=sa(f,R);let ne;if(Gt(W)?ne=W(H()):typeof W=="number"&&(ne=W),typeof ne!="number")return jc;oe({_l:ne}),$.current=ne;const be=[],[ve]=sa(f,R);let Ce=null;for(let De=0;De<ne;++De){const[F]=ki(l(De,Ce)),[M]=sa(f,F),P=F?M().data:Ze;if(fe(P))return Z(ve().data);be.push(P),Ce=P}return Z(be)},[R,f,Z,H]);return{size:H(),setSize:le,mutate:Z,get data(){return Y.data},get error(){return Y.error},get isValidating(){return Y.isValidating},get isLoading(){return Y.isLoading}}},Q1=A1(cf,V1);var Vg=Object.prototype.hasOwnProperty;function Qg(a,l,r){for(r of a.keys())if(Ql(r,l))return r}function Ql(a,l){var r,o,u;if(a===l)return!0;if(a&&l&&(r=a.constructor)===l.constructor){if(r===Date)return a.getTime()===l.getTime();if(r===RegExp)return a.toString()===l.toString();if(r===Array){if((o=a.length)===l.length)for(;o--&&Ql(a[o],l[o]););return o===-1}if(r===Set){if(a.size!==l.size)return!1;for(o of a)if(u=o,u&&typeof u=="object"&&(u=Qg(l,u),!u)||!l.has(u))return!1;return!0}if(r===Map){if(a.size!==l.size)return!1;for(o of a)if(u=o[0],u&&typeof u=="object"&&(u=Qg(l,u),!u)||!Ql(o[1],l.get(u)))return!1;return!0}if(r===ArrayBuffer)a=new Uint8Array(a),l=new Uint8Array(l);else if(r===DataView){if((o=a.byteLength)===l.byteLength)for(;o--&&a.getInt8(o)===l.getInt8(o););return o===-1}if(ArrayBuffer.isView(a)){if((o=a.byteLength)===l.byteLength)for(;o--&&a[o]===l[o];);return o===-1}if(!r||typeof a=="object"){o=0;for(r in a)if(Vg.call(a,r)&&++o&&!Vg.call(l,r)||!(r in l)||!Ql(a[r],l[r]))return!1;return Object.keys(l).length===o}}return a!==a&&l!==l}function P1(a,l){if(!a)throw typeof l=="string"?new Error(l):new Error(`${l.displayName} not found`)}var Mi=(a,l)=>{const{assertCtxFn:r=P1}={},o=q.createContext(void 0);return o.displayName=a,[o,()=>{const d=q.useContext(o);return r(d,`${a} not found`),d.value},()=>{const d=q.useContext(o);return d?d.value:{}}]},ff={};NS(ff,{useSWR:()=>cf,useSWRInfinite:()=>Q1});qS(ff,D1);var[Xp,Wp]=Mi("ClerkInstanceContext"),[Y1,$1]=Mi("UserContext"),[I1,cw]=Mi("ClientContext"),[G1,fw]=Mi("SessionContext");q.createContext({});var[X1,dw]=Mi("OrganizationContext"),W1=({children:a,organization:l,swrConfig:r})=>q.createElement(ff.SWRConfig,{value:r},q.createElement(X1.Provider,{value:{value:{organization:l}}},a));function Kp(a){if(!q.useContext(Xp)){if(typeof a=="function"){a();return}throw new Error(`${a} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}typeof window<"u"?q.useLayoutEffect:q.useEffect;var Pg="useUser";function hw(){Kp(Pg);const a=$1();return Wp().telemetry?.record(Up(Pg)),a===void 0?{isLoaded:!1,isSignedIn:void 0,user:void 0}:a===null?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:a}}var Yg=Ql,K1=()=>{try{return!1}catch{}return!1},Z1=()=>{try{return!1}catch{}return!1},F1=()=>{try{return!0}catch{}return!1},$g=new Set,df=(a,l,r)=>{const o=Z1()||F1(),u=a;$g.has(u)||o||($g.add(u),console.warn(`Clerk - DEPRECATION WARNING: "${a}" is deprecated and will be removed in the next major release.
${l}`))},hn=Rp({packageName:"@clerk/clerk-react"});function J1(a){hn.setMessages(a).setPackageName(a)}var[e_,t_]=Mi("AuthContext"),n_=Xp,Zp=Wp,a_="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",i_=a=>`You've passed multiple children components to <${a}/>. You can only pass a single child component or text.`,l_="Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support",Mc="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",r_="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",s_="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",o_="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",u_="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",c_=a=>`<${a} /> can only accept <${a}.Page /> and <${a}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,f_=a=>`Missing props. <${a}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,d_=a=>`Missing props. <${a}.Link /> component requires the following props: url, label and labelIcon.`,h_="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",m_="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",g_="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",p_="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",v_="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",y_="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",b_="Missing props. <UserButton.Action /> component requires the following props: label.",Vs=a=>{Kp(()=>{hn.throwMissingClerkProviderError({source:a})})},Fp=a=>new Promise(l=>{const r=o=>{["ready","degraded"].includes(o)&&(l(),a.off("status",r))};a.on("status",r,{notify:!0})}),S_=a=>async l=>(await Fp(a),a.session?a.session.getToken(l):null),__=a=>async(...l)=>(await Fp(a),a.signOut(...l)),Jp=(a={})=>{var l,r;Vs("useAuth");const{treatPendingAsSignedOut:o,...u}=a??{},f=u;let v=t_();v.sessionId===void 0&&v.userId===void 0&&(v=f??{});const m=Zp(),g=w.useCallback(S_(m),[m]),y=w.useCallback(__(m),[m]);return(l=m.telemetry)==null||l.record(Up("useAuth",{treatPendingAsSignedOut:o})),E_({...v,getToken:g,signOut:y},{treatPendingAsSignedOut:o??((r=m.__internal_getOption)==null?void 0:r.call(m,"treatPendingAsSignedOut"))})};function E_(a,{treatPendingAsSignedOut:l=!0}={}){const{userId:r,orgId:o,orgRole:u,has:f,signOut:d,getToken:v,orgPermissions:m,factorVerificationAge:g,sessionClaims:y}=a??{},O=w.useCallback(R=>f?f(R):KS({userId:r,orgId:o,orgRole:u,orgPermissions:m,factorVerificationAge:g,features:y?.fea||"",plans:y?.pla||""})(R),[f,r,o,u,m,g]),S=ZS({authObject:{...a,getToken:v,signOut:d,has:O},options:{treatPendingAsSignedOut:l}});return S||hn.throw(l_)}var Pe=(a,l)=>{const o=(typeof l=="string"?l:l?.component)||a.displayName||a.name||"Component";a.displayName=o;const u=typeof l=="string"?void 0:l,f=d=>{Vs(o||"withClerk");const v=Zp();return!v.loaded&&!u?.renderWhileLoading?null:q.createElement(a,{...d,component:o,clerk:v})};return f.displayName=`withClerk(${o})`,f},ws=({children:a,treatPendingAsSignedOut:l})=>{Vs("SignedIn");const{userId:r}=Jp({treatPendingAsSignedOut:l});return r?a:null},As=({children:a,treatPendingAsSignedOut:l})=>{Vs("SignedOut");const{userId:r}=Jp({treatPendingAsSignedOut:l});return r===null?a:null};Pe(({clerk:a,...l})=>{const{client:r,session:o}=a,u=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return q.useEffect(()=>{o===null&&u?a.redirectToAfterSignOut():a.redirectToSignIn(l)},[]),null},"RedirectToSignIn");Pe(({clerk:a,...l})=>(q.useEffect(()=>{a.redirectToSignUp(l)},[]),null),"RedirectToSignUp");Pe(({clerk:a})=>(q.useEffect(()=>{df("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),a.redirectToUserProfile()},[]),null),"RedirectToUserProfile");Pe(({clerk:a})=>(q.useEffect(()=>{df("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),a.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile");Pe(({clerk:a})=>(q.useEffect(()=>{df("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),a.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization");Pe(({clerk:a,...l})=>(q.useEffect(()=>{a.handleRedirectCallback(l)},[]),null),"AuthenticateWithRedirectCallback");var ev=a=>{throw TypeError(a)},hf=(a,l,r)=>l.has(a)||ev("Cannot "+r),Be=(a,l,r)=>(hf(a,l,"read from private field"),r?r.call(a):l.get(a)),Ua=(a,l,r)=>l.has(a)?ev("Cannot add the same private member more than once"):l instanceof WeakSet?l.add(a):l.set(a,r),Ei=(a,l,r,o)=>(hf(a,l,"write to private field"),l.set(a,r),r),Ig=(a,l,r)=>(hf(a,l,"access private method"),r),x_=(a,l="5.69.1")=>{if(a)return a;const r=w_(l);return r?r==="snapshot"?"5.69.1":r:A_(l)},w_=a=>a.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],A_=a=>a.trim().replace(/^v/,"").split(".")[0];function C_(a){return a?T_(a)||tv(a):!0}function T_(a){return/^http(s)?:\/\//.test(a||"")}function tv(a){return a.startsWith("/")}function O_(a){return a?tv(a)?new URL(a,window.location.origin).toString():a:""}function R_(a){if(!a)return"";let l;if(a.match(/^(clerk\.)+\w*$/))l=/(clerk\.)*(?=clerk\.)/;else{if(a.match(/\.clerk.accounts/))return a;l=/^(clerk\.)*/gi}return`clerk.${a.replace(l,"")}`}var k_={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(a,l)=>l<5,retryImmediately:!1,jitter:!0},j_=100,nv=async a=>new Promise(l=>setTimeout(l,a)),av=(a,l)=>l?a*(1+Math.random()):a,M_=a=>{let l=0;const r=()=>{const o=a.initialDelay,u=a.factor;let f=o*Math.pow(u,l);return f=av(f,a.jitter),Math.min(a.maxDelayBetweenRetries||f,f)};return async()=>{await nv(r()),l++}},U_=async(a,l={})=>{let r=0;const{shouldRetry:o,initialDelay:u,maxDelayBetweenRetries:f,factor:d,retryImmediately:v,jitter:m}={...k_,...l},g=M_({initialDelay:u,maxDelayBetweenRetries:f,factor:d,jitter:m});for(;;)try{return await a()}catch(y){if(r++,!o(y,r))throw y;v&&r===1?await nv(av(j_,m)):await g()}},D_="loadScript cannot be called when document does not exist",L_="loadScript cannot be called without a src";async function N_(a="",l){const{async:r,defer:o,beforeLoad:u,crossOrigin:f,nonce:d}=l||{};return U_(()=>new Promise((m,g)=>{a||g(new Error(L_)),(!document||!document.body)&&g(D_);const y=document.createElement("script");f&&y.setAttribute("crossorigin",f),y.async=r||!1,y.defer=o||!1,y.addEventListener("load",()=>{y.remove(),m(y)}),y.addEventListener("error",()=>{y.remove(),g()}),y.src=a,y.nonce=d,u?.(y),document.body.appendChild(y)}),{shouldRetry:(m,g)=>g<=5})}var Gg="Clerk: Failed to load Clerk",{isDevOrStagingUrl:z_}=e1(),iv=Rp({packageName:"@clerk/shared"});function q_(a){iv.setPackageName({packageName:a})}var B_=async a=>{const l=document.querySelector("script[data-clerk-js-script]");if(l)return new Promise((r,o)=>{l.addEventListener("load",()=>{r(l)}),l.addEventListener("error",()=>{o(Gg)})});if(!a?.publishableKey){iv.throwMissingPublishableKeyError();return}return N_(H_(a),{async:!0,crossOrigin:"anonymous",nonce:a.nonce,beforeLoad:Q_(a)}).catch(()=>{throw new Error(Gg)})},H_=a=>{const{clerkJSUrl:l,clerkJSVariant:r,clerkJSVersion:o,proxyUrl:u,domain:f,publishableKey:d}=a;if(l)return l;let v="";u&&C_(u)?v=O_(u).replace(/http(s)?:\/\//,""):f&&!z_(jg(d)?.frontendApi||"")?v=R_(f):v=jg(d)?.frontendApi||"";const m=r?`${r.replace(/\.+$/,"")}.`:"",g=x_(o);return`https://${v}/npm/@clerk/clerk-js@${g}/dist/clerk.${m}browser.js`},V_=a=>{const l={};return a.publishableKey&&(l["data-clerk-publishable-key"]=a.publishableKey),a.proxyUrl&&(l["data-clerk-proxy-url"]=a.proxyUrl),a.domain&&(l["data-clerk-domain"]=a.domain),a.nonce&&(l.nonce=a.nonce),l},Q_=a=>l=>{const r=V_(a);for(const o in r)l.setAttribute(o,r[o])},yt=a=>{K1()&&console.error(`Clerk: ${a}`)};function Uc(a,l,r){if(typeof a=="function")return a(l);if(typeof a<"u")return a;if(typeof r<"u")return r}var P_=gp(),Xg=(a,...l)=>{const r={...a};for(const o of l)delete r[o];return r},Y_=(a,l,r)=>!a&&r?$_(r):I_(l),$_=a=>{const l=a.userId,r=a.user,o=a.sessionId,u=a.sessionStatus,f=a.sessionClaims,d=a.session,v=a.organization,m=a.orgId,g=a.orgRole,y=a.orgPermissions,O=a.orgSlug,S=a.actor,R=a.factorVerificationAge;return{userId:l,user:r,sessionId:o,session:d,sessionStatus:u,sessionClaims:f,organization:v,orgId:m,orgRole:g,orgPermissions:y,orgSlug:O,actor:S,factorVerificationAge:R}},I_=a=>{const l=a.user?a.user.id:a.user,r=a.user,o=a.session?a.session.id:a.session,u=a.session,f=a.session?.status,d=a.session?a.session.lastActiveToken?.jwt?.claims:null,v=a.session?a.session.factorVerificationAge:null,m=u?.actor,g=a.organization,y=a.organization?a.organization.id:a.organization,O=g?.slug,S=g&&r?.organizationMemberships?.find(L=>L.organization.id===y),R=S&&S.permissions,A=S&&S.role;return{userId:l,user:r,sessionId:o,session:u,sessionStatus:f,sessionClaims:d,organization:g,orgId:y,orgRole:A,orgSlug:O,orgPermissions:R,actor:m,factorVerificationAge:v}};function Wg(){return typeof window<"u"}var Kg=(a,l,r,o,u)=>{const{notify:f}=u||{};let d=a.get(r);d||(d=[],a.set(r,d)),d.push(o),f&&l.has(r)&&o(l.get(r))},Zg=(a,l,r)=>(a.get(l)||[]).map(o=>o(r)),Fg=(a,l,r)=>{const o=a.get(l);o&&(r?o.splice(o.indexOf(r)>>>0,1):a.set(l,[]))},G_=()=>{const a=new Map,l=new Map,r=new Map;return{on:(...u)=>Kg(a,l,...u),prioritizedOn:(...u)=>Kg(r,l,...u),emit:(u,f)=>{l.set(u,f),Zg(r,u,f),Zg(a,u,f)},off:(...u)=>Fg(a,...u),prioritizedOff:(...u)=>Fg(r,...u),internal:{retrieveListeners:u=>a.get(u)||[]}}},Cs={Status:"status"},X_=()=>G_();typeof window<"u"&&!window.global&&(window.global=typeof global>"u"?window:global);var Qs=a=>l=>{try{return q.Children.only(a)}catch{return hn.throw(i_(l))}},Ps=(a,l)=>(a||(a=l),typeof a=="string"&&(a=q.createElement("button",null,a)),a),Ys=a=>(...l)=>{if(a&&typeof a=="function")return a(...l)};function W_(a){return typeof a=="function"}var Ts=new Map;function K_(a,l,r=1){q.useEffect(()=>{const o=Ts.get(a)||0;return o==r?hn.throw(l):(Ts.set(a,o+1),()=>{Ts.set(a,(Ts.get(a)||1)-1)})},[])}function Z_(a,l,r){const o=a.displayName||a.name||l||"Component",u=f=>(K_(l,r),q.createElement(a,{...f}));return u.displayName=`withMaxAllowedInstancesGuard(${o})`,u}var Pl=a=>{const l=Array(a.length).fill(null),[r,o]=w.useState(l);return a.map((u,f)=>({id:u.id,mount:d=>o(v=>v.map((m,g)=>g===f?d:m)),unmount:()=>o(d=>d.map((v,m)=>m===f?null:v)),portal:()=>q.createElement(q.Fragment,null,r[f]?P_.createPortal(u.component,r[f]):null)}))},vt=(a,l)=>!!a&&q.isValidElement(a)&&a?.type===l,lv=(a,l)=>ov({children:a,reorderItemsLabels:["account","security"],LinkComponent:Jl,PageComponent:Fl,MenuItemsComponent:Is,componentName:"UserProfile"},l),rv=(a,l)=>ov({children:a,reorderItemsLabels:["general","members"],LinkComponent:Xs,PageComponent:Gs,componentName:"OrganizationProfile"},l),sv=a=>{const l=[],r=[Xs,Gs,Is,Fl,Jl];return q.Children.forEach(a,o=>{r.some(u=>vt(o,u))||l.push(o)}),l},ov=(a,l)=>{const{children:r,LinkComponent:o,PageComponent:u,MenuItemsComponent:f,reorderItemsLabels:d,componentName:v}=a,{allowForAnyChildren:m=!1}=l||{},g=[];q.Children.forEach(r,H=>{if(!vt(H,u)&&!vt(H,o)&&!vt(H,f)){H&&!m&&yt(c_(v));return}const{props:$}=H,{children:J,label:Y,url:Z,labelIcon:le}=$;if(vt(H,u))if(Jg($,d))g.push({label:Y});else if(Dc($))g.push({label:Y,labelIcon:le,children:J,url:Z});else{yt(f_(v));return}if(vt(H,o))if(Lc($))g.push({label:Y,labelIcon:le,url:Z});else{yt(d_(v));return}});const y=[],O=[],S=[];g.forEach((H,$)=>{if(Dc(H)){y.push({component:H.children,id:$}),O.push({component:H.labelIcon,id:$});return}Lc(H)&&S.push({component:H.labelIcon,id:$})});const R=Pl(y),A=Pl(O),L=Pl(S),N=[],C=[];return g.forEach((H,$)=>{if(Jg(H,d)){N.push({label:H.label});return}if(Dc(H)){const{portal:J,mount:Y,unmount:Z}=R.find(ne=>ne.id===$),{portal:le,mount:W,unmount:oe}=A.find(ne=>ne.id===$);N.push({label:H.label,url:H.url,mount:Y,unmount:Z,mountIcon:W,unmountIcon:oe}),C.push(J),C.push(le);return}if(Lc(H)){const{portal:J,mount:Y,unmount:Z}=L.find(le=>le.id===$);N.push({label:H.label,url:H.url,mountIcon:Y,unmountIcon:Z}),C.push(J);return}}),{customPages:N,customPagesPortals:C}},Jg=(a,l)=>{const{children:r,label:o,url:u,labelIcon:f}=a;return!r&&!u&&!f&&l.some(d=>d===o)},Dc=a=>{const{children:l,label:r,url:o,labelIcon:u}=a;return!!l&&!!o&&!!u&&!!r},Lc=a=>{const{children:l,label:r,url:o,labelIcon:u}=a;return!l&&!!o&&!!u&&!!r},F_=a=>J_({children:a,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:Is,MenuActionComponent:cv,MenuLinkComponent:fv,UserProfileLinkComponent:Jl,UserProfilePageComponent:Fl}),J_=({children:a,MenuItemsComponent:l,MenuActionComponent:r,MenuLinkComponent:o,UserProfileLinkComponent:u,UserProfilePageComponent:f,reorderItemsLabels:d})=>{const v=[],m=[],g=[];q.Children.forEach(a,A=>{if(!vt(A,l)&&!vt(A,u)&&!vt(A,f)){A&&yt(h_);return}if(vt(A,u)||vt(A,f))return;const{props:L}=A;q.Children.forEach(L.children,N=>{if(!vt(N,r)&&!vt(N,o)){N&&yt(m_);return}const{props:C}=N,{label:H,labelIcon:$,href:J,onClick:Y,open:Z}=C;if(vt(N,r))if(ep(C,d))v.push({label:H});else if(Nc(C)){const le={label:H,labelIcon:$};if(Y!==void 0)v.push({...le,onClick:Y});else if(Z!==void 0)v.push({...le,open:Z.startsWith("/")?Z:`/${Z}`});else{yt("Custom menu item must have either onClick or open property");return}}else{yt(b_);return}if(vt(N,o))if(zc(C))v.push({label:H,labelIcon:$,href:J});else{yt(y_);return}})});const y=[],O=[];v.forEach((A,L)=>{Nc(A)&&y.push({component:A.labelIcon,id:L}),zc(A)&&O.push({component:A.labelIcon,id:L})});const S=Pl(y),R=Pl(O);return v.forEach((A,L)=>{if(ep(A,d)&&m.push({label:A.label}),Nc(A)){const{portal:N,mount:C,unmount:H}=S.find(J=>J.id===L),$={label:A.label,mountIcon:C,unmountIcon:H};"onClick"in A?$.onClick=A.onClick:"open"in A&&($.open=A.open),m.push($),g.push(N)}if(zc(A)){const{portal:N,mount:C,unmount:H}=R.find($=>$.id===L);m.push({label:A.label,href:A.href,mountIcon:C,unmountIcon:H}),g.push(N)}}),{customMenuItems:m,customMenuItemsPortals:g}},ep=(a,l)=>{const{children:r,label:o,onClick:u,labelIcon:f}=a;return!r&&!u&&!f&&l.some(d=>d===o)},Nc=a=>{const{label:l,labelIcon:r,onClick:o,open:u}=a;return!!r&&!!l&&(typeof o=="function"||typeof u=="string")},zc=a=>{const{label:l,href:r,labelIcon:o}=a;return!!r&&!!o&&!!l};function eE(a){const{root:l=document?.body,selector:r,timeout:o=0}=a;return new Promise((u,f)=>{if(!l){f(new Error("No root element provided"));return}let d=l;if(r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0){u();return}const m=new MutationObserver(g=>{for(const y of g)if(y.type==="childList"&&(!d&&r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0)){m.disconnect(),u();return}});m.observe(l,{childList:!0,subtree:!0}),o>0&&setTimeout(()=>{m.disconnect(),f(new Error("Timeout waiting for element children"))},o)})}function Xt(a){const l=w.useRef(),[r,o]=w.useState("rendering");return w.useEffect(()=>{if(!a)throw new Error("Clerk: no component name provided, unable to detect mount.");typeof window<"u"&&!l.current&&(l.current=eE({selector:`[data-clerk-component="${a}"]`}).then(()=>{o("rendered")}).catch(()=>{o("error")}))},[a]),r}var Os=a=>"mount"in a,tp=a=>"open"in a,np=a=>a?.map(({mountIcon:l,unmountIcon:r,...o})=>o),Ct=class extends q.PureComponent{constructor(){super(...arguments),this.rootRef=q.createRef()}componentDidUpdate(a){var l,r,o,u;if(!Os(a)||!Os(this.props))return;const f=Xg(a.props,"customPages","customMenuItems","children"),d=Xg(this.props.props,"customPages","customMenuItems","children"),v=((l=f.customPages)==null?void 0:l.length)!==((r=d.customPages)==null?void 0:r.length),m=((o=f.customMenuItems)==null?void 0:o.length)!==((u=d.customMenuItems)==null?void 0:u.length),g=np(a.props.customMenuItems),y=np(this.props.props.customMenuItems);(!Yg(f,d)||!Yg(g,y)||v||m)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(Os(this.props)&&this.props.mount(this.rootRef.current,this.props.props),tp(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(Os(this.props)&&this.props.unmount(this.rootRef.current),tp(this.props)&&this.props.close())}render(){const{hideRootHtmlElement:a=!1}=this.props,l={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return q.createElement(q.Fragment,null,!a&&q.createElement("div",{...l}),this.props.children)}},$s=a=>{var l,r;return q.createElement(q.Fragment,null,(l=a?.customPagesPortals)==null?void 0:l.map((o,u)=>w.createElement(o,{key:u})),(r=a?.customMenuItemsPortals)==null?void 0:r.map((o,u)=>w.createElement(o,{key:u})))},mw=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountSignIn,unmount:a.unmountSignIn,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"SignIn",renderWhileLoading:!0}),gw=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountSignUp,unmount:a.unmountSignUp,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"SignUp",renderWhileLoading:!0});function Fl({children:a}){return yt(r_),q.createElement(q.Fragment,null,a)}function Jl({children:a}){return yt(s_),q.createElement(q.Fragment,null,a)}var tE=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=lv(o.children);return q.createElement(q.Fragment,null,f&&r,q.createElement(Ct,{component:l,mount:a.mountUserProfile,unmount:a.unmountUserProfile,updateProps:a.__unstable__updateProps,props:{...o,customPages:v},rootProps:d},q.createElement($s,{customPagesPortals:m})))},{component:"UserProfile",renderWhileLoading:!0});Object.assign(tE,{Page:Fl,Link:Jl});var uv=w.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),nE=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=lv(o.children,{allowForAnyChildren:!!o.__experimental_asProvider}),g=Object.assign(o.userProfileProps||{},{customPages:v}),{customMenuItems:y,customMenuItemsPortals:O}=F_(o.children),S=sv(o.children),R={mount:a.mountUserButton,unmount:a.unmountUserButton,updateProps:a.__unstable__updateProps,props:{...o,userProfileProps:g,customMenuItems:y}},A={customPagesPortals:m,customMenuItemsPortals:O};return q.createElement(uv.Provider,{value:R},f&&r,a.loaded&&q.createElement(Ct,{component:l,...R,hideRootHtmlElement:!!o.__experimental_asProvider,rootProps:d},o.__experimental_asProvider?S:null,q.createElement($s,{...A})))},{component:"UserButton",renderWhileLoading:!0});function Is({children:a}){return yt(g_),q.createElement(q.Fragment,null,a)}function cv({children:a}){return yt(p_),q.createElement(q.Fragment,null,a)}function fv({children:a}){return yt(v_),q.createElement(q.Fragment,null,a)}function aE(a){const l=w.useContext(uv),r={...l,props:{...l.props,...a}};return q.createElement(Ct,{...r})}var pw=Object.assign(nE,{UserProfilePage:Fl,UserProfileLink:Jl,MenuItems:Is,Action:cv,Link:fv,__experimental_Outlet:aE});function Gs({children:a}){return yt(o_),q.createElement(q.Fragment,null,a)}function Xs({children:a}){return yt(u_),q.createElement(q.Fragment,null,a)}var iE=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=rv(o.children);return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountOrganizationProfile,unmount:a.unmountOrganizationProfile,updateProps:a.__unstable__updateProps,props:{...o,customPages:v},rootProps:d},q.createElement($s,{customPagesPortals:m})))},{component:"OrganizationProfile",renderWhileLoading:!0});Object.assign(iE,{Page:Gs,Link:Xs});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountCreateOrganization,unmount:a.unmountCreateOrganization,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"CreateOrganization",renderWhileLoading:!0});var dv=w.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),lE=Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=rv(o.children,{allowForAnyChildren:!!o.__experimental_asProvider}),g=Object.assign(o.organizationProfileProps||{},{customPages:v}),y=sv(o.children),O={mount:a.mountOrganizationSwitcher,unmount:a.unmountOrganizationSwitcher,updateProps:a.__unstable__updateProps,props:{...o,organizationProfileProps:g},rootProps:d,component:l};return a.__experimental_prefetchOrganizationSwitcher(),q.createElement(dv.Provider,{value:O},q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{...O,hideRootHtmlElement:!!o.__experimental_asProvider},o.__experimental_asProvider?y:null,q.createElement($s,{customPagesPortals:m}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0});function rE(a){const l=w.useContext(dv),r={...l,props:{...l.props,...a}};return q.createElement(Ct,{...r})}Object.assign(lE,{OrganizationProfilePage:Gs,OrganizationProfileLink:Xs,__experimental_Outlet:rE});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountOrganizationList,unmount:a.unmountOrganizationList,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"OrganizationList",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,open:a.openGoogleOneTap,close:a.closeGoogleOneTap,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"GoogleOneTap",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountWaitlist,unmount:a.unmountWaitlist,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"Waitlist",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountPricingTable,unmount:a.unmountPricingTable,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"PricingTable",renderWhileLoading:!0});Pe(({clerk:a,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!a.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,a.loaded&&q.createElement(Ct,{component:l,mount:a.mountApiKeys,unmount:a.unmountApiKeys,updateProps:a.__unstable__updateProps,props:o,rootProps:d}))},{component:"ApiKeys",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{signUpFallbackRedirectUrl:o,forceRedirectUrl:u,fallbackRedirectUrl:f,signUpForceRedirectUrl:d,mode:v,initialValues:m,withSignUp:g,oauthFlow:y,...O}=r;l=Ps(l,"Sign in");const S=Qs(l)("SignInButton"),R=()=>{const N={forceRedirectUrl:u,fallbackRedirectUrl:f,signUpFallbackRedirectUrl:o,signUpForceRedirectUrl:d,initialValues:m,withSignUp:g,oauthFlow:y};return v==="modal"?a.openSignIn({...N,appearance:r.appearance}):a.redirectToSignIn({...N,signInFallbackRedirectUrl:f,signInForceRedirectUrl:u})},L={...O,onClick:async N=>(S&&typeof S=="object"&&"props"in S&&await Ys(S.props.onClick)(N),R())};return q.cloneElement(S,L)},{component:"SignInButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{fallbackRedirectUrl:o,forceRedirectUrl:u,signInFallbackRedirectUrl:f,signInForceRedirectUrl:d,mode:v,unsafeMetadata:m,initialValues:g,oauthFlow:y,...O}=r;l=Ps(l,"Sign up");const S=Qs(l)("SignUpButton"),R=()=>{const N={fallbackRedirectUrl:o,forceRedirectUrl:u,signInFallbackRedirectUrl:f,signInForceRedirectUrl:d,unsafeMetadata:m,initialValues:g,oauthFlow:y};return v==="modal"?a.openSignUp({...N,appearance:r.appearance}):a.redirectToSignUp({...N,signUpFallbackRedirectUrl:o,signUpForceRedirectUrl:u})},L={...O,onClick:async N=>(S&&typeof S=="object"&&"props"in S&&await Ys(S.props.onClick)(N),R())};return q.cloneElement(S,L)},{component:"SignUpButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{redirectUrl:o="/",signOutOptions:u,...f}=r;l=Ps(l,"Sign out");const d=Qs(l)("SignOutButton"),v=()=>a.signOut({redirectUrl:o,...u}),g={...f,onClick:async y=>(await Ys(d.props.onClick)(y),v())};return q.cloneElement(d,g)},{component:"SignOutButton",renderWhileLoading:!0});Pe(({clerk:a,children:l,...r})=>{const{redirectUrl:o,...u}=r;l=Ps(l,"Sign in with Metamask");const f=Qs(l)("SignInWithMetamaskButton"),d=async()=>{async function g(){await a.authenticateWithMetamask({redirectUrl:o||void 0})}g()},m={...u,onClick:async g=>(await Ys(f.props.onClick)(g),d())};return q.cloneElement(f,m)},{component:"SignInWithMetamask",renderWhileLoading:!0});typeof globalThis.__BUILD_DISABLE_RHC__>"u"&&(globalThis.__BUILD_DISABLE_RHC__=!1);var sE={name:"@clerk/clerk-react",version:"5.32.1",environment:"production"},js,wi,Ai,ra,kn,oa,Ms,af,hv=class mv{constructor(l){Ua(this,Ms),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],Ua(this,js,"loading"),Ua(this,wi),Ua(this,Ai),Ua(this,ra),Ua(this,kn,X_()),this.buildSignInUrl=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignInUrl(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildSignInUrl",f)},this.buildSignUpUrl=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignUpUrl(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildSignUpUrl",f)},this.buildAfterSignInUrl=(...u)=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignInUrl(...u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildAfterSignInUrl",f)},this.buildAfterSignUpUrl=(...u)=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignUpUrl(...u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildAfterSignUpUrl",f)},this.buildAfterSignOutUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterSignOutUrl",u)},this.buildNewSubscriptionRedirectUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",u)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",u)},this.buildUserProfileUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildUserProfileUrl",u)},this.buildCreateOrganizationUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildCreateOrganizationUrl",u)},this.buildOrganizationProfileUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildOrganizationProfileUrl",u)},this.buildWaitlistUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildWaitlistUrl",u)},this.buildUrlWithAuth=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildUrlWithAuth(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildUrlWithAuth",f)},this.handleUnauthenticated=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.handleUnauthenticated()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("handleUnauthenticated",u)},this.on=(...u)=>{var f;if((f=this.clerkjs)!=null&&f.on)return this.clerkjs.on(...u);Be(this,kn).on(...u)},this.off=(...u)=>{var f;if((f=this.clerkjs)!=null&&f.off)return this.clerkjs.off(...u);Be(this,kn).off(...u)},this.addOnLoaded=u=>{this.loadedListeners.push(u),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(u=>u()),this.loadedListeners=[]},this.beforeLoad=u=>{if(!u)throw new Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=u=>{var f;if(!u)throw new Error("Failed to hydrate latest Clerk JS");return this.clerkjs=u,this.premountMethodCalls.forEach(d=>d()),this.premountAddListenerCalls.forEach((d,v)=>{d.nativeUnsubscribe=u.addListener(v)}),(f=Be(this,kn).internal.retrieveListeners("status"))==null||f.forEach(d=>{this.on("status",d,{notify:!0})}),this.preopenSignIn!==null&&u.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&u.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&u.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSignUp!==null&&u.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&u.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&u.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&u.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&u.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&u.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&u.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((d,v)=>{u.mountSignIn(v,d)}),this.premountSignUpNodes.forEach((d,v)=>{u.mountSignUp(v,d)}),this.premountUserProfileNodes.forEach((d,v)=>{u.mountUserProfile(v,d)}),this.premountUserButtonNodes.forEach((d,v)=>{u.mountUserButton(v,d)}),this.premountOrganizationListNodes.forEach((d,v)=>{u.mountOrganizationList(v,d)}),this.premountWaitlistNodes.forEach((d,v)=>{u.mountWaitlist(v,d)}),this.premountPricingTableNodes.forEach((d,v)=>{u.mountPricingTable(v,d)}),this.premountApiKeysNodes.forEach((d,v)=>{u.mountApiKeys(v,d)}),this.premountOAuthConsentNodes.forEach((d,v)=>{u.__internal_mountOAuthConsent(v,d)}),typeof this.clerkjs.status>"u"&&Be(this,kn).emit(Cs.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async u=>{const f=await Ig(this,Ms,af).call(this);if(f&&"__unstable__updateProps"in f)return f.__unstable__updateProps(u)},this.__experimental_navigateToTask=async u=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(u):Promise.reject(),this.setActive=u=>this.clerkjs?this.clerkjs.setActive(u):Promise.reject(),this.openSignIn=u=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(u):this.preopenSignIn=u},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(u):this.preopenCheckout=u},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(u):this.preopenPlanDetails=u},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(u):this.preopenUserVerification=u},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=u=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(u):this.preopenOneTap=u},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(u):this.preopenUserProfile=u},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(u):this.preopenOrganizationProfile=u},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=u=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(u):this.preopenCreateOrganization=u},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=u=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(u):this.preOpenWaitlist=u},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=u=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(u):this.preopenSignUp=u},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(u,f):this.premountSignInNodes.set(u,f)},this.unmountSignIn=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(u):this.premountSignInNodes.delete(u)},this.mountSignUp=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(u,f):this.premountSignUpNodes.set(u,f)},this.unmountSignUp=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(u):this.premountSignUpNodes.delete(u)},this.mountUserProfile=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(u,f):this.premountUserProfileNodes.set(u,f)},this.unmountUserProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(u):this.premountUserProfileNodes.delete(u)},this.mountOrganizationProfile=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(u,f):this.premountOrganizationProfileNodes.set(u,f)},this.unmountOrganizationProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(u):this.premountOrganizationProfileNodes.delete(u)},this.mountCreateOrganization=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(u,f):this.premountCreateOrganizationNodes.set(u,f)},this.unmountCreateOrganization=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(u):this.premountCreateOrganizationNodes.delete(u)},this.mountOrganizationSwitcher=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(u,f):this.premountOrganizationSwitcherNodes.set(u,f)},this.unmountOrganizationSwitcher=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(u):this.premountOrganizationSwitcherNodes.delete(u)},this.__experimental_prefetchOrganizationSwitcher=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",u)},this.mountOrganizationList=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(u,f):this.premountOrganizationListNodes.set(u,f)},this.unmountOrganizationList=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(u):this.premountOrganizationListNodes.delete(u)},this.mountUserButton=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(u,f):this.premountUserButtonNodes.set(u,f)},this.unmountUserButton=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(u):this.premountUserButtonNodes.delete(u)},this.mountWaitlist=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(u,f):this.premountWaitlistNodes.set(u,f)},this.unmountWaitlist=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(u):this.premountWaitlistNodes.delete(u)},this.mountPricingTable=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(u,f):this.premountPricingTableNodes.set(u,f)},this.unmountPricingTable=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(u):this.premountPricingTableNodes.delete(u)},this.mountApiKeys=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(u,f):this.premountApiKeysNodes.set(u,f)},this.unmountApiKeys=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(u):this.premountApiKeysNodes.delete(u)},this.__internal_mountOAuthConsent=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(u,f):this.premountOAuthConsentNodes.set(u,f)},this.__internal_unmountOAuthConsent=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(u):this.premountOAuthConsentNodes.delete(u)},this.addListener=u=>{if(this.clerkjs)return this.clerkjs.addListener(u);{const f=()=>{var d;const v=this.premountAddListenerCalls.get(u);v&&((d=v.nativeUnsubscribe)==null||d.call(v),this.premountAddListenerCalls.delete(u))};return this.premountAddListenerCalls.set(u,{unsubscribe:f,nativeUnsubscribe:void 0}),f}},this.navigate=u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.navigate(u)};this.clerkjs&&this.loaded?f():this.premountMethodCalls.set("navigate",f)},this.redirectWithAuth=async(...u)=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectWithAuth(...u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectWithAuth",f)},this.redirectToSignIn=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignIn(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectToSignIn",f)},this.redirectToSignUp=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignUp(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectToSignUp",f)},this.redirectToUserProfile=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToUserProfile",u)},this.redirectToAfterSignUp=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToAfterSignUp",u)},this.redirectToAfterSignIn=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignIn()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("redirectToAfterSignIn",u)},this.redirectToAfterSignOut=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignOut()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("redirectToAfterSignOut",u)},this.redirectToOrganizationProfile=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToOrganizationProfile",u)},this.redirectToCreateOrganization=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToCreateOrganization",u)},this.redirectToWaitlist=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToWaitlist",u)},this.handleRedirectCallback=async u=>{var f;const d=()=>{var v;return(v=this.clerkjs)==null?void 0:v.handleRedirectCallback(u)};this.clerkjs&&this.loaded?(f=d())==null||f.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",d)},this.handleGoogleOneTapCallback=async(u,f)=>{var d;const v=()=>{var m;return(m=this.clerkjs)==null?void 0:m.handleGoogleOneTapCallback(u,f)};this.clerkjs&&this.loaded?(d=v())==null||d.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",v)},this.handleEmailLinkVerification=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.handleEmailLinkVerification(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("handleEmailLinkVerification",f)},this.authenticateWithMetamask=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithMetamask(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithMetamask",f)},this.authenticateWithCoinbaseWallet=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithCoinbaseWallet(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",f)},this.authenticateWithOKXWallet=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithOKXWallet(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithOKXWallet",f)},this.authenticateWithWeb3=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithWeb3(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithWeb3",f)},this.authenticateWithGoogleOneTap=async u=>(await Ig(this,Ms,af).call(this)).authenticateWithGoogleOneTap(u),this.createOrganization=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.createOrganization(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("createOrganization",f)},this.getOrganization=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.getOrganization(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("getOrganization",f)},this.joinWaitlist=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.joinWaitlist(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("joinWaitlist",f)},this.signOut=async(...u)=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.signOut(...u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("signOut",f)};const{Clerk:r=null,publishableKey:o}=l||{};Ei(this,ra,o),Ei(this,Ai,l?.proxyUrl),Ei(this,wi,l?.domain),this.options=l,this.Clerk=r,this.mode=Wg()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=sE),Be(this,kn).emit(Cs.Status,"loading"),Be(this,kn).prioritizedOn(Cs.Status,u=>Ei(this,js,u)),Be(this,ra)&&this.loadClerkJS()}get publishableKey(){return Be(this,ra)}get loaded(){var l;return((l=this.clerkjs)==null?void 0:l.loaded)||!1}get status(){var l;return this.clerkjs?((l=this.clerkjs)==null?void 0:l.status)||(this.clerkjs.loaded?"ready":"loading"):Be(this,js)}static getOrCreateInstance(l){return(!Wg()||!Be(this,oa)||l.Clerk&&Be(this,oa).Clerk!==l.Clerk||Be(this,oa).publishableKey!==l.publishableKey)&&Ei(this,oa,new mv(l)),Be(this,oa)}static clearInstance(){Ei(this,oa,null)}get domain(){return typeof window<"u"&&window.location?Uc(Be(this,wi),new URL(window.location.href),""):typeof Be(this,wi)=="function"?hn.throw(Mc):Be(this,wi)||""}get proxyUrl(){return typeof window<"u"&&window.location?Uc(Be(this,Ai),new URL(window.location.href),""):typeof Be(this,Ai)=="function"?hn.throw(Mc):Be(this,Ai)||""}__internal_getOption(l){var r,o;return(r=this.clerkjs)!=null&&r.__internal_getOption?(o=this.clerkjs)==null?void 0:o.__internal_getOption(l):this.options[l]}get sdkMetadata(){var l;return((l=this.clerkjs)==null?void 0:l.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var l;return(l=this.clerkjs)==null?void 0:l.instanceType}get frontendApi(){var l;return((l=this.clerkjs)==null?void 0:l.frontendApi)||""}get isStandardBrowser(){var l;return((l=this.clerkjs)==null?void 0:l.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return typeof window<"u"&&window.location?Uc(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite=="function"?hn.throw(Mc):!1}async loadClerkJS(){var l;if(!(this.mode!=="browser"||this.loaded)){typeof window<"u"&&(window.__clerk_publishable_key=Be(this,ra),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let r;W_(this.Clerk)?(r=new this.Clerk(Be(this,ra),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(r),await r.load(this.options)):(r=this.Clerk,r.loaded||(this.beforeLoad(r),await r.load(this.options))),global.Clerk=r}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await B_({...this.options,publishableKey:Be(this,ra),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}return(l=global.Clerk)!=null&&l.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(r){const o=r;Be(this,kn).emit(Cs.Status,"error"),console.error(o.stack||o.message||o);return}}}get version(){var l;return(l=this.clerkjs)==null?void 0:l.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var l;return(l=this.clerkjs)==null?void 0:l.billing}get apiKeys(){var l;return(l=this.clerkjs)==null?void 0:l.apiKeys}__unstable__setEnvironment(...l){if(this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs)this.clerkjs.__unstable__setEnvironment(l);else return}};js=new WeakMap;wi=new WeakMap;Ai=new WeakMap;ra=new WeakMap;kn=new WeakMap;oa=new WeakMap;Ms=new WeakSet;af=function(){return new Promise(a=>{this.addOnLoaded(()=>a(this.clerkjs))})};Ua(hv,oa);var ap=hv;function oE(a){const{isomorphicClerkOptions:l,initialState:r,children:o}=a,{isomorphicClerk:u,clerkStatus:f}=uE(l),[d,v]=q.useState({client:u.client,session:u.session,user:u.user,organization:u.organization});q.useEffect(()=>u.addListener(ve=>v({...ve})),[]);const m=Y_(u.loaded,d,r),g=q.useMemo(()=>({value:u}),[f]),y=q.useMemo(()=>({value:d.client}),[d.client]),{sessionId:O,sessionStatus:S,sessionClaims:R,session:A,userId:L,user:N,orgId:C,actor:H,organization:$,orgRole:J,orgSlug:Y,orgPermissions:Z,factorVerificationAge:le}=m,W=q.useMemo(()=>({value:{sessionId:O,sessionStatus:S,sessionClaims:R,userId:L,actor:H,orgId:C,orgRole:J,orgSlug:Y,orgPermissions:Z,factorVerificationAge:le}}),[O,S,L,H,C,J,Y,le,R?.__raw]),oe=q.useMemo(()=>({value:A}),[O,A]),ne=q.useMemo(()=>({value:N}),[L,N]),be=q.useMemo(()=>({value:{organization:$}}),[C,$]);return q.createElement(n_.Provider,{value:g},q.createElement(I1.Provider,{value:y},q.createElement(G1.Provider,{value:oe},q.createElement(W1,{...be.value},q.createElement(e_.Provider,{value:W},q.createElement(Y1.Provider,{value:ne},o))))))}var uE=a=>{const l=q.useRef(ap.getOrCreateInstance(a)),[r,o]=q.useState(l.current.status);return q.useEffect(()=>{l.current.__unstable__updateProps({appearance:a.appearance})},[a.appearance]),q.useEffect(()=>{l.current.__unstable__updateProps({options:a})},[a.localization]),q.useEffect(()=>(l.current.on("status",o),()=>{l.current&&l.current.off("status",o),ap.clearInstance()}),[]),{isomorphicClerk:l.current,clerkStatus:r}};function cE(a){const{initialState:l,children:r,__internal_bypassMissingPublishableKey:o,...u}=a,{publishableKey:f="",Clerk:d}=u;return!d&&!o&&(f?f&&!Yc(f)&&hn.throwInvalidPublishableKeyError({key:f}):hn.throwMissingPublishableKeyError()),q.createElement(oE,{initialState:l,isomorphicClerkOptions:u},r)}var gv=Z_(cE,"ClerkProvider",a_);gv.displayName="ClerkProvider";J1({packageName:"@clerk/clerk-react"});q_("@clerk/clerk-react");var Nl={},ip;function fE(){if(ip)return Nl;ip=1,Object.defineProperty(Nl,"__esModule",{value:!0}),Nl.parse=d,Nl.serialize=g;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,u=Object.prototype.toString,f=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function d(S,R){const A=new f,L=S.length;if(L<2)return A;const N=R?.decode||y;let C=0;do{const H=S.indexOf("=",C);if(H===-1)break;const $=S.indexOf(";",C),J=$===-1?L:$;if(H>J){C=S.lastIndexOf(";",H-1)+1;continue}const Y=v(S,C,H),Z=m(S,H,Y),le=S.slice(Y,Z);if(A[le]===void 0){let W=v(S,H+1,J),oe=m(S,J,W);const ne=N(S.slice(W,oe));A[le]=ne}C=J+1}while(C<L);return A}function v(S,R,A){do{const L=S.charCodeAt(R);if(L!==32&&L!==9)return R}while(++R<A);return A}function m(S,R,A){for(;R>A;){const L=S.charCodeAt(--R);if(L!==32&&L!==9)return R+1}return A}function g(S,R,A){const L=A?.encode||encodeURIComponent;if(!a.test(S))throw new TypeError(`argument name is invalid: ${S}`);const N=L(R);if(!l.test(N))throw new TypeError(`argument val is invalid: ${R}`);let C=S+"="+N;if(!A)return C;if(A.maxAge!==void 0){if(!Number.isInteger(A.maxAge))throw new TypeError(`option maxAge is invalid: ${A.maxAge}`);C+="; Max-Age="+A.maxAge}if(A.domain){if(!r.test(A.domain))throw new TypeError(`option domain is invalid: ${A.domain}`);C+="; Domain="+A.domain}if(A.path){if(!o.test(A.path))throw new TypeError(`option path is invalid: ${A.path}`);C+="; Path="+A.path}if(A.expires){if(!O(A.expires)||!Number.isFinite(A.expires.valueOf()))throw new TypeError(`option expires is invalid: ${A.expires}`);C+="; Expires="+A.expires.toUTCString()}if(A.httpOnly&&(C+="; HttpOnly"),A.secure&&(C+="; Secure"),A.partitioned&&(C+="; Partitioned"),A.priority)switch(typeof A.priority=="string"?A.priority.toLowerCase():void 0){case"low":C+="; Priority=Low";break;case"medium":C+="; Priority=Medium";break;case"high":C+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${A.priority}`)}if(A.sameSite)switch(typeof A.sameSite=="string"?A.sameSite.toLowerCase():A.sameSite){case!0:case"strict":C+="; SameSite=Strict";break;case"lax":C+="; SameSite=Lax";break;case"none":C+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${A.sameSite}`)}return C}function y(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function O(S){return u.call(S)==="[object Date]"}return Nl}fE();var lp="popstate";function dE(a={}){function l(o,u){let{pathname:f,search:d,hash:v}=o.location;return lf("",{pathname:f,search:d,hash:v},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function r(o,u){return typeof u=="string"?u:Wl(u)}return mE(l,r,null,a)}function Ne(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}function Jt(a,l){if(!a){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function hE(){return Math.random().toString(36).substring(2,10)}function rp(a,l){return{usr:a.state,key:a.key,idx:l}}function lf(a,l,r=null,o){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof l=="string"?Ui(l):l,state:r,key:l&&l.key||o||hE()}}function Wl({pathname:a="/",search:l="",hash:r=""}){return l&&l!=="?"&&(a+=l.charAt(0)==="?"?l:"?"+l),r&&r!=="#"&&(a+=r.charAt(0)==="#"?r:"#"+r),a}function Ui(a){let l={};if(a){let r=a.indexOf("#");r>=0&&(l.hash=a.substring(r),a=a.substring(0,r));let o=a.indexOf("?");o>=0&&(l.search=a.substring(o),a=a.substring(0,o)),a&&(l.pathname=a)}return l}function mE(a,l,r,o={}){let{window:u=document.defaultView,v5Compat:f=!1}=o,d=u.history,v="POP",m=null,g=y();g==null&&(g=0,d.replaceState({...d.state,idx:g},""));function y(){return(d.state||{idx:null}).idx}function O(){v="POP";let N=y(),C=N==null?null:N-g;g=N,m&&m({action:v,location:L.location,delta:C})}function S(N,C){v="PUSH";let H=lf(L.location,N,C);g=y()+1;let $=rp(H,g),J=L.createHref(H);try{d.pushState($,"",J)}catch(Y){if(Y instanceof DOMException&&Y.name==="DataCloneError")throw Y;u.location.assign(J)}f&&m&&m({action:v,location:L.location,delta:1})}function R(N,C){v="REPLACE";let H=lf(L.location,N,C);g=y();let $=rp(H,g),J=L.createHref(H);d.replaceState($,"",J),f&&m&&m({action:v,location:L.location,delta:0})}function A(N){return gE(N)}let L={get action(){return v},get location(){return a(u,d)},listen(N){if(m)throw new Error("A history only accepts one active listener");return u.addEventListener(lp,O),m=N,()=>{u.removeEventListener(lp,O),m=null}},createHref(N){return l(u,N)},createURL:A,encodeLocation(N){let C=A(N);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:S,replace:R,go(N){return d.go(N)}};return L}function gE(a,l=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Ne(r,"No window.location.(origin|href) available to create URL");let o=typeof a=="string"?a:Wl(a);return o=o.replace(/ $/,"%20"),!l&&o.startsWith("//")&&(o=r+o),new URL(o,r)}function pv(a,l,r="/"){return pE(a,l,r,!1)}function pE(a,l,r,o){let u=typeof l=="string"?Ui(l):l,f=Dn(u.pathname||"/",r);if(f==null)return null;let d=vv(a);vE(d);let v=null;for(let m=0;v==null&&m<d.length;++m){let g=OE(f);v=CE(d[m],g,o)}return v}function vv(a,l=[],r=[],o=""){let u=(f,d,v)=>{let m={relativePath:v===void 0?f.path||"":v,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};m.relativePath.startsWith("/")&&(Ne(m.relativePath.startsWith(o),`Absolute route path "${m.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),m.relativePath=m.relativePath.slice(o.length));let g=Mn([o,m.relativePath]),y=r.concat(m);f.children&&f.children.length>0&&(Ne(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${g}".`),vv(f.children,l,y,g)),!(f.path==null&&!f.index)&&l.push({path:g,score:wE(g,f.index),routesMeta:y})};return a.forEach((f,d)=>{if(f.path===""||!f.path?.includes("?"))u(f,d);else for(let v of yv(f.path))u(f,d,v)}),l}function yv(a){let l=a.split("/");if(l.length===0)return[];let[r,...o]=l,u=r.endsWith("?"),f=r.replace(/\?$/,"");if(o.length===0)return u?[f,""]:[f];let d=yv(o.join("/")),v=[];return v.push(...d.map(m=>m===""?f:[f,m].join("/"))),u&&v.push(...d),v.map(m=>a.startsWith("/")&&m===""?"/":m)}function vE(a){a.sort((l,r)=>l.score!==r.score?r.score-l.score:AE(l.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}var yE=/^:[\w-]+$/,bE=3,SE=2,_E=1,EE=10,xE=-2,sp=a=>a==="*";function wE(a,l){let r=a.split("/"),o=r.length;return r.some(sp)&&(o+=xE),l&&(o+=SE),r.filter(u=>!sp(u)).reduce((u,f)=>u+(yE.test(f)?bE:f===""?_E:EE),o)}function AE(a,l){return a.length===l.length&&a.slice(0,-1).every((o,u)=>o===l[u])?a[a.length-1]-l[l.length-1]:0}function CE(a,l,r=!1){let{routesMeta:o}=a,u={},f="/",d=[];for(let v=0;v<o.length;++v){let m=o[v],g=v===o.length-1,y=f==="/"?l:l.slice(f.length)||"/",O=Bs({path:m.relativePath,caseSensitive:m.caseSensitive,end:g},y),S=m.route;if(!O&&g&&r&&!o[o.length-1].route.index&&(O=Bs({path:m.relativePath,caseSensitive:m.caseSensitive,end:!1},y)),!O)return null;Object.assign(u,O.params),d.push({params:u,pathname:Mn([f,O.pathname]),pathnameBase:ME(Mn([f,O.pathnameBase])),route:S}),O.pathnameBase!=="/"&&(f=Mn([f,O.pathnameBase]))}return d}function Bs(a,l){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[r,o]=TE(a.path,a.caseSensitive,a.end),u=l.match(r);if(!u)return null;let f=u[0],d=f.replace(/(.)\/+$/,"$1"),v=u.slice(1);return{params:o.reduce((g,{paramName:y,isOptional:O},S)=>{if(y==="*"){let A=v[S]||"";d=f.slice(0,f.length-A.length).replace(/(.)\/+$/,"$1")}const R=v[S];return O&&!R?g[y]=void 0:g[y]=(R||"").replace(/%2F/g,"/"),g},{}),pathname:f,pathnameBase:d,pattern:a}}function TE(a,l=!1,r=!0){Jt(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let o=[],u="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,v,m)=>(o.push({paramName:v,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(o.push({paramName:"*"}),u+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?u+="\\/*$":a!==""&&a!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,l?void 0:"i"),o]}function OE(a){try{return a.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return Jt(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),a}}function Dn(a,l){if(l==="/")return a;if(!a.toLowerCase().startsWith(l.toLowerCase()))return null;let r=l.endsWith("/")?l.length-1:l.length,o=a.charAt(r);return o&&o!=="/"?null:a.slice(r)||"/"}function RE(a,l="/"){let{pathname:r,search:o="",hash:u=""}=typeof a=="string"?Ui(a):a;return{pathname:r?r.startsWith("/")?r:kE(r,l):l,search:UE(o),hash:DE(u)}}function kE(a,l){let r=l.replace(/\/+$/,"").split("/");return a.split("/").forEach(u=>{u===".."?r.length>1&&r.pop():u!=="."&&r.push(u)}),r.length>1?r.join("/"):"/"}function qc(a,l,r,o){return`Cannot include a '${a}' character in a manually specified \`to.${l}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function jE(a){return a.filter((l,r)=>r===0||l.route.path&&l.route.path.length>0)}function mf(a){let l=jE(a);return l.map((r,o)=>o===l.length-1?r.pathname:r.pathnameBase)}function gf(a,l,r,o=!1){let u;typeof a=="string"?u=Ui(a):(u={...a},Ne(!u.pathname||!u.pathname.includes("?"),qc("?","pathname","search",u)),Ne(!u.pathname||!u.pathname.includes("#"),qc("#","pathname","hash",u)),Ne(!u.search||!u.search.includes("#"),qc("#","search","hash",u)));let f=a===""||u.pathname==="",d=f?"/":u.pathname,v;if(d==null)v=r;else{let O=l.length-1;if(!o&&d.startsWith("..")){let S=d.split("/");for(;S[0]==="..";)S.shift(),O-=1;u.pathname=S.join("/")}v=O>=0?l[O]:"/"}let m=RE(u,v),g=d&&d!=="/"&&d.endsWith("/"),y=(f||d===".")&&r.endsWith("/");return!m.pathname.endsWith("/")&&(g||y)&&(m.pathname+="/"),m}var Mn=a=>a.join("/").replace(/\/\/+/g,"/"),ME=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),UE=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,DE=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function LE(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var bv=["POST","PUT","PATCH","DELETE"];new Set(bv);var NE=["GET",...bv];new Set(NE);var Di=w.createContext(null);Di.displayName="DataRouter";var Ws=w.createContext(null);Ws.displayName="DataRouterState";var Sv=w.createContext({isTransitioning:!1});Sv.displayName="ViewTransition";var zE=w.createContext(new Map);zE.displayName="Fetchers";var qE=w.createContext(null);qE.displayName="Await";var en=w.createContext(null);en.displayName="Navigation";var er=w.createContext(null);er.displayName="Location";var tn=w.createContext({outlet:null,matches:[],isDataRoute:!1});tn.displayName="Route";var pf=w.createContext(null);pf.displayName="RouteError";function BE(a,{relative:l}={}){Ne(Li(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=w.useContext(en),{hash:u,pathname:f,search:d}=tr(a,{relative:l}),v=f;return r!=="/"&&(v=f==="/"?r:Mn([r,f])),o.createHref({pathname:v,search:d,hash:u})}function Li(){return w.useContext(er)!=null}function da(){return Ne(Li(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(er).location}var _v="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Ev(a){w.useContext(en).static||w.useLayoutEffect(a)}function xv(){let{isDataRoute:a}=w.useContext(tn);return a?FE():HE()}function HE(){Ne(Li(),"useNavigate() may be used only in the context of a <Router> component.");let a=w.useContext(Di),{basename:l,navigator:r}=w.useContext(en),{matches:o}=w.useContext(tn),{pathname:u}=da(),f=JSON.stringify(mf(o)),d=w.useRef(!1);return Ev(()=>{d.current=!0}),w.useCallback((m,g={})=>{if(Jt(d.current,_v),!d.current)return;if(typeof m=="number"){r.go(m);return}let y=gf(m,JSON.parse(f),u,g.relative==="path");a==null&&l!=="/"&&(y.pathname=y.pathname==="/"?l:Mn([l,y.pathname])),(g.replace?r.replace:r.push)(y,g.state,g)},[l,r,f,u,a])}w.createContext(null);function vw(){let{matches:a}=w.useContext(tn),l=a[a.length-1];return l?l.params:{}}function tr(a,{relative:l}={}){let{matches:r}=w.useContext(tn),{pathname:o}=da(),u=JSON.stringify(mf(r));return w.useMemo(()=>gf(a,JSON.parse(u),o,l==="path"),[a,u,o,l])}function VE(a,l){return wv(a,l)}function wv(a,l,r,o){Ne(Li(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:u}=w.useContext(en),{matches:f}=w.useContext(tn),d=f[f.length-1],v=d?d.params:{},m=d?d.pathname:"/",g=d?d.pathnameBase:"/",y=d&&d.route;{let C=y&&y.path||"";Av(m,!y||C.endsWith("*")||C.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${C}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${C}"> to <Route path="${C==="/"?"*":`${C}/*`}">.`)}let O=da(),S;if(l){let C=typeof l=="string"?Ui(l):l;Ne(g==="/"||C.pathname?.startsWith(g),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${C.pathname}" was given in the \`location\` prop.`),S=C}else S=O;let R=S.pathname||"/",A=R;if(g!=="/"){let C=g.replace(/^\//,"").split("/");A="/"+R.replace(/^\//,"").split("/").slice(C.length).join("/")}let L=pv(a,{pathname:A});Jt(y||L!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),Jt(L==null||L[L.length-1].route.element!==void 0||L[L.length-1].route.Component!==void 0||L[L.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let N=IE(L&&L.map(C=>Object.assign({},C,{params:Object.assign({},v,C.params),pathname:Mn([g,u.encodeLocation?u.encodeLocation(C.pathname).pathname:C.pathname]),pathnameBase:C.pathnameBase==="/"?g:Mn([g,u.encodeLocation?u.encodeLocation(C.pathnameBase).pathname:C.pathnameBase])})),f,r,o);return l&&N?w.createElement(er.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},N):N}function QE(){let a=ZE(),l=LE(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),r=a instanceof Error?a.stack:null,o="rgba(200,200,200, 0.5)",u={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},d=null;return console.error("Error handled by React Router default ErrorBoundary:",a),d=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:f},"ErrorBoundary")," or"," ",w.createElement("code",{style:f},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},l),r?w.createElement("pre",{style:u},r):null,d)}var PE=w.createElement(QE,null),YE=class extends w.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,l){return l.location!==a.location||l.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:l.error,location:l.location,revalidation:a.revalidation||l.revalidation}}componentDidCatch(a,l){console.error("React Router caught the following error during render",a,l)}render(){return this.state.error!==void 0?w.createElement(tn.Provider,{value:this.props.routeContext},w.createElement(pf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function $E({routeContext:a,match:l,children:r}){let o=w.useContext(Di);return o&&o.static&&o.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=l.route.id),w.createElement(tn.Provider,{value:a},r)}function IE(a,l=[],r=null,o=null){if(a==null){if(!r)return null;if(r.errors)a=r.matches;else if(l.length===0&&!r.initialized&&r.matches.length>0)a=r.matches;else return null}let u=a,f=r?.errors;if(f!=null){let m=u.findIndex(g=>g.route.id&&f?.[g.route.id]!==void 0);Ne(m>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),u=u.slice(0,Math.min(u.length,m+1))}let d=!1,v=-1;if(r)for(let m=0;m<u.length;m++){let g=u[m];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(v=m),g.route.id){let{loaderData:y,errors:O}=r,S=g.route.loader&&!y.hasOwnProperty(g.route.id)&&(!O||O[g.route.id]===void 0);if(g.route.lazy||S){d=!0,v>=0?u=u.slice(0,v+1):u=[u[0]];break}}}return u.reduceRight((m,g,y)=>{let O,S=!1,R=null,A=null;r&&(O=f&&g.route.id?f[g.route.id]:void 0,R=g.route.errorElement||PE,d&&(v<0&&y===0?(Av("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,A=null):v===y&&(S=!0,A=g.route.hydrateFallbackElement||null)));let L=l.concat(u.slice(0,y+1)),N=()=>{let C;return O?C=R:S?C=A:g.route.Component?C=w.createElement(g.route.Component,null):g.route.element?C=g.route.element:C=m,w.createElement($E,{match:g,routeContext:{outlet:m,matches:L,isDataRoute:r!=null},children:C})};return r&&(g.route.ErrorBoundary||g.route.errorElement||y===0)?w.createElement(YE,{location:r.location,revalidation:r.revalidation,component:R,error:O,children:N(),routeContext:{outlet:null,matches:L,isDataRoute:!0}}):N()},null)}function vf(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function GE(a){let l=w.useContext(Di);return Ne(l,vf(a)),l}function XE(a){let l=w.useContext(Ws);return Ne(l,vf(a)),l}function WE(a){let l=w.useContext(tn);return Ne(l,vf(a)),l}function yf(a){let l=WE(a),r=l.matches[l.matches.length-1];return Ne(r.route.id,`${a} can only be used on routes that contain a unique "id"`),r.route.id}function KE(){return yf("useRouteId")}function ZE(){let a=w.useContext(pf),l=XE("useRouteError"),r=yf("useRouteError");return a!==void 0?a:l.errors?.[r]}function FE(){let{router:a}=GE("useNavigate"),l=yf("useNavigate"),r=w.useRef(!1);return Ev(()=>{r.current=!0}),w.useCallback(async(u,f={})=>{Jt(r.current,_v),r.current&&(typeof u=="number"?a.navigate(u):await a.navigate(u,{fromRouteId:l,...f}))},[a,l])}var op={};function Av(a,l,r){!l&&!op[a]&&(op[a]=!0,Jt(!1,r))}w.memo(JE);function JE({routes:a,future:l,state:r}){return wv(a,void 0,r,l)}function Rs({to:a,replace:l,state:r,relative:o}){Ne(Li(),"<Navigate> may be used only in the context of a <Router> component.");let{static:u}=w.useContext(en);Jt(!u,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=w.useContext(tn),{pathname:d}=da(),v=xv(),m=gf(a,mf(f),d,o==="path"),g=JSON.stringify(m);return w.useEffect(()=>{v(JSON.parse(g),{replace:l,state:r,relative:o})},[v,g,o,l,r]),null}function Da(a){Ne(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ex({basename:a="/",children:l=null,location:r,navigationType:o="POP",navigator:u,static:f=!1}){Ne(!Li(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=a.replace(/^\/*/,"/"),v=w.useMemo(()=>({basename:d,navigator:u,static:f,future:{}}),[d,u,f]);typeof r=="string"&&(r=Ui(r));let{pathname:m="/",search:g="",hash:y="",state:O=null,key:S="default"}=r,R=w.useMemo(()=>{let A=Dn(m,d);return A==null?null:{location:{pathname:A,search:g,hash:y,state:O,key:S},navigationType:o}},[d,m,g,y,O,S,o]);return Jt(R!=null,`<Router basename="${d}"> is not able to match the URL "${m}${g}${y}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:w.createElement(en.Provider,{value:v},w.createElement(er.Provider,{children:l,value:R}))}function tx({children:a,location:l}){return VE(rf(a),l)}function rf(a,l=[]){let r=[];return w.Children.forEach(a,(o,u)=>{if(!w.isValidElement(o))return;let f=[...l,u];if(o.type===w.Fragment){r.push.apply(r,rf(o.props.children,f));return}Ne(o.type===Da,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ne(!o.props.index||!o.props.children,"An index route cannot have child routes.");let d={id:o.props.id||f.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(d.children=rf(o.props.children,f)),r.push(d)}),r}var Us="get",Ds="application/x-www-form-urlencoded";function Ks(a){return a!=null&&typeof a.tagName=="string"}function nx(a){return Ks(a)&&a.tagName.toLowerCase()==="button"}function ax(a){return Ks(a)&&a.tagName.toLowerCase()==="form"}function ix(a){return Ks(a)&&a.tagName.toLowerCase()==="input"}function lx(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function rx(a,l){return a.button===0&&(!l||l==="_self")&&!lx(a)}var ks=null;function sx(){if(ks===null)try{new FormData(document.createElement("form"),0),ks=!1}catch{ks=!0}return ks}var ox=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Bc(a){return a!=null&&!ox.has(a)?(Jt(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ds}"`),null):a}function ux(a,l){let r,o,u,f,d;if(ax(a)){let v=a.getAttribute("action");o=v?Dn(v,l):null,r=a.getAttribute("method")||Us,u=Bc(a.getAttribute("enctype"))||Ds,f=new FormData(a)}else if(nx(a)||ix(a)&&(a.type==="submit"||a.type==="image")){let v=a.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let m=a.getAttribute("formaction")||v.getAttribute("action");if(o=m?Dn(m,l):null,r=a.getAttribute("formmethod")||v.getAttribute("method")||Us,u=Bc(a.getAttribute("formenctype"))||Bc(v.getAttribute("enctype"))||Ds,f=new FormData(v,a),!sx()){let{name:g,type:y,value:O}=a;if(y==="image"){let S=g?`${g}.`:"";f.append(`${S}x`,"0"),f.append(`${S}y`,"0")}else g&&f.append(g,O)}}else{if(Ks(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Us,o=null,u=Ds,d=a}return f&&u==="text/plain"&&(d=f,f=void 0),{action:o,method:r.toLowerCase(),encType:u,formData:f,body:d}}function bf(a,l){if(a===!1||a===null||typeof a>"u")throw new Error(l)}async function cx(a,l){if(a.id in l)return l[a.id];try{let r=await import(a.module);return l[a.id]=r,r}catch(r){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function fx(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function dx(a,l,r){let o=await Promise.all(a.map(async u=>{let f=l.routes[u.route.id];if(f){let d=await cx(f,r);return d.links?d.links():[]}return[]}));return px(o.flat(1).filter(fx).filter(u=>u.rel==="stylesheet"||u.rel==="preload").map(u=>u.rel==="stylesheet"?{...u,rel:"prefetch",as:"style"}:{...u,rel:"prefetch"}))}function up(a,l,r,o,u,f){let d=(m,g)=>r[g]?m.route.id!==r[g].route.id:!0,v=(m,g)=>r[g].pathname!==m.pathname||r[g].route.path?.endsWith("*")&&r[g].params["*"]!==m.params["*"];return f==="assets"?l.filter((m,g)=>d(m,g)||v(m,g)):f==="data"?l.filter((m,g)=>{let y=o.routes[m.route.id];if(!y||!y.hasLoader)return!1;if(d(m,g)||v(m,g))return!0;if(m.route.shouldRevalidate){let O=m.route.shouldRevalidate({currentUrl:new URL(u.pathname+u.search+u.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(a,window.origin),nextParams:m.params,defaultShouldRevalidate:!0});if(typeof O=="boolean")return O}return!0}):[]}function hx(a,l,{includeHydrateFallback:r}={}){return mx(a.map(o=>{let u=l.routes[o.route.id];if(!u)return[];let f=[u.module];return u.clientActionModule&&(f=f.concat(u.clientActionModule)),u.clientLoaderModule&&(f=f.concat(u.clientLoaderModule)),r&&u.hydrateFallbackModule&&(f=f.concat(u.hydrateFallbackModule)),u.imports&&(f=f.concat(u.imports)),f}).flat(1))}function mx(a){return[...new Set(a)]}function gx(a){let l={},r=Object.keys(a).sort();for(let o of r)l[o]=a[o];return l}function px(a,l){let r=new Set;return new Set(l),a.reduce((o,u)=>{let f=JSON.stringify(gx(u));return r.has(f)||(r.add(f),o.push({key:f,link:u})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var vx=new Set([100,101,204,205]);function yx(a,l){let r=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return r.pathname==="/"?r.pathname="_root.data":l&&Dn(r.pathname,l)==="/"?r.pathname=`${l.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Cv(){let a=w.useContext(Di);return bf(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function bx(){let a=w.useContext(Ws);return bf(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Sf=w.createContext(void 0);Sf.displayName="FrameworkContext";function Tv(){let a=w.useContext(Sf);return bf(a,"You must render this element inside a <HydratedRouter> element"),a}function Sx(a,l){let r=w.useContext(Sf),[o,u]=w.useState(!1),[f,d]=w.useState(!1),{onFocus:v,onBlur:m,onMouseEnter:g,onMouseLeave:y,onTouchStart:O}=l,S=w.useRef(null);w.useEffect(()=>{if(a==="render"&&d(!0),a==="viewport"){let L=C=>{C.forEach(H=>{d(H.isIntersecting)})},N=new IntersectionObserver(L,{threshold:.5});return S.current&&N.observe(S.current),()=>{N.disconnect()}}},[a]),w.useEffect(()=>{if(o){let L=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(L)}}},[o]);let R=()=>{u(!0)},A=()=>{u(!1),d(!1)};return r?a!=="intent"?[f,S,{}]:[f,S,{onFocus:zl(v,R),onBlur:zl(m,A),onMouseEnter:zl(g,R),onMouseLeave:zl(y,A),onTouchStart:zl(O,R)}]:[!1,S,{}]}function zl(a,l){return r=>{a&&a(r),r.defaultPrevented||l(r)}}function _x({page:a,...l}){let{router:r}=Cv(),o=w.useMemo(()=>pv(r.routes,a,r.basename),[r.routes,a,r.basename]);return o?w.createElement(xx,{page:a,matches:o,...l}):null}function Ex(a){let{manifest:l,routeModules:r}=Tv(),[o,u]=w.useState([]);return w.useEffect(()=>{let f=!1;return dx(a,l,r).then(d=>{f||u(d)}),()=>{f=!0}},[a,l,r]),o}function xx({page:a,matches:l,...r}){let o=da(),{manifest:u,routeModules:f}=Tv(),{basename:d}=Cv(),{loaderData:v,matches:m}=bx(),g=w.useMemo(()=>up(a,l,m,u,o,"data"),[a,l,m,u,o]),y=w.useMemo(()=>up(a,l,m,u,o,"assets"),[a,l,m,u,o]),O=w.useMemo(()=>{if(a===o.pathname+o.search+o.hash)return[];let A=new Set,L=!1;if(l.forEach(C=>{let H=u.routes[C.route.id];!H||!H.hasLoader||(!g.some($=>$.route.id===C.route.id)&&C.route.id in v&&f[C.route.id]?.shouldRevalidate||H.hasClientLoader?L=!0:A.add(C.route.id))}),A.size===0)return[];let N=yx(a,d);return L&&A.size>0&&N.searchParams.set("_routes",l.filter(C=>A.has(C.route.id)).map(C=>C.route.id).join(",")),[N.pathname+N.search]},[d,v,o,u,g,l,a,f]),S=w.useMemo(()=>hx(y,u),[y,u]),R=Ex(y);return w.createElement(w.Fragment,null,O.map(A=>w.createElement("link",{key:A,rel:"prefetch",as:"fetch",href:A,...r})),S.map(A=>w.createElement("link",{key:A,rel:"modulepreload",href:A,...r})),R.map(({key:A,link:L})=>w.createElement("link",{key:A,...L})))}function wx(...a){return l=>{a.forEach(r=>{typeof r=="function"?r(l):r!=null&&(r.current=l)})}}var Ov=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ov&&(window.__reactRouterVersion="7.6.2")}catch{}function Ax({basename:a,children:l,window:r}){let o=w.useRef();o.current==null&&(o.current=dE({window:r,v5Compat:!0}));let u=o.current,[f,d]=w.useState({action:u.action,location:u.location}),v=w.useCallback(m=>{w.startTransition(()=>d(m))},[d]);return w.useLayoutEffect(()=>u.listen(v),[u,v]),w.createElement(ex,{basename:a,children:l,location:f.location,navigationType:f.action,navigator:u})}var Rv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,kv=w.forwardRef(function({onClick:l,discover:r="render",prefetch:o="none",relative:u,reloadDocument:f,replace:d,state:v,target:m,to:g,preventScrollReset:y,viewTransition:O,...S},R){let{basename:A}=w.useContext(en),L=typeof g=="string"&&Rv.test(g),N,C=!1;if(typeof g=="string"&&L&&(N=g,Ov))try{let oe=new URL(window.location.href),ne=g.startsWith("//")?new URL(oe.protocol+g):new URL(g),be=Dn(ne.pathname,A);ne.origin===oe.origin&&be!=null?g=be+ne.search+ne.hash:C=!0}catch{Jt(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let H=BE(g,{relative:u}),[$,J,Y]=Sx(o,S),Z=Rx(g,{replace:d,state:v,target:m,preventScrollReset:y,relative:u,viewTransition:O});function le(oe){l&&l(oe),oe.defaultPrevented||Z(oe)}let W=w.createElement("a",{...S,...Y,href:N||H,onClick:C||f?l:le,ref:wx(R,J),target:m,"data-discover":!L&&r==="render"?"true":void 0});return $&&!L?w.createElement(w.Fragment,null,W,w.createElement(_x,{page:H})):W});kv.displayName="Link";var Cx=w.forwardRef(function({"aria-current":l="page",caseSensitive:r=!1,className:o="",end:u=!1,style:f,to:d,viewTransition:v,children:m,...g},y){let O=tr(d,{relative:g.relative}),S=da(),R=w.useContext(Ws),{navigator:A,basename:L}=w.useContext(en),N=R!=null&&Dx(O)&&v===!0,C=A.encodeLocation?A.encodeLocation(O).pathname:O.pathname,H=S.pathname,$=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;r||(H=H.toLowerCase(),$=$?$.toLowerCase():null,C=C.toLowerCase()),$&&L&&($=Dn($,L)||$);const J=C!=="/"&&C.endsWith("/")?C.length-1:C.length;let Y=H===C||!u&&H.startsWith(C)&&H.charAt(J)==="/",Z=$!=null&&($===C||!u&&$.startsWith(C)&&$.charAt(C.length)==="/"),le={isActive:Y,isPending:Z,isTransitioning:N},W=Y?l:void 0,oe;typeof o=="function"?oe=o(le):oe=[o,Y?"active":null,Z?"pending":null,N?"transitioning":null].filter(Boolean).join(" ");let ne=typeof f=="function"?f(le):f;return w.createElement(kv,{...g,"aria-current":W,className:oe,ref:y,style:ne,to:d,viewTransition:v},typeof m=="function"?m(le):m)});Cx.displayName="NavLink";var Tx=w.forwardRef(({discover:a="render",fetcherKey:l,navigate:r,reloadDocument:o,replace:u,state:f,method:d=Us,action:v,onSubmit:m,relative:g,preventScrollReset:y,viewTransition:O,...S},R)=>{let A=Mx(),L=Ux(v,{relative:g}),N=d.toLowerCase()==="get"?"get":"post",C=typeof v=="string"&&Rv.test(v),H=$=>{if(m&&m($),$.defaultPrevented)return;$.preventDefault();let J=$.nativeEvent.submitter,Y=J?.getAttribute("formmethod")||d;A(J||$.currentTarget,{fetcherKey:l,method:Y,navigate:r,replace:u,state:f,relative:g,preventScrollReset:y,viewTransition:O})};return w.createElement("form",{ref:R,method:N,action:L,onSubmit:o?m:H,...S,"data-discover":!C&&a==="render"?"true":void 0})});Tx.displayName="Form";function Ox(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function jv(a){let l=w.useContext(Di);return Ne(l,Ox(a)),l}function Rx(a,{target:l,replace:r,state:o,preventScrollReset:u,relative:f,viewTransition:d}={}){let v=xv(),m=da(),g=tr(a,{relative:f});return w.useCallback(y=>{if(rx(y,l)){y.preventDefault();let O=r!==void 0?r:Wl(m)===Wl(g);v(a,{replace:O,state:o,preventScrollReset:u,relative:f,viewTransition:d})}},[m,v,g,r,o,l,a,u,f,d])}var kx=0,jx=()=>`__${String(++kx)}__`;function Mx(){let{router:a}=jv("useSubmit"),{basename:l}=w.useContext(en),r=KE();return w.useCallback(async(o,u={})=>{let{action:f,method:d,encType:v,formData:m,body:g}=ux(o,l);if(u.navigate===!1){let y=u.fetcherKey||jx();await a.fetch(y,r,u.action||f,{preventScrollReset:u.preventScrollReset,formData:m,body:g,formMethod:u.method||d,formEncType:u.encType||v,flushSync:u.flushSync})}else await a.navigate(u.action||f,{preventScrollReset:u.preventScrollReset,formData:m,body:g,formMethod:u.method||d,formEncType:u.encType||v,replace:u.replace,state:u.state,fromRouteId:r,flushSync:u.flushSync,viewTransition:u.viewTransition})},[a,l,r])}function Ux(a,{relative:l}={}){let{basename:r}=w.useContext(en),o=w.useContext(tn);Ne(o,"useFormAction must be used inside a RouteContext");let[u]=o.matches.slice(-1),f={...tr(a||".",{relative:l})},d=da();if(a==null){f.search=d.search;let v=new URLSearchParams(f.search),m=v.getAll("index");if(m.some(y=>y==="")){v.delete("index"),m.filter(O=>O).forEach(O=>v.append("index",O));let y=v.toString();f.search=y?`?${y}`:""}}return(!a||a===".")&&u.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:Mn([r,f.pathname])),Wl(f)}function Dx(a,l={}){let r=w.useContext(Sv);Ne(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=jv("useViewTransitionState"),u=tr(a,{relative:l.relative});if(!r.isTransitioning)return!1;let f=Dn(r.currentLocation.pathname,o)||r.currentLocation.pathname,d=Dn(r.nextLocation.pathname,o)||r.nextLocation.pathname;return Bs(u.pathname,d)!=null||Bs(u.pathname,f)!=null}[...vx];function Mv(){const[a,l]=w.useState(!1),[r,o]=w.useState(!1),[u,f]=w.useState(navigator.onLine),[d,v]=w.useState(!1),[m,g]=w.useState(null),[y,O]=w.useState(null);w.useEffect(()=>{(()=>{const Y=window.matchMedia("(display-mode: standalone)").matches,Z=window.navigator.standalone===!0;o(Y||Z)})();const C=Y=>{Y.preventDefault(),g(Y),l(!0)},H=()=>{o(!0),l(!1),g(null),console.log("[PWA] App installed successfully")},$=()=>f(!0),J=()=>f(!1);return window.addEventListener("beforeinstallprompt",C),window.addEventListener("appinstalled",H),window.addEventListener("online",$),window.addEventListener("offline",J),S(),()=>{window.removeEventListener("beforeinstallprompt",C),window.removeEventListener("appinstalled",H),window.removeEventListener("online",$),window.removeEventListener("offline",J)}},[]);const S=async()=>{if("serviceWorker"in navigator)try{const N=await navigator.serviceWorker.register("/sw.js",{scope:"/"});O(N),N.addEventListener("updatefound",()=>{const C=N.installing;C&&C.addEventListener("statechange",()=>{C.state==="installed"&&navigator.serviceWorker.controller&&(v(!0),console.log("[PWA] New version available"))})}),navigator.serviceWorker.addEventListener("controllerchange",()=>{window.location.reload()}),console.log("[PWA] Service Worker registered successfully")}catch(N){console.error("[PWA] Service Worker registration failed:",N)}};return{isInstallable:a,isInstalled:r,isOnline:u,isUpdateAvailable:d,installPrompt:m,installApp:async()=>{if(!m)return console.warn("[PWA] No install prompt available"),!1;try{return await m.prompt(),(await m.userChoice).outcome==="accepted"?(console.log("[PWA] User accepted the install prompt"),l(!1),g(null),!0):(console.log("[PWA] User dismissed the install prompt"),!1)}catch(N){return console.error("[PWA] Install failed:",N),!1}},updateApp:async()=>{if(!y){console.warn("[PWA] No service worker registration available");return}try{const N=y.waiting;N&&(N.postMessage({type:"SKIP_WAITING"}),v(!1))}catch(N){console.error("[PWA] Update failed:",N)}},registerForNotifications:async()=>{if(!("Notification"in window)||!y)return console.warn("[PWA] Notifications not supported"),!1;try{if(await Notification.requestPermission()==="granted"){console.log("[PWA] Notification permission granted");const C=await y.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:Lx("YOUR_VAPID_PUBLIC_KEY")});return console.log("[PWA] Push subscription created:",C),!0}else return console.log("[PWA] Notification permission denied"),!1}catch(N){return console.error("[PWA] Notification registration failed:",N),!1}}}}function Lx(a){const l="=".repeat((4-a.length%4)%4),r=(a+l).replace(/-/g,"+").replace(/_/g,"/"),o=window.atob(r),u=new Uint8Array(o.length);for(let f=0;f<o.length;++f)u[f]=o.charCodeAt(f);return u}const Nx={isPWA:()=>{const a=window.matchMedia("(display-mode: standalone)").matches,l=window.navigator.standalone===!0;return a||l},getInstallInstructions:()=>{const a=navigator.userAgent.toLowerCase();return a.includes("chrome")&&!a.includes("edg")?'Trykk på meny-ikonet (⋮) og velg "Installer JobbLogg"':a.includes("firefox")?'Trykk på adresselinjen og velg "Installer denne siden som app"':a.includes("safari")?'Trykk på Del-knappen og velg "Legg til på hjemskjerm"':a.includes("edg")?'Trykk på meny-ikonet (⋯) og velg "Installer denne siden som app"':"Se nettleserens meny for å installere som app"},shareContent:async a=>{if(navigator.share)try{return await navigator.share(a),!0}catch(l){return console.error("[PWA] Share failed:",l),!1}else try{const l=`${a.title}
${a.text}${a.url?`
${a.url}`:""}`;return await navigator.clipboard.writeText(l),!0}catch(l){return console.error("[PWA] Clipboard fallback failed:",l),!1}}},ji=({children:a,onClick:l,type:r="button",disabled:o=!1,className:u="",loading:f=!1,icon:d,variant:v="primary",size:m="md",fullWidth:g=!1})=>{const y=()=>{!o&&!f&&l&&l()},O=A=>{(A.key==="Enter"||A.key===" ")&&!o&&!f&&(A.preventDefault(),l&&l())},S=()=>{switch(v){case"secondary":return"btn-secondary-solid";case"outline":return"btn-outline";case"ghost":return"btn-ghost-enhanced";case"danger":return"btn-error-soft";default:return"btn-primary-solid"}},R=()=>{switch(m){case"sm":return"px-3 py-1.5 text-sm";case"lg":return"px-6 py-3 text-lg";default:return"px-4 py-2 text-base"}};return x.jsx("button",{type:r,onClick:y,onKeyDown:O,disabled:o||f,className:`
        ${S()}
        ${R()}
        ${g?"w-full":""}
        ${u}
      `.trim().replace(/\s+/g," "),"aria-disabled":o||f,children:f?x.jsxs(x.Fragment,{children:[x.jsx("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"}),x.jsx("span",{children:"Laster..."})]}):x.jsxs(x.Fragment,{children:[d&&x.jsx("span",{className:"flex-shrink-0",children:d}),x.jsx("span",{children:a})]})})},zx=({children:a,as:l="p",className:r=""})=>x.jsx(l,{className:`
        text-jobblogg-text-strong font-semibold
        ${r}
      `.trim().replace(/\s+/g," "),children:a}),cp=({children:a,as:l="p",className:r=""})=>x.jsx(l,{className:`
        text-jobblogg-text-medium
        ${r}
      `.trim().replace(/\s+/g," "),children:a}),fp=({children:a,as:l="p",className:r=""})=>x.jsx(l,{className:`
        text-jobblogg-text-muted text-small
        ${r}
      `.trim().replace(/\s+/g," "),children:a}),qx=w.forwardRef(({label:a,error:l,helperText:r,required:o=!1,disabled:u=!1,size:f="medium",fullWidth:d=!1,startIcon:v,endIcon:m,className:g="",id:y,"aria-describedby":O,...S},R)=>{const A=y||`text-input-${Math.random().toString(36).substr(2,9)}`,L=l?`${A}-error`:void 0,N=r?`${A}-helper`:void 0,C=[O,L,N].filter(Boolean).join(" ")||void 0,$=`
      input-modern
      ${l?"border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error":""}
      ${{small:"h-8 text-sm px-3",medium:"h-10 text-base px-4",large:"h-12 text-lg px-4"}[f]}
      ${d?"w-full":""}
      ${v?"pl-10":""}
      ${m?"pr-10":""}
    `.trim().replace(/\s+/g," ");return x.jsxs("div",{className:`${d?"w-full":""} ${g}`,children:[a&&x.jsxs("label",{htmlFor:A,className:`
              block text-sm font-medium mb-2
              ${l?"text-jobblogg-error":"text-jobblogg-text-strong"}
              ${u?"text-jobblogg-text-muted":""}
            `,children:[a,o&&x.jsx("span",{className:"text-jobblogg-error ml-1","aria-label":"required",children:"*"})]}),x.jsxs("div",{className:"relative",children:[v&&x.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none",children:v}),x.jsx("input",{ref:R,id:A,className:$,disabled:u,required:o,"aria-invalid":l?"true":"false","aria-describedby":C,...S}),m&&x.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none",children:m})]}),r&&!l&&x.jsx("p",{id:N,className:"mt-1 text-sm text-jobblogg-text-muted",children:r}),l&&x.jsxs("p",{id:L,className:"mt-1 text-sm text-jobblogg-error flex items-center gap-1",role:"alert","aria-live":"polite",children:[x.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),l]})]})});qx.displayName="TextInput";const Bx=w.forwardRef(({label:a,error:l,helperText:r,required:o=!1,disabled:u=!1,size:f="medium",fullWidth:d=!1,showCharCount:v=!1,showCharacterCount:m,maxLength:g,className:y="",id:O,value:S,"aria-describedby":R,...A},L)=>{const N=O||`textarea-${Math.random().toString(36).substr(2,9)}`,C=l?`${N}-error`:void 0,H=r?`${N}-helper`:void 0,$=v||m,J=$?`${N}-charcount`:void 0,Y=[R,C,H,J].filter(Boolean).join(" ")||void 0,Z=typeof S=="string"?S.length:0,le=g&&Z>g*.8,W=g&&Z>g,ne=`
      textarea-modern
      ${l?"border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error":""}
      ${{small:"min-h-[80px] text-sm p-3",medium:"min-h-[100px] text-base p-4",large:"min-h-[120px] text-lg p-4"}[f]}
      ${d?"w-full":""}
    `.trim().replace(/\s+/g," ");return x.jsxs("div",{className:`${d?"w-full":""} ${y}`,children:[a&&x.jsxs("label",{htmlFor:N,className:`
              block text-sm font-medium mb-2
              ${l?"text-jobblogg-error":"text-jobblogg-text-strong"}
              ${u?"text-jobblogg-text-muted":""}
            `,children:[a,o&&x.jsx("span",{className:"text-jobblogg-error ml-1","aria-label":"required",children:"*"})]}),x.jsx("textarea",{ref:L,id:N,className:ne,disabled:u,required:o,maxLength:g,value:S,"aria-invalid":l?"true":"false","aria-describedby":Y,...A}),x.jsxs("div",{className:"mt-1 flex justify-between items-start gap-2",children:[x.jsxs("div",{className:"flex-1",children:[r&&!l&&x.jsx("p",{id:H,className:"text-sm text-jobblogg-text-muted",children:r}),l&&x.jsxs("p",{id:C,className:"text-sm text-jobblogg-error flex items-center gap-1",role:"alert","aria-live":"polite",children:[x.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),l]})]}),$&&g&&x.jsxs("p",{id:J,className:`
                text-sm flex-shrink-0 tabular-nums
                ${W?"text-jobblogg-error font-medium":le?"text-jobblogg-warning":"text-jobblogg-text-muted"}
              `,"aria-live":"polite","aria-label":`${Z} av ${g} tegn brukt`,children:[Z,"/",g]})]})]})});Bx.displayName="TextArea";const Hx=w.forwardRef(({label:a,helperText:l,error:r,required:o=!1,disabled:u=!1,accept:f=".jpg,.jpeg,.png,.webp",maxSize:d=10*1024*1024,maxFiles:v=1,multiple:m=!1,files:g=[],onFilesChange:y,onFilesRemove:O,validateFile:S,renderPreview:R,className:A="",id:L,...N},C)=>{const[H,$]=w.useState(!1),J=w.useRef(null),Y=L||`file-upload-${Math.random().toString(36).substr(2,9)}`,Z=F=>{if(F.size>d)return`Filen kan ikke være større enn ${(d/1024/1024).toFixed(1)}MB`;if(f){const M=f.split(",").map(_=>_.trim().toLowerCase()),P="."+F.name.split(".").pop()?.toLowerCase(),X=F.type.toLowerCase();if(!M.some(_=>_.startsWith(".")?P===_:X.includes(_.replace("*",""))))return`Kun følgende filtyper er tillatt: ${f}`}return null},le=F=>{const M=Array.from(F),P=[],X=[];if(g.length+M.length>v){X.push(`Du kan maksimalt laste opp ${v} fil${v>1?"er":""}`);return}if(M.forEach(ue=>{const _=S?S(ue):Z(ue);_?X.push(`${ue.name}: ${_}`):P.push(ue)}),X.length>0){alert(X.join(`
`));return}if(P.length>0){const ue=m?[...g,...P]:P;y?.(ue)}},W=F=>{const M=F.target.files;M&&M.length>0&&le(M)},oe=F=>{F.preventDefault(),u||$(!0)},ne=F=>{F.preventDefault(),$(!1)},be=F=>{if(F.preventDefault(),$(!1),u)return;const M=F.dataTransfer.files;M.length>0&&le(M)},ve=F=>{O?.(F);const M=g.filter((P,X)=>X!==F);y?.(M)},Ce=()=>{u||J.current?.click()},De=(F,M,P)=>{const X=F.type.startsWith("image/"),ue=X?URL.createObjectURL(F):null;return x.jsxs("div",{className:"relative rounded-xl overflow-hidden bg-jobblogg-neutral animate-scale-in",children:[X&&ue?x.jsx("img",{src:ue,alt:`Preview ${M+1}`,className:"w-full h-32 object-cover",onLoad:()=>URL.revokeObjectURL(ue)}):x.jsx("div",{className:"w-full h-32 flex items-center justify-center bg-jobblogg-neutral",children:x.jsxs("div",{className:"text-center",children:[x.jsx("svg",{className:"w-8 h-8 mx-auto mb-2 text-jobblogg-text-muted",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),x.jsx(fp,{className:"text-xs",children:F.name})]})}),x.jsx("div",{className:"absolute inset-0 bg-jobblogg-text-strong bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center",children:x.jsx(ji,{onClick:P,variant:"danger",size:"sm",className:"shadow-lg rounded-full w-8 h-8 p-0","aria-label":`Fjern ${F.name}`,children:x.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})}),x.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-jobblogg-text-strong to-transparent p-2",children:[x.jsxs("p",{className:"text-white text-xs font-medium truncate",children:["✅ ",F.name]}),x.jsxs("p",{className:"text-jobblogg-neutral-light text-xs",children:[(F.size/1024/1024).toFixed(1)," MB"]})]})]},M)};return x.jsxs("div",{className:`${A}`,children:[a&&x.jsxs("div",{className:"flex items-center justify-between mb-3",children:[x.jsxs(cp,{className:"font-semibold",children:[a,o&&x.jsx("span",{className:"text-jobblogg-error ml-1","aria-label":"required",children:"*"})]}),!o&&x.jsx(fp,{className:"text-sm",children:"(valgfritt)"})]}),g.length===0&&x.jsxs("div",{className:`
              border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
              ${H?"border-jobblogg-primary bg-jobblogg-primary-soft scale-105":r?"border-jobblogg-error hover:border-jobblogg-error hover:bg-jobblogg-error-soft":"border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral"}
              ${u?"opacity-50 cursor-not-allowed":""}
            `,onDragOver:oe,onDragLeave:ne,onDrop:be,onClick:Ce,role:"button",tabIndex:u?-1:0,"aria-label":`Last opp fil${m?"er":""}`,onKeyDown:F=>{(F.key==="Enter"||F.key===" ")&&(F.preventDefault(),Ce())},children:[x.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center",children:x.jsx("svg",{className:"w-8 h-8 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),x.jsx(zx,{as:"h3",className:"text-lg mb-2",children:H?"📁 Slipp fil"+(m?"ene":"en")+" her!":"📎 Last opp fil"+(m?"er":"")}),x.jsxs(cp,{className:"mb-4",children:["Dra og slipp ",m?"filer":"en fil"," hit, eller klikk for å velge"]}),f&&x.jsxs("div",{className:"flex items-center justify-center gap-2 text-caption",children:[f.split(",").map((F,M)=>x.jsxs(q.Fragment,{children:[M>0&&x.jsx("span",{children:"•"}),x.jsx("span",{children:F.trim().toUpperCase().replace(".","")})]},F.trim())),x.jsx("span",{children:"•"}),x.jsxs("span",{children:["Maks ",(d/1024/1024).toFixed(0),"MB"]})]})]}),g.length>0&&x.jsx("div",{className:`grid gap-4 ${m?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:g.map((F,M)=>R?R(F,M,()=>ve(M)):De(F,M,()=>ve(M)))}),m&&g.length>0&&g.length<v&&x.jsx("div",{className:"mt-4",children:x.jsx(ji,{onClick:Ce,variant:"outline",disabled:u,icon:x.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),children:"Legg til flere filer"})}),x.jsx("input",{ref:J,type:"file",accept:f,multiple:m,onChange:W,className:"hidden",disabled:u,id:Y,"aria-describedby":r?`${Y}-error`:l?`${Y}-helper`:void 0,...N}),l&&!r&&x.jsx("p",{id:`${Y}-helper`,className:"mt-2 text-sm text-jobblogg-text-muted",children:l}),r&&x.jsxs("p",{id:`${Y}-error`,className:"mt-2 text-sm text-jobblogg-error flex items-center gap-1",role:"alert","aria-live":"polite",children:[x.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),r]})]})});Hx.displayName="FileUpload";const Vx=({className:a=""})=>{const{isInstallable:l,isInstalled:r,installApp:o}=Mv(),[u,f]=w.useState(!1),[d,v]=w.useState(!1);if(r||!l||u)return null;const m=async()=>{v(!0);try{await o()&&f(!0)}catch(y){console.error("Install failed:",y)}finally{v(!1)}},g=()=>{f(!0),localStorage.setItem("pwa-install-dismissed","true")};return x.jsxs("div",{className:`
      fixed bottom-4 left-4 right-4 z-50 
      bg-white border border-jobblogg-primary/20 rounded-xl shadow-lg 
      p-4 animate-slide-up
      md:left-auto md:right-4 md:max-w-sm
      ${a}
    `,children:[x.jsxs("div",{className:"flex items-start gap-3",children:[x.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center",children:x.jsx("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),x.jsxs("div",{className:"flex-1 min-w-0",children:[x.jsx("h3",{className:"font-semibold text-jobblogg-text-strong text-sm mb-1",children:"Installer JobbLogg"}),x.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-3 leading-relaxed",children:"Få rask tilgang til prosjektene dine direkte fra hjemskjermen. Fungerer offline og gir deg en bedre opplevelse."}),x.jsxs("div",{className:"flex items-center gap-2",children:[x.jsx(ji,{onClick:m,loading:d,size:"sm",className:"text-xs px-3 py-1.5",children:d?"Installerer...":"Installer"}),x.jsx("button",{onClick:g,className:"text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1.5 rounded-md transition-colors",children:"Ikke nå"})]})]}),x.jsx("button",{onClick:g,className:"flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center","aria-label":"Lukk installasjonsbanner",children:x.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),x.jsx("div",{className:"mt-3 pt-3 border-t border-jobblogg-neutral",children:x.jsxs("p",{className:"text-jobblogg-text-muted text-xs",children:["💡 ",Nx.getInstallInstructions()]})})]})},Qx=()=>{const{isOnline:a,isUpdateAvailable:l,updateApp:r}=Mv(),[o,u]=w.useState(!1),f=async()=>{u(!0);try{await r()}catch(d){console.error("Update failed:",d)}finally{u(!1)}};return x.jsxs("div",{className:"fixed top-4 right-4 z-40 flex flex-col gap-2",children:[x.jsxs("div",{className:`
        px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 transition-all duration-300
        ${a?"bg-jobblogg-success-soft text-jobblogg-success border border-jobblogg-success/20":"bg-jobblogg-warning-soft text-jobblogg-warning border border-jobblogg-warning/20"}
      `,children:[x.jsx("div",{className:`w-2 h-2 rounded-full ${a?"bg-jobblogg-success":"bg-jobblogg-warning"}`}),a?"Online":"Offline"]}),l&&x.jsx("div",{className:"bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg p-3 max-w-xs animate-slide-down",children:x.jsxs("div",{className:"flex items-start gap-2",children:[x.jsx("div",{className:"w-5 h-5 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:x.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})})}),x.jsxs("div",{className:"flex-1 min-w-0",children:[x.jsx("p",{className:"text-jobblogg-text-strong text-sm font-medium mb-1",children:"Oppdatering tilgjengelig"}),x.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-2",children:"En ny versjon av JobbLogg er klar til installasjon."}),x.jsx(ji,{onClick:f,loading:o,size:"sm",className:"text-xs",children:o?"Oppdaterer...":"Oppdater nå"})]})]})})]})},dp=z0,Ma={OFFLINE_DATA:"jobblogg-offline-data",SYNC_QUEUE:"jobblogg-sync-queue",LAST_SYNC:"jobblogg-last-sync"};class Ti{static instance;syncQueue=[];constructor(){this.loadSyncQueue()}static getInstance(){return Ti.instance||(Ti.instance=new Ti),Ti.instance}getOfflineData(){try{const l=localStorage.getItem(Ma.OFFLINE_DATA);return l?JSON.parse(l):{projects:[],projectLogs:[],lastSync:""}}catch(l){return console.error("[OfflineStorage] Error loading offline data:",l),{projects:[],projectLogs:[],lastSync:""}}}saveOfflineData(l){try{localStorage.setItem(Ma.OFFLINE_DATA,JSON.stringify(l))}catch(r){console.error("[OfflineStorage] Error saving offline data:",r)}}addOfflineProject(l){const r=this.getOfflineData(),o={...l,isOffline:!0,syncStatus:"pending"};r.projects.push(o),this.saveOfflineData(r),this.addToSyncQueue("create-project",o)}addOfflineProjectLog(l){const r=this.getOfflineData(),o={...l,isOffline:!0,syncStatus:"pending"};r.projectLogs.push(o),this.saveOfflineData(r),this.addToSyncQueue("create-project-log",o)}getAllProjects(){return this.getOfflineData().projects}getProjectLogs(l){return this.getOfflineData().projectLogs.filter(o=>o.projectId===l)}addToSyncQueue(l,r){const o={type:l,data:r,timestamp:new Date().toISOString()};this.syncQueue.push(o),this.saveSyncQueue()}loadSyncQueue(){try{const l=localStorage.getItem(Ma.SYNC_QUEUE);this.syncQueue=l?JSON.parse(l):[]}catch(l){console.error("[OfflineStorage] Error loading sync queue:",l),this.syncQueue=[]}}saveSyncQueue(){try{localStorage.setItem(Ma.SYNC_QUEUE,JSON.stringify(this.syncQueue))}catch(l){console.error("[OfflineStorage] Error saving sync queue:",l)}}getSyncQueue(){return[...this.syncQueue]}clearSyncQueue(){this.syncQueue=[],this.saveSyncQueue()}removeFromSyncQueue(l){this.syncQueue.splice(l,1),this.saveSyncQueue()}updateSyncStatus(l,r,o){const u=this.getOfflineData();if(r==="project"){const f=u.projects.find(d=>d.id===l);f&&(f.syncStatus=o,o==="synced"&&(f.isOffline=!1))}else{const f=u.projectLogs.find(d=>d.id===l);f&&(f.syncStatus=o,o==="synced"&&(f.isOffline=!1))}this.saveOfflineData(u)}clearOfflineData(){localStorage.removeItem(Ma.OFFLINE_DATA),localStorage.removeItem(Ma.SYNC_QUEUE),localStorage.removeItem(Ma.LAST_SYNC),this.syncQueue=[]}getStorageUsage(){try{"storage"in navigator&&"estimate"in navigator.storage&&navigator.storage.estimate().then(u=>{const f=u.usage||0,d=u.quota||0,v=d>0?f/d*100:0;return console.log("[OfflineStorage] Storage usage:",{used:Math.round(f/1024/1024*100)/100+" MB",available:Math.round(d/1024/1024*100)/100+" MB",percentage:Math.round(v*100)/100+"%"}),{used:f,available:d,percentage:v}});let l=0;for(const u in localStorage)localStorage.hasOwnProperty(u)&&(l+=localStorage[u].length);const r=5*1024*1024,o=l/r*100;return{used:l,available:r,percentage:o}}catch(l){return console.error("[OfflineStorage] Error calculating storage usage:",l),{used:0,available:0,percentage:0}}}isStorageNearlyFull(){return this.getStorageUsage().percentage>80}}const fn=Ti.getInstance(),Px=({className:a=""})=>{const[l,r]=w.useState(!1),[o,u]=w.useState(0),[f,d]=w.useState("idle"),[v,m]=w.useState(0),[g,y]=w.useState(navigator.onLine),O=Rg(dp.projects.create),S=Rg(dp.logEntries.create);w.useEffect(()=>{const A=()=>y(!0),L=()=>y(!1);return window.addEventListener("online",A),window.addEventListener("offline",L),()=>{window.removeEventListener("online",A),window.removeEventListener("offline",L)}},[]),w.useEffect(()=>{const A=()=>{const N=fn.getSyncQueue();m(N.length)};A();const L=setInterval(A,5e3);return()=>clearInterval(L)},[f]),w.useEffect(()=>{if(g&&v>0&&f==="idle"){const A=setTimeout(()=>{R()},2e3);return()=>clearTimeout(A)}},[g,v,f]);const R=async()=>{if(!(!g||l)){r(!0),d("syncing"),u(0);try{const A=fn.getSyncQueue();if(A.length===0){d("success"),r(!1);return}let L=0;const N=A.length;for(let C=0;C<A.length;C++){const H=A[C];try{H.type==="create-project"?(fn.updateSyncStatus(H.data.id,"project","syncing"),await O({name:H.data.title,description:H.data.description,userId:H.data.userId||"offline-user"}),fn.updateSyncStatus(H.data.id,"project","synced")):H.type==="create-project-log"&&(fn.updateSyncStatus(H.data.id,"projectLog","syncing"),await S({projectId:H.data.projectId,userId:H.data.userId||"offline-user",description:H.data.description,imageId:H.data.imageId}),fn.updateSyncStatus(H.data.id,"projectLog","synced")),fn.removeFromSyncQueue(0),L++,u(L/N*100)}catch($){console.error("[OfflineSync] Error syncing item:",$),H.type==="create-project"?fn.updateSyncStatus(H.data.id,"project","error"):H.type==="create-project-log"&&fn.updateSyncStatus(H.data.id,"projectLog","error"),L++,u(L/N*100)}}d("success"),setTimeout(()=>{d("idle")},3e3)}catch(A){console.error("[OfflineSync] Sync failed:",A),d("error"),setTimeout(()=>{d("idle")},5e3)}finally{r(!1),u(0)}}};return v===0&&f==="idle"?null:x.jsxs("div",{className:`
      fixed bottom-20 left-4 right-4 z-40
      bg-white border border-jobblogg-border rounded-xl shadow-lg p-4
      md:left-auto md:right-4 md:max-w-sm
      ${a}
    `,children:[x.jsxs("div",{className:"flex items-start gap-3",children:[x.jsx("div",{className:`
          flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
          ${f==="syncing"?"bg-jobblogg-primary-soft":f==="success"?"bg-jobblogg-success-soft":f==="error"?"bg-jobblogg-error-soft":"bg-jobblogg-warning-soft"}
        `,children:f==="syncing"?x.jsx("div",{className:"w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"}):f==="success"?x.jsx("svg",{className:"w-5 h-5 text-jobblogg-success",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):f==="error"?x.jsx("svg",{className:"w-5 h-5 text-jobblogg-error",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}):x.jsx("svg",{className:"w-5 h-5 text-jobblogg-warning",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),x.jsxs("div",{className:"flex-1 min-w-0",children:[x.jsx("h3",{className:"font-semibold text-jobblogg-text-strong text-sm mb-1",children:f==="syncing"?"Synkroniserer data...":f==="success"?"Synkronisering fullført":f==="error"?"Synkroniseringsfeil":`${v} element${v!==1?"er":""} venter på synkronisering`}),x.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-3",children:f==="syncing"?`${Math.round(o)}% fullført`:f==="success"?"Alle endringer er synkronisert med serveren":f==="error"?"Noen elementer kunne ikke synkroniseres":g?"Trykk for å synkronisere nå":"Venter på internettforbindelse"}),f==="syncing"&&x.jsx("div",{className:"w-full bg-jobblogg-neutral rounded-full h-2 mb-3",children:x.jsx("div",{className:"bg-jobblogg-primary h-2 rounded-full transition-all duration-300",style:{width:`${o}%`}})}),f==="idle"&&g&&v>0&&x.jsx(ji,{onClick:R,size:"sm",className:"text-xs",children:"Synkroniser nå"}),f==="error"&&x.jsxs("div",{className:"flex gap-2",children:[x.jsx(ji,{onClick:R,size:"sm",className:"text-xs",children:"Prøv igjen"}),x.jsx("button",{onClick:()=>d("idle"),className:"text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1 rounded transition-colors",children:"Lukk"})]})]}),f!=="syncing"&&x.jsx("button",{onClick:()=>d("idle"),className:"flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center","aria-label":"Lukk synkroniseringspanel",children:x.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),x.jsx("div",{className:"mt-3 pt-3 border-t border-jobblogg-neutral",children:x.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[x.jsx("div",{className:`w-2 h-2 rounded-full ${g?"bg-jobblogg-success":"bg-jobblogg-error"}`}),x.jsx("span",{className:"text-jobblogg-text-muted",children:g?"Online":"Offline - endringer lagres lokalt"})]})})]})},Yx=()=>{const[a,l]=w.useState({used:0,available:0,percentage:0}),[r,o]=w.useState(!1);return w.useEffect(()=>{const u=()=>{const d=fn.getStorageUsage();l(d)};u();const f=setInterval(u,3e4);return()=>clearInterval(f)},[]),a.percentage<50?null:x.jsx("div",{className:"fixed top-4 left-4 z-40 bg-white border border-jobblogg-warning/20 rounded-lg p-3 max-w-xs shadow-lg",children:x.jsxs("div",{className:"flex items-start gap-2",children:[x.jsx("svg",{className:"w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})}),x.jsxs("div",{className:"flex-1 min-w-0",children:[x.jsxs("p",{className:"text-sm font-medium text-jobblogg-text-strong",children:["Lagringsplass ",a.percentage>80?"nesten full":"fylles opp"]}),x.jsxs("p",{className:"text-xs text-jobblogg-text-medium mb-2",children:[Math.round(a.percentage),"% av tilgjengelig plass brukt"]}),x.jsx("div",{className:"w-full bg-jobblogg-neutral rounded-full h-2 mb-2",children:x.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${a.percentage>90?"bg-jobblogg-error":a.percentage>80?"bg-jobblogg-warning":"bg-jobblogg-primary"}`,style:{width:`${Math.min(a.percentage,100)}%`}})}),x.jsx("button",{onClick:()=>o(!r),className:"text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors",children:r?"Skjul detaljer":"Vis detaljer"}),r&&x.jsxs("div",{className:"mt-2 text-xs text-jobblogg-text-muted",children:[x.jsxs("p",{children:["Brukt: ",Math.round(a.used/1024)," KB"]}),x.jsxs("p",{children:["Tilgjengelig: ",Math.round(a.available/1024)," KB"]})]})]})]})})},$x="modulepreload",Ix=function(a){return"/"+a},hp={},fa=function(l,r,o){let u=Promise.resolve();if(r&&r.length>0){let m=function(g){return Promise.all(g.map(y=>Promise.resolve(y).then(O=>({status:"fulfilled",value:O}),O=>({status:"rejected",reason:O}))))};document.getElementsByTagName("link");const d=document.querySelector("meta[property=csp-nonce]"),v=d?.nonce||d?.getAttribute("nonce");u=m(r.map(g=>{if(g=Ix(g),g in hp)return;hp[g]=!0;const y=g.endsWith(".css"),O=y?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${O}`))return;const S=document.createElement("link");if(S.rel=y?"stylesheet":$x,y||(S.as="script"),S.crossOrigin="",S.href=g,v&&S.setAttribute("nonce",v),document.head.appendChild(S),y)return new Promise((R,A)=>{S.addEventListener("load",R),S.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${g}`)))})}))}function f(d){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=d,window.dispatchEvent(v),!v.defaultPrevented)throw d}return u.then(d=>{for(const v of d||[])v.status==="rejected"&&f(v.reason);return l().catch(f)})};function Ni(a){return w.lazy(a)}function mp(a){a().catch(l=>{console.warn("[LazyLoading] Preload failed:",l)})}class Gx{observer=null;images=new Set;constructor(){"IntersectionObserver"in window&&(this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:"50px 0px",threshold:.01}))}handleIntersection(l){l.forEach(r=>{if(r.isIntersecting){const o=r.target;this.loadImage(o),this.observer?.unobserve(o),this.images.delete(o)}})}loadImage(l){const r=l.dataset.src,o=l.dataset.srcset;r&&(l.src=r),o&&(l.srcset=o),l.classList.remove("lazy"),l.classList.add("lazy-loaded")}observe(l){this.observer?(this.images.add(l),this.observer.observe(l)):this.loadImage(l)}unobserve(l){this.observer&&(this.observer.unobserve(l),this.images.delete(l))}disconnect(){this.observer&&(this.observer.disconnect(),this.images.clear())}}new Gx;class Xx{observer=null;elements=new Map;constructor(){"IntersectionObserver"in window&&(this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:"100px 0px",threshold:.1}))}handleIntersection(l){l.forEach(r=>{if(r.isIntersecting){const o=this.elements.get(r.target);o&&(o(),this.observer?.unobserve(r.target),this.elements.delete(r.target))}})}observe(l,r){this.observer?(this.elements.set(l,r),this.observer.observe(l)):r()}unobserve(l){this.observer&&(this.observer.unobserve(l),this.elements.delete(l))}disconnect(){this.observer&&(this.observer.disconnect(),this.elements.clear())}}new Xx;const Wx={measureRender:(a,l)=>{const r=performance.now();l();const o=performance.now();console.log(`[Performance] ${a} render time: ${o-r}ms`)},measureImageLoad:a=>new Promise(l=>{const r=performance.now(),o=new Image;o.onload=()=>{const u=performance.now()-r;console.log(`[Performance] Image load time (${a}): ${u}ms`),l(u)},o.onerror=()=>{const u=performance.now()-r;console.warn(`[Performance] Image load failed (${a}): ${u}ms`),l(u)},o.src=a}),monitorWebVitals:()=>{new PerformanceObserver(l=>{const r=l.getEntries(),o=r[r.length-1];console.log("[Performance] LCP:",o.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(l=>{l.getEntries().forEach(o=>{console.log("[Performance] FID:",o.processingStart-o.startTime)})}).observe({entryTypes:["first-input"]});let a=0;new PerformanceObserver(l=>{l.getEntries().forEach(o=>{o.hadRecentInput||(a+=o.value,console.log("[Performance] CLS:",a))})}).observe({entryTypes:["layout-shift"]})}},Kx=()=>x.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:x.jsxs("div",{className:"text-center",children:[x.jsx("div",{className:"w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"}),x.jsx("p",{className:"text-jobblogg-text-medium",children:"Laster inn..."})]})}),Zx=Ni(()=>fa(()=>import("./Dashboard-DUwE_EIa.js"),__vite__mapDeps([0,1,2]))),Fx=Ni(()=>fa(()=>import("./CreateProject-C-to07im.js"),__vite__mapDeps([3,1,4,5,6]))),Jx=Ni(()=>fa(()=>import("./ProjectDetail-A1xRxMUl.js"),__vite__mapDeps([7,1,4,5,2]))),ew=Ni(()=>fa(()=>import("./ProjectLog-DbJS2rWz.js"),__vite__mapDeps([8,5,6]))),tw=Ni(()=>fa(()=>import("./SignIn-Bbe2tWET.js"),[])),nw=Ni(()=>fa(()=>import("./SignUp-D6KgDo4M.js"),[])),xi=({children:a,fallback:l=Kx})=>x.jsx(w.Suspense,{fallback:x.jsx(l,{}),children:a}),aw=()=>{mp(()=>fa(()=>import("./Dashboard-DUwE_EIa.js"),__vite__mapDeps([0,1,2]))),mp(()=>fa(()=>import("./CreateProject-C-to07im.js"),__vite__mapDeps([3,1,4,5,6])))};function iw(){return w.useEffect(()=>{aw()},[]),x.jsx(Ax,{children:x.jsxs("div",{className:"min-h-screen bg-white transition-colors duration-300",children:[x.jsx(Qx,{}),x.jsx(Vx,{}),x.jsx(Px,{}),x.jsx(Yx,{}),x.jsxs(tx,{children:[x.jsx(Da,{path:"/sign-in",element:x.jsx(xi,{children:x.jsx(tw,{})})}),x.jsx(Da,{path:"/sign-up",element:x.jsx(xi,{children:x.jsx(nw,{})})}),x.jsx(Da,{path:"/",element:x.jsxs(x.Fragment,{children:[x.jsx(ws,{children:x.jsx(xi,{children:x.jsx(Zx,{})})}),x.jsx(As,{children:x.jsx(Rs,{to:"/sign-in",replace:!0})})]})}),x.jsx(Da,{path:"/create",element:x.jsxs(x.Fragment,{children:[x.jsx(ws,{children:x.jsx(xi,{children:x.jsx(Fx,{})})}),x.jsx(As,{children:x.jsx(Rs,{to:"/sign-in",replace:!0})})]})}),x.jsx(Da,{path:"/project/:projectId/details",element:x.jsxs(x.Fragment,{children:[x.jsx(ws,{children:x.jsx(xi,{children:x.jsx(Jx,{})})}),x.jsx(As,{children:x.jsx(Rs,{to:"/sign-in",replace:!0})})]})}),x.jsx(Da,{path:"/project/:projectId",element:x.jsxs(x.Fragment,{children:[x.jsx(ws,{children:x.jsx(xi,{children:x.jsx(ew,{})})}),x.jsx(As,{children:x.jsx(Rs,{to:"/sign-in",replace:!0})})]})})]})]})})}const je={background:"#ffffff",backgroundSecondary:"#F8FAFC",text:"#1F2937",textSecondary:"#4B5563",textMuted:"#6B7280",border:"#E5E7EB",shimmer:"#F8FAFC",inputBackground:"#ffffff",cardBackground:"#F8FAFC"},lw={variables:{colorPrimary:"#1D4ED8",colorPrimaryFocus:"#1E40AF",colorSuccess:"#10B981",colorWarning:"#FBBF24",colorDanger:"#DC2626",fontFamily:"Inter, system-ui, sans-serif",fontSize:"16px",fontWeight:{normal:"400",medium:"500",semibold:"600",bold:"700"},borderRadius:"0.75rem",spacingUnit:"1rem",colorBackground:je.background,colorInputBackground:je.inputBackground,colorInputText:je.text,colorText:je.text,colorTextSecondary:je.textSecondary,colorTextOnPrimaryBackground:"#ffffff",colorBorder:je.border,colorShimmer:je.shimmer},elements:{card:{backgroundColor:je.cardBackground,borderRadius:"1rem",boxShadow:"0 25px 50px -12px rgb(0 0 0 / 0.25)",border:`1px solid ${je.border}`,padding:"2rem",maxWidth:"28rem",width:"100%"},headerTitle:{fontSize:"1.875rem",fontWeight:"700",color:je.text,textAlign:"center",marginBottom:"0.5rem"},headerSubtitle:{fontSize:"1rem",color:je.textSecondary,textAlign:"center",marginBottom:"2rem"},formButtonPrimary:{backgroundColor:"#1D4ED8",color:"#ffffff",borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"600",border:"none",cursor:"pointer",transition:"all 0.2s ease",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.25)",width:"100%",height:"3rem","&:hover":{backgroundColor:"#1E40AF",transform:"scale(1.02)",boxShadow:"0 8px 25px 0 rgb(29 78 216 / 0.35)"},"&:focus":{outline:"2px solid #1D4ED8",outlineOffset:"2px",transform:"scale(1.02)"},"&:active":{transform:"scale(0.98)"}},formButtonSecondary:{backgroundColor:"transparent",color:je.text,border:`2px solid ${je.border}`,borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",width:"100%",height:"3rem","&:hover":{borderColor:"#1D4ED8",color:"#1D4ED8",transform:"scale(1.02)",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.15)"}},socialButtonsBlockButton:{backgroundColor:je.background,color:je.text,border:`2px solid ${je.border}`,borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",width:"100%",height:"3rem",display:"flex",alignItems:"center",justifyContent:"center",gap:"0.75rem",marginBottom:"0.75rem","&:hover":{borderColor:"#1D4ED8",transform:"scale(1.02)",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.15)"},"&:focus":{outline:"2px solid #1D4ED8",outlineOffset:"2px"}},formFieldInput:{backgroundColor:je.inputBackground,color:je.text,border:`2px solid ${je.border}`,borderRadius:"0.75rem",padding:"0.75rem 1rem",fontSize:"1rem",width:"100%",height:"3rem",transition:"all 0.2s ease","&:focus":{borderColor:"#1D4ED8",outline:"none",boxShadow:"0 0 0 3px rgb(29 78 216 / 0.1)",backgroundColor:je.inputBackground},"&::placeholder":{color:je.textMuted}},formFieldLabel:{fontSize:"0.875rem",fontWeight:"600",color:je.text,marginBottom:"0.5rem",display:"block"},formFieldAction:{color:"#1D4ED8",fontSize:"0.875rem",fontWeight:"500",textDecoration:"none",transition:"color 0.2s ease","&:hover":{color:"#1E40AF",textDecoration:"underline"}},footerActionLink:{color:"#1D4ED8",fontSize:"0.875rem",fontWeight:"500",textDecoration:"none",transition:"color 0.2s ease","&:hover":{color:"#1E40AF",textDecoration:"underline"}},dividerLine:{backgroundColor:je.border,height:"1px",margin:"1.5rem 0"},dividerText:{color:je.textSecondary,fontSize:"0.875rem",fontWeight:"500"},spinner:{color:"#1D4ED8",width:"1.5rem",height:"1.5rem"},formFieldErrorText:{color:"#DC2626",fontSize:"0.875rem",fontWeight:"500",marginTop:"0.5rem"},formFieldSuccessText:{color:"#10B981",fontSize:"0.875rem",fontWeight:"500",marginTop:"0.5rem"},alert:{backgroundColor:je.backgroundSecondary,border:`1px solid ${je.border}`,borderRadius:"0.75rem",padding:"1rem",marginBottom:"1rem"},alertText:{color:je.text,fontSize:"0.875rem",lineHeight:"1.5"}}},rw={locale:"nb-NO",signIn:{start:{title:"Velkommen tilbake! 👋",subtitle:"Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen",actionText:"Har du ikke konto ennå?",actionLink:"Opprett konto"},emailCode:{title:"Sjekk e-posten din",subtitle:"Vi har sendt en kode til {{identifier}}",formTitle:"Bekreftelseskode",formSubtitle:"Skriv inn koden du mottok på e-post",resendButton:"Send kode på nytt"},emailLink:{title:"Sjekk e-posten din",subtitle:"Vi har sendt en innloggingslenke til {{identifier}}",formTitle:"Innloggingslenke",formSubtitle:"Klikk på lenken i e-posten for å logge inn",resendButton:"Send lenke på nytt"},forgotPasswordAlternativeMethods:{title:"Glemt passordet?",label:"Ingen problem! Vi hjelper deg å komme inn igjen.",blockButton__emailCode:"Send kode til e-post",blockButton__emailLink:"Send innloggingslenke til e-post"},alternativeMethods:{title:"Andre innloggingsmåter",actionLink:"Få hjelp",blockButton__emailCode:"Bruk e-postkode",blockButton__emailLink:"Bruk e-postlenke",blockButton__password:"Bruk passord"}},signUp:{start:{title:"Opprett din JobbLogg-konto 🚀",subtitle:"Begynn å dokumentere arbeidet ditt profesjonelt",actionText:"Har du allerede en konto?",actionLink:"Logg inn"},emailCode:{title:"Bekreft e-postadressen din",subtitle:"Vi har sendt en bekreftelseskode til {{identifier}}",formTitle:"Bekreftelseskode",formSubtitle:"Skriv inn koden du mottok på e-post",resendButton:"Send kode på nytt"},emailLink:{title:"Bekreft e-postadressen din",subtitle:"Vi har sendt en bekreftelseslenke til {{identifier}}",formTitle:"Bekreftelseslenke",formSubtitle:"Klikk på lenken i e-posten for å bekrefte kontoen",resendButton:"Send lenke på nytt"},continue:{title:"Fullfør registreringen",subtitle:"Fyll ut de siste detaljene for å komme i gang",actionText:"Har du allerede en konto?",actionLink:"Logg inn"}},formFieldLabel__emailAddress:"E-postadresse",formFieldLabel__emailAddress_username:"E-postadresse eller brukernavn",formFieldLabel__username:"Brukernavn",formFieldLabel__firstName:"Fornavn",formFieldLabel__lastName:"Etternavn",formFieldLabel__password:"Passord",formFieldLabel__newPassword:"Nytt passord",formFieldLabel__confirmPassword:"Bekreft passord",formFieldLabel__currentPassword:"Nåværende passord",formFieldLabel__signOutOfOtherSessions:"Logg ut av andre enheter",formFieldInputPlaceholder__emailAddress:"Skriv inn e-postadressen din",formFieldInputPlaceholder__emailAddress_username:"E-postadresse eller brukernavn",formFieldInputPlaceholder__username:"Skriv inn brukernavn",formFieldInputPlaceholder__firstName:"Skriv inn fornavn",formFieldInputPlaceholder__lastName:"Skriv inn etternavn",formFieldInputPlaceholder__password:"Skriv inn passord",formFieldInputPlaceholder__newPassword:"Skriv inn nytt passord",formFieldInputPlaceholder__confirmPassword:"Bekreft passordet",formFieldInputPlaceholder__currentPassword:"Skriv inn nåværende passord",formButtonPrimary:"Fortsett",formButtonPrimary__signIn:"Logg inn",formButtonPrimary__signUp:"Opprett konto",formButtonPrimary__continue:"Fortsett",formButtonPrimary__finish:"Fullfør",socialButtonsBlockButton:"Fortsett med {{provider|titleize}}",dividerText:"eller",footerActionLink__useAnotherMethod:"Bruk en annen metode",footerActionLink__signUp:"Opprett konto",footerActionLink__signIn:"Logg inn",formFieldError__notProvided:"Dette feltet er påkrevd",formFieldError__emailAddress_invalid:"Ugyldig e-postadresse",formFieldError__password_pwned:"Dette passordet er kompromittert. Velg et annet passord.",formFieldError__password_too_short:"Passordet må være minst {{length}} tegn",formFieldError__password_weak:"Passordet er for svakt. Bruk en kombinasjon av bokstaver, tall og symboler.",formFieldError__firstName_invalid:"Ugyldig fornavn",formFieldError__lastName_invalid:"Ugyldig etternavn",formFieldError__username_invalid:"Ugyldig brukernavn",formFieldError__username_taken:"Dette brukernavnet er allerede tatt",formFieldError__emailAddress_taken:"Denne e-postadressen er allerede registrert",formFieldSuccess__signUp:"Kontoen din er opprettet!",formFieldSuccess__signIn:"Du er nå logget inn!",formFieldAction__loading:"Laster...",formFieldHintText__optional:"(valgfritt)",formFieldHintText__slug:"Kun bokstaver, tall og bindestreker",verificationLinkText:"Bekreftelseslenke",verificationCodeText:"Bekreftelseskode",resendButton:"Send på nytt",backButton:"Tilbake",modalCloseButton:"Lukk",breadcrumbsItem1:"Logg inn",breadcrumbsItem2:"Velg konto",breadcrumbsItem3:"Bekreft",alertText:"Hvis du fortsetter, godtar du våre vilkår og betingelser.",badge__primary:"Primær",badge__thisDevice:"Denne enheten",badge__userDevice:"Brukerenhet",badge__otherImpersonatorDevice:"Annen enhet",dates:{previous6Days:"Siste {{count}} dager",lastDay:"I går",sameDay:"I dag",nextDay:"I morgen",next6Days:"Neste {{count}} dager",numeric:"{{date}}"}},sw=new ES("https://enchanted-quail-174.convex.cloud"),ow="pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk";"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(a=>{console.log("[PWA] Service Worker registered successfully:",a.scope)}).catch(a=>{console.log("[PWA] Service Worker registration failed:",a)})});Wx.monitorWebVitals();i0.createRoot(document.getElementById("root")).render(x.jsx(w.StrictMode,{children:x.jsx(gv,{publishableKey:ow,appearance:lw,localization:rw,children:x.jsx(wS,{client:sw,children:x.jsx(iw,{})})})}));export{kv as L,ji as P,mw as S,fp as T,pw as U,xv as a,uw as b,dp as c,Rg as d,qx as e,Bx as f,vw as g,zx as h,cp as i,x as j,gw as k,w as r,hw as u};
