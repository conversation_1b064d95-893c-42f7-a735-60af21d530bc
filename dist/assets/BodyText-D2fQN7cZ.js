import{j as r}from"./index-BgxeSoF9.js";const l=({children:t,className:e="",as:s="p",size:o="body"})=>{const a=()=>{switch(o){case"small":return"text-small text-jobblogg-text-medium";case"caption":return"text-caption text-jobblogg-text-muted";default:return"text-body text-jobblogg-text-medium"}};return r.jsx(s,{className:`
        ${a()} leading-relaxed
        ${e}
      `.trim().replace(/\s+/g," "),children:t})};export{l as B};
