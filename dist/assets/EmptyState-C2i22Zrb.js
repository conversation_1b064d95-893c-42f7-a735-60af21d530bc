import{j as e}from"./index-CKtwqk6x.js";const d=({title:s,description:o,actionLabel:t,onAction:r,icon:n,className:a=""})=>{const i=e.jsx("svg",{className:"w-16 h-16 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});return e.jsxs("div",{className:`
        bg-jobblogg-blue-50 rounded-xl p-8 text-center animate-fade-in
        ${a}
      `.trim().replace(/\s+/g," "),children:[e.jsx("div",{className:"flex justify-center mb-6",children:n||i}),e.jsx("h3",{className:"text-xl font-semibold text-jobblogg-text-strong mb-3",children:s}),e.jsx("p",{className:"text-jobblogg-text-medium mb-6 max-w-md mx-auto leading-relaxed",children:o}),t&&r&&e.jsxs("button",{onClick:r,className:"btn-primary-solid inline-flex items-center gap-2",children:[e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),t]})]})};export{d as E};
