@import"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap";*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:Inter,system-ui,sans-serif;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}:where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light]{color-scheme:light;--color-base-100: oklch(100% 0 0);--color-base-200: oklch(98% 0 0);--color-base-300: oklch(95% 0 0);--color-base-content: oklch(21% .006 285.885);--color-primary: oklch(45% .24 277.023);--color-primary-content: oklch(93% .034 272.788);--color-secondary: oklch(65% .241 354.308);--color-secondary-content: oklch(94% .028 342.258);--color-accent: oklch(77% .152 181.912);--color-accent-content: oklch(38% .063 188.416);--color-neutral: oklch(14% .005 285.823);--color-neutral-content: oklch(92% .004 286.32);--color-info: oklch(74% .16 232.661);--color-info-content: oklch(29% .066 243.157);--color-success: oklch(76% .177 163.223);--color-success-content: oklch(37% .077 168.94);--color-warning: oklch(82% .189 84.429);--color-warning-content: oklch(41% .112 45.904);--color-error: oklch(71% .194 13.428);--color-error-content: oklch(27% .105 12.094);--radius-selector: .5rem;--radius-field: .25rem;--radius-box: .5rem;--size-selector: .25rem;--size-field: .25rem;--border: 1px;--depth: 1;--noise: 0}@media (prefers-color-scheme: dark){:root{color-scheme:dark;--color-base-100: oklch(25.33% .016 252.42);--color-base-200: oklch(23.26% .014 253.1);--color-base-300: oklch(21.15% .012 254.09);--color-base-content: oklch(97.807% .029 256.847);--color-primary: oklch(58% .233 277.117);--color-primary-content: oklch(96% .018 272.314);--color-secondary: oklch(65% .241 354.308);--color-secondary-content: oklch(94% .028 342.258);--color-accent: oklch(77% .152 181.912);--color-accent-content: oklch(38% .063 188.416);--color-neutral: oklch(14% .005 285.823);--color-neutral-content: oklch(92% .004 286.32);--color-info: oklch(74% .16 232.661);--color-info-content: oklch(29% .066 243.157);--color-success: oklch(76% .177 163.223);--color-success-content: oklch(37% .077 168.94);--color-warning: oklch(82% .189 84.429);--color-warning-content: oklch(41% .112 45.904);--color-error: oklch(71% .194 13.428);--color-error-content: oklch(27% .105 12.094);--radius-selector: .5rem;--radius-field: .25rem;--radius-box: .5rem;--size-selector: .25rem;--size-field: .25rem;--border: 1px;--depth: 1;--noise: 0}}:root:has(input.theme-controller[value=light]:checked),[data-theme=light]{color-scheme:light;--color-base-100: oklch(100% 0 0);--color-base-200: oklch(98% 0 0);--color-base-300: oklch(95% 0 0);--color-base-content: oklch(21% .006 285.885);--color-primary: oklch(45% .24 277.023);--color-primary-content: oklch(93% .034 272.788);--color-secondary: oklch(65% .241 354.308);--color-secondary-content: oklch(94% .028 342.258);--color-accent: oklch(77% .152 181.912);--color-accent-content: oklch(38% .063 188.416);--color-neutral: oklch(14% .005 285.823);--color-neutral-content: oklch(92% .004 286.32);--color-info: oklch(74% .16 232.661);--color-info-content: oklch(29% .066 243.157);--color-success: oklch(76% .177 163.223);--color-success-content: oklch(37% .077 168.94);--color-warning: oklch(82% .189 84.429);--color-warning-content: oklch(41% .112 45.904);--color-error: oklch(71% .194 13.428);--color-error-content: oklch(27% .105 12.094);--radius-selector: .5rem;--radius-field: .25rem;--radius-box: .5rem;--size-selector: .25rem;--size-field: .25rem;--border: 1px;--depth: 1;--noise: 0}:root:has(input.theme-controller[value=dark]:checked),[data-theme=dark]{color-scheme:dark;--color-base-100: oklch(25.33% .016 252.42);--color-base-200: oklch(23.26% .014 253.1);--color-base-300: oklch(21.15% .012 254.09);--color-base-content: oklch(97.807% .029 256.847);--color-primary: oklch(58% .233 277.117);--color-primary-content: oklch(96% .018 272.314);--color-secondary: oklch(65% .241 354.308);--color-secondary-content: oklch(94% .028 342.258);--color-accent: oklch(77% .152 181.912);--color-accent-content: oklch(38% .063 188.416);--color-neutral: oklch(14% .005 285.823);--color-neutral-content: oklch(92% .004 286.32);--color-info: oklch(74% .16 232.661);--color-info-content: oklch(29% .066 243.157);--color-success: oklch(76% .177 163.223);--color-success-content: oklch(37% .077 168.94);--color-warning: oklch(82% .189 84.429);--color-warning-content: oklch(41% .112 45.904);--color-error: oklch(71% .194 13.428);--color-error-content: oklch(27% .105 12.094);--radius-selector: .5rem;--radius-field: .25rem;--radius-box: .5rem;--size-selector: .25rem;--size-field: .25rem;--border: 1px;--depth: 1;--noise: 0}@property --radialprogress{syntax: "<percentage>"; inherits: true; initial-value: 0%;}:root{scrollbar-color:color-mix(in oklch,currentColor 35%,#0000) #0000;--fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E")}:root,[data-theme]{background-color:var(--root-bg, var(--color-base-100));color:var(--color-base-content)}html{font-family:Inter,system-ui,sans-serif}body{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}@keyframes radio{0%{padding:5px}50%{padding:3px}}.skeleton{border-radius:var(--radius-box);background-color:var(--color-base-300)}@media (prefers-reduced-motion: reduce){.skeleton{transition-duration:15s}}.skeleton{will-change:background-position;animation:skeleton 1.8s ease-in-out infinite;background-image:linear-gradient(105deg,#0000 0% 40%,var(--color-base-100) 50%,#0000 60% 100%);background-size:200% auto;background-repeat:no-repeat;background-position-x:-50%}@keyframes skeleton{0%{background-position:150%}to{background-position:-50%}}@keyframes progress{50%{background-position-x:-115%}}.menu{display:flex;width:-moz-fit-content;width:fit-content;flex-direction:column;flex-wrap:wrap;padding:.5rem;--menu-active-fg: var(--color-neutral-content);--menu-active-bg: var(--color-neutral);font-size:.875rem}.menu :where(li ul){position:relative;margin-inline-start:1rem;padding-inline-start:.5rem;white-space:nowrap}.menu :where(li ul):before{position:absolute;inset-inline-start:0rem;top:.75rem;bottom:.75rem;background-color:var(--color-base-content);opacity:10%;width:var(--border);content:""}.menu :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}.menu :where(li:not(.menu-title)>*:not(ul,details,.menu-title,.btn)),.menu :where(li:not(.menu-title)>details>summary:not(.menu-title)){display:grid;grid-auto-flow:column;align-content:flex-start;align-items:center;gap:.5rem;border-radius:var(--radius-field);padding-inline:.75rem;padding-block:.375rem;text-align:start;transition-property:color,background-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);grid-auto-columns:minmax(auto,max-content) auto max-content;text-wrap:balance;-webkit-user-select:none;-moz-user-select:none;user-select:none}.menu :where(li>details>summary){--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.menu :where(li>details>summary){outline:2px solid transparent;outline-offset:2px}}.menu :where(li>details>summary)::-webkit-details-marker{display:none}.menu :where(li>details>summary):after,.menu :where(li>.menu-dropdown-toggle):after{justify-self:flex-end;display:block;height:.375rem;width:.375rem;rotate:-135deg;translate:0 -1px;transition-property:rotate,translate;transition-duration:.2s;content:"";transform-origin:50% 50%;box-shadow:2px 2px inset;pointer-events:none}.menu :where(li>details[open]>summary):after,.menu :where(li>.menu-dropdown-toggle.menu-dropdown-show):after{rotate:45deg;translate:0 1px}.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible{cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content) 10%,transparent);color:var(--color-base-content);--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible{outline:2px solid transparent;outline-offset:2px}}.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){cursor:pointer;background-color:color-mix(in oklab,var(--color-base-content) 10%,transparent);--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){outline:2px solid transparent;outline-offset:2px}}.menu :where(li:not(.menu-title,.disabled)>*:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){box-shadow:0 1px #00000003 inset,0 -1px #ffffff03 inset}.menu :where(li:empty){background-color:var(--color-base-content);opacity:10%;margin:.5rem 1rem;height:1px}.menu :where(li){position:relative;display:flex;flex-shrink:0;flex-direction:column;flex-wrap:wrap;align-items:stretch}.menu :where(li) .badge{justify-self:flex-end}.menu :where(li)>*:not(ul,.menu-title,details,.btn):active,.menu :where(li)>*:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.menu :where(li)>*:not(ul,.menu-title,details,.btn):active,.menu :where(li)>*:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active{outline:2px solid transparent;outline-offset:2px}}.menu :where(li)>*:not(ul,.menu-title,details,.btn):active,.menu :where(li)>*:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>details>summary:active{color:var(--menu-active-fg);background-color:var(--menu-active-bg);background-size:auto,calc(var(--noise) * 100%);background-image:none,var(--fx-noise)}.menu :where(li)>*:not(ul,.menu-title,details,.btn):active:not(.menu :where(li)>*:not(ul,.menu-title,details,.btn):active:active),.menu :where(li)>*:not(ul,.menu-title,details,.btn).menu-active:not(.menu :where(li)>*:not(ul,.menu-title,details,.btn).menu-active:active),.menu :where(li)>details>summary:active:not(.menu :where(li)>details>summary:active:active){box-shadow:0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg)}.menu :where(li).menu-disabled{pointer-events:none;color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.menu .dropdown:focus-within .menu-dropdown-toggle:after{rotate:45deg;translate:0 1px}.menu .dropdown-content{margin-top:.5rem;padding:.5rem}.menu .dropdown-content:before{display:none}:where(.btn){width:unset}.btn{display:inline-flex;flex-shrink:0;cursor:pointer;flex-wrap:nowrap;align-items:center;justify-content:center;gap:.375rem;text-align:center;vertical-align:middle;outline-offset:2px;webkit-user-select:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;padding-inline:var(--btn-p);color:var(--btn-fg);--tw-prose-links: var(--btn-fg);height:var(--size);font-size:var(--fontsize, .875rem);font-weight:600;outline-color:var(--btn-color, var(--color-base-content));transition-property:color,background-color,border-color,box-shadow;transition-timing-function:cubic-bezier(0,0,.2,1);transition-duration:.2s;border-start-start-radius:var(--join-ss, var(--radius-field));border-start-end-radius:var(--join-se, var(--radius-field));border-end-start-radius:var(--join-es, var(--radius-field));border-end-end-radius:var(--join-ee, var(--radius-field));background-color:var(--btn-bg);background-size:auto,calc(var(--noise) * 100%);background-image:none,var(--btn-noise);border-width:var(--border);border-style:solid;border-color:var(--btn-border);text-shadow:0 .5px oklch(100% 0 0 / calc(var(--depth) * .15));touch-action:manipulation;box-shadow:0 .5px 0 .5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset,var(--btn-shadow);--size: calc(var(--size-field, .25rem) * 10);--btn-bg: var(--btn-color, var(--color-base-200));--btn-fg: var(--color-base-content);--btn-p: 1rem;--btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));--btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000), 0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);--btn-noise: var(--fx-noise)}.prose .btn{text-decoration-line:none}@media (hover: hover){.btn:hover{--btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%)}}.btn:focus-visible{outline-width:2px;outline-style:solid;isolation:isolate}.btn:active:not(.btn-active){translate:0 .5px;--btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);--btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);--btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0)}.btn:is(:disabled,[disabled],.btn-disabled):not(.btn-link,.btn-ghost){background-color:color-mix(in oklab,var(--color-base-content) 10%,transparent);box-shadow:none}.btn:is(:disabled,[disabled],.btn-disabled){pointer-events:none;--btn-border: #0000;--btn-noise: none;--btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000)}@media (hover: hover){.btn:is(:disabled,[disabled],.btn-disabled):hover{pointer-events:none;background-color:color-mix(in oklab,var(--color-neutral) 20%,transparent);--btn-border: #0000;--btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000)}}.btn:is(input[type=checkbox],input[type=radio]){-webkit-appearance:none;-moz-appearance:none;appearance:none}.btn:is(input[type=checkbox],input[type=radio]):after{content:attr(aria-label)}.btn:where(input:checked:not(.filter .btn)){--btn-color: var(--color-primary);--btn-fg: var(--color-primary-content);isolation:isolate}.btn-primary{--btn-color: var(--color-primary);--btn-fg: var(--color-primary-content)}.btn-ghost:not(.btn-active,:hover,:active:focus,:focus-visible){--btn-shadow: "";--btn-bg: #0000;--btn-border: #0000;--btn-noise: none}.btn-ghost:not(.btn-active,:hover,:active:focus,:focus-visible):not(:disabled,[disabled],.btn-disabled){outline-color:currentColor;--btn-fg: currentColor}@media (hover: none){.btn-ghost:hover:not(.btn-active,:active,:focus-visible,:disabled,[disabled],.btn-disabled){--btn-shadow: "";--btn-bg: #0000;--btn-border: #0000;--btn-noise: none;--btn-fg: currentColor}}.btn-outline:not(.btn-active,:hover,:active:focus,:focus-visible,:disabled,[disabled],.btn-disabled,:checked){--btn-shadow: "";--btn-bg: #0000;--btn-fg: var(--btn-color);--btn-border: var(--btn-color);--btn-noise: none}@media (hover: none){.btn-outline:hover:not(.btn-active,:active,:focus-visible,:disabled,[disabled],.btn-disabled,:checked){--btn-shadow: "";--btn-bg: #0000;--btn-fg: var(--btn-color);--btn-border: var(--btn-color);--btn-noise: none}}.btn-lg{--fontsize: 1.125rem;--btn-p: 1.25rem;--size: calc(var(--size-field, .25rem) * 12)}.btn-circle{border-radius:calc(infinity * 1px);padding-inline:0rem;width:var(--size);height:var(--size)}.textarea{border:var(--border) solid #0000;min-height:5rem;flex-shrink:1;-webkit-appearance:none;-moz-appearance:none;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);padding-block:.5rem;vertical-align:middle;width:clamp(3rem,20rem,100%);padding-inline-start:.75rem;padding-inline-end:.75rem;font-size:.875rem;touch-action:manipulation;border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth) * 10%),#0000) inset,0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;--input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000)}.textarea textarea{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:transparent;border:none}.textarea textarea:focus,.textarea textarea:focus-within{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.textarea textarea:focus,.textarea textarea:focus-within{outline:2px solid transparent;outline-offset:2px}}.textarea:focus,.textarea:focus-within{--input-color: var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth) * 10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content) 40%,transparent)}.textarea:has(>textarea[disabled])::-moz-placeholder,.textarea:is(:disabled,[disabled])::-moz-placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.textarea:has(>textarea[disabled])::placeholder,.textarea:is(:disabled,[disabled])::placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.textarea:has(>textarea[disabled]),.textarea:is(:disabled,[disabled]){box-shadow:none}.textarea:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}.label{display:inline-flex;align-items:center;gap:.375rem;white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent)}.label:has(input){cursor:pointer}.label:is(.input>*,.select>*){display:flex;height:calc(100% - .5rem);align-items:center;padding-inline:.75rem;white-space:nowrap;font-size:inherit}.label:is(.input>*,.select>*):first-child{margin-inline-start:-.75rem;margin-inline-end:.75rem;border-inline-end:var(--border) solid color-mix(in oklab,currentColor 10%,#0000)}.label:is(.input>*,.select>*):last-child{margin-inline-start:.75rem;margin-inline-end:-.75rem;border-inline-start:var(--border) solid color-mix(in oklab,currentColor 10%,#0000)}.card{position:relative;display:flex;flex-direction:column;border-radius:var(--radius-box);outline-width:2px;transition:outline .2s ease-in-out;outline:0 solid #0000;outline-offset:2px}.card:focus{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.card:focus{outline:2px solid transparent;outline-offset:2px}}.card:focus-visible{outline-color:currentColor}.card :where(figure:first-child){overflow:hidden;border-start-start-radius:inherit;border-start-end-radius:inherit;border-end-start-radius:unset;border-end-end-radius:unset}.card :where(figure:last-child){overflow:hidden;border-start-start-radius:unset;border-start-end-radius:unset;border-end-start-radius:inherit;border-end-end-radius:inherit}.card:where(.card-border){border:var(--border) solid var(--color-base-200)}.card:where(.card-dash){border:var(--border) dashed var(--color-base-200)}.card.image-full{display:grid}.card.image-full>*{grid-column-start:1;grid-row-start:1}.card.image-full>.card-body{position:relative;color:var(--color-neutral-content)}.card.image-full :where(figure){overflow:hidden;border-radius:inherit}.card.image-full>figure img{height:100%;-o-object-fit:cover;object-fit:cover;filter:brightness(28%)}.card figure{display:flex;align-items:center;justify-content:center}.card:has(>input:is(input[type=checkbox],input[type=radio])){cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.card:has(>:checked){outline:2px solid currentColor}.card-title{display:flex;align-items:center;gap:.5rem;font-size:var(--cardtitle-fs, 1.125rem);font-weight:600}.card-body{display:flex;flex:auto;flex-direction:column;gap:.5rem;padding:var(--card-p, 1.5rem);font-size:var(--card-fs, .875rem)}.card-body :where(p){flex-grow:1}.card-actions{display:flex;flex-wrap:wrap;align-items:flex-start;gap:.5rem}.card-xs .card-body{--card-p: .5rem;--card-fs: .6875rem}.card-xs .card-title{--cardtitle-fs: .875rem}.card-sm .card-body{--card-p: 1rem;--card-fs: .75rem}.card-sm .card-title{--cardtitle-fs: 1rem}.card-md .card-body{--card-p: 1.5rem;--card-fs: .875rem}.card-md .card-title{--cardtitle-fs: 1.125rem}.card-lg .card-body{--card-p: 2rem;--card-fs: 1rem}.card-lg .card-title{--cardtitle-fs: 1.25rem}.card-xl .card-body{--card-p: 2.5rem;--card-fs: 1.125rem}.card-xl .card-title{--cardtitle-fs: 1.375rem}@keyframes toast{0%{scale:.9;opacity:0}to{scale:1;opacity:1}}.filter{display:flex;flex-wrap:wrap}.filter input[type=radio]{width:auto}.filter input{overflow:hidden;opacity:100%;scale:1;transition:margin .1s,opacity .3s,padding .3s,border-width .1s}.filter input:not(:last-child){margin-inline-end:.25rem}.filter input.filter-reset{aspect-ratio:1 / 1}.filter input.filter-reset:after{content:"×"}.filter:not(:has(input:checked:not(.filter-reset))) .filter-reset,.filter:not(:has(input:checked:not(.filter-reset))) input[type=reset]{scale:0;border-width:0;margin-inline:0rem;width:0rem;padding-inline:0rem;opacity:0%}.filter:has(input:checked:not(.filter-reset)) input:not(:checked,.filter-reset,input[type=reset]){scale:0;border-width:0;margin-inline:0rem;width:0rem;padding-inline:0rem;opacity:0%}.status{display:inline-block;aspect-ratio:1 / 1;width:.5rem;height:.5rem;border-radius:var(--radius-selector);background-color:color-mix(in oklab,var(--color-base-content) 20%,transparent);background-position:center;background-repeat:no-repeat;vertical-align:middle;color:color-mix(in srgb,#000 30%,transparent)}@supports (color: color-mix(in lab,red,red)){.status{color:color-mix(in oklab,var(--color-black) 30%,transparent)}}.status{background-image:radial-gradient(circle at 35% 30%,oklch(1 0 0 / calc(var(--depth) * .5)),#0000);box-shadow:0 2px 3px -1px color-mix(in oklab,currentColor calc(var(--depth) * 100%),#0000)}.list{display:flex;flex-direction:column;font-size:.875rem}.list :where(.list-row){--list-grid-cols: minmax(0, auto) 1fr;position:relative;display:grid;grid-auto-flow:column;gap:1rem;border-radius:var(--radius-box);padding:1rem;word-break:break-word;grid-template-columns:var(--list-grid-cols)}.list :where(.list-row):has(.list-col-grow:nth-child(1)){--list-grid-cols: 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(2)){--list-grid-cols: minmax(0, auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(3)){--list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(4)){--list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(5)){--list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(6)){--list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr}.list :where(.list-row) :not(.list-col-wrap){grid-row-start:1}.list>:not(:last-child).list-row:after,.list>:not(:last-child) .list-row:after{content:"";border-bottom:var(--border) solid;inset-inline:var(--radius-box);position:absolute;bottom:0rem;border-color:color-mix(in oklab,var(--color-base-content) 5%,transparent)}.mockup-browser .mockup-browser-toolbar .input{margin-inline:auto;display:flex;height:100%;align-items:center;gap:.5rem;overflow:hidden;background-color:var(--color-base-200);text-overflow:ellipsis;white-space:nowrap;font-size:.75rem;direction:ltr}.mockup-browser .mockup-browser-toolbar .input:before{content:"";width:1rem;height:1rem;opacity:30%;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A")}@keyframes dropdown{0%{opacity:0}}.input{cursor:text;border:var(--border) solid #0000;position:relative;display:inline-flex;flex-shrink:1;-webkit-appearance:none;-moz-appearance:none;appearance:none;align-items:center;gap:.5rem;background-color:var(--color-base-100);padding-inline:.75rem;vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);height:var(--size);font-size:.875rem;touch-action:manipulation;border-start-start-radius:var(--join-ss, var(--radius-field));border-start-end-radius:var(--join-se, var(--radius-field));border-end-start-radius:var(--join-es, var(--radius-field));border-end-end-radius:var(--join-ee, var(--radius-field));border-color:var(--input-color);box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth) * 10%),#0000) inset,0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;--size: calc(var(--size-field, .25rem) * 10);--input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000)}.input:where(input){display:inline-flex}.input :where(input){display:inline-flex;height:100%;width:100%;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:transparent;border:none}.input :where(input):focus,.input :where(input):focus-within{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.input :where(input):focus,.input :where(input):focus-within{outline:2px solid transparent;outline-offset:2px}}.input :where(input[type=url]),.input :where(input[type=email]){direction:ltr}.input :where(input[type=date]){display:inline-block}.input:focus,.input:focus-within{--input-color: var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth) * 10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}.input:has(>input[disabled]),.input:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content) 40%,transparent)}.input:has(>input[disabled])::-moz-placeholder,.input:is(:disabled,[disabled])::-moz-placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.input:has(>input[disabled])::placeholder,.input:is(:disabled,[disabled])::placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.input:has(>input[disabled]),.input:is(:disabled,[disabled]){box-shadow:none}.input:has(>input[disabled])>input[disabled]{cursor:not-allowed}.input::-webkit-date-and-time-value{text-align:inherit}.input[type=number]::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}.input::-webkit-calendar-picker-indicator{position:absolute;inset-inline-end:.75em}.indicator{position:relative;display:inline-flex;width:-moz-max-content;width:max-content}.indicator :where(.indicator-item){z-index:1;position:absolute;white-space:nowrap;top:var(--indicator-t, 0);bottom:var(--indicator-b, auto);left:var(--indicator-s, auto);right:var(--indicator-e, 0);translate:var(--indicator-x, 50%) var(--indicator-y, -50%)}@keyframes rating{0%,40%{scale:1.1;filter:brightness(1.05) contrast(1.05)}}.\!loading{pointer-events:none!important;display:inline-block!important;aspect-ratio:1 / 1!important;background-color:currentColor!important;vertical-align:middle!important;width:calc(var(--size-selector, .25rem) * 6)!important;-webkit-mask-size:100%!important;mask-size:100%!important;-webkit-mask-repeat:no-repeat!important;mask-repeat:no-repeat!important;-webkit-mask-position:center!important;mask-position:center!important;-webkit-mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E")!important;mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E")!important}.loading{pointer-events:none;display:inline-block;aspect-ratio:1 / 1;background-color:currentColor;vertical-align:middle;width:calc(var(--size-selector, .25rem) * 6);-webkit-mask-size:100%;mask-size:100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-position:center;mask-position:center;-webkit-mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E")}.link{cursor:pointer;text-decoration-line:underline}.link:focus{--tw-outline-style: none;outline-style:none}@media (forced-colors: active){.link:focus{outline:2px solid transparent;outline-offset:2px}}.link:focus-visible{outline:2px solid currentColor;outline-offset:2px}.alert{display:grid;align-items:center;gap:1rem;border-radius:var(--radius-box);padding-inline:1rem;padding-block:.75rem;color:var(--color-base-content);background-color:var(--alert-color, var(--color-base-200));justify-content:start;justify-items:start;grid-auto-flow:column;grid-template-columns:auto;text-align:start;border:var(--border) solid var(--color-base-200);font-size:.875rem;line-height:1.25rem;background-size:auto,calc(var(--noise) * 100%);background-image:none,var(--fx-noise);box-shadow:0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * .08)) inset,0 1px color-mix(in oklab,color-mix(in oklab,#000 20%,var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%),#0000),0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * .08))}.alert:has(:nth-child(2)){grid-template-columns:auto minmax(auto,1fr)}.alert.alert-outline{background-color:transparent;color:var(--alert-color);box-shadow:none;background-image:none}.alert.alert-dash{background-color:transparent;color:var(--alert-color);border-style:dashed;box-shadow:none;background-image:none}.alert.alert-soft{color:var(--alert-color, var(--color-base-content));background:color-mix(in oklab,var(--alert-color, var(--color-base-content)) 8%,var(--color-base-100));border-color:color-mix(in oklab,var(--alert-color, var(--color-base-content)) 10%,var(--color-base-100));box-shadow:none;background-image:none}.btn-modern{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-radius:.75rem;padding:.75rem 1.5rem;font-weight:500;--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-modern:hover{--tw-scale-x: 1.05;--tw-scale-y: 1.05;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.btn-modern:focus{--tw-scale-x: 1.05;--tw-scale-y: 1.05;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: var(--color-primary);--tw-ring-opacity: .5}.btn-outline{border-radius:.75rem;border-width:2px;--tw-border-opacity: 1;border-color:rgb(29 78 216 / var(--tw-border-opacity, 1));background-color:transparent;padding:.75rem 1.5rem;font-weight:500;--tw-text-opacity: 1;color:rgb(29 78 216 / var(--tw-text-opacity, 1));transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-outline:hover{--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.btn-outline:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity, 1));--tw-ring-opacity: .5}.btn-primary-solid{border-radius:.75rem;--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1));padding:.75rem 1.5rem;font-weight:500;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.btn-primary-solid:hover{--tw-bg-opacity: 1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1));--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.btn-primary-solid:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity, 1));--tw-ring-opacity: .5}.card-hover{border-radius:.75rem;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.card-hover:hover{--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.textarea{resize:none;border-width:1px;--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.textarea::-moz-placeholder{--tw-placeholder-opacity: 1;color:rgb(107 114 128 / var(--tw-placeholder-opacity, 1))}.textarea::placeholder{--tw-placeholder-opacity: 1;color:rgb(107 114 128 / var(--tw-placeholder-opacity, 1))}.input{border-width:1px;--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.input::-moz-placeholder{--tw-placeholder-opacity: 1;color:rgb(107 114 128 / var(--tw-placeholder-opacity, 1))}.input::placeholder{--tw-placeholder-opacity: 1;color:rgb(107 114 128 / var(--tw-placeholder-opacity, 1))}@keyframes pulse{50%{opacity:.5}}.skeleton{animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite;border-radius:.75rem;--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.text-heading-1{font-size:2.25rem;line-height:2.5rem;font-weight:700;--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}@media (min-width: 1024px){.text-heading-1{font-size:3rem;line-height:1}}.text-body{font-size:1rem;line-height:1.5rem;--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-body-small{font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-caption{font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-muted{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.card-elevated{border-radius:.75rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:1.5rem;--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.card-elevated:hover{--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.container-section{padding:3rem 1rem}.container-content{margin-left:auto;margin-right:auto;max-width:80rem}.space-section{margin-bottom:3rem}.gradient-header{background-image:linear-gradient(to bottom right,var(--tw-gradient-stops));--tw-gradient-from: #1D4ED8 var(--tw-gradient-from-position);--tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);--tw-gradient-to: #10B981 var(--tw-gradient-to-position)}*{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.pointer-events-none{pointer-events:none}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{inset:0}.bottom-0{bottom:0}.bottom-20{bottom:5rem}.bottom-4{bottom:1rem}.left-0{left:0}.left-3{left:.75rem}.left-4{left:1rem}.right-0{right:0}.right-3{right:.75rem}.right-4{right:1rem}.top-1\/2{top:50%}.top-3{top:.75rem}.top-4{top:1rem}.z-40{z-index:40}.z-50{z-index:50}.col-span-full{grid-column:1 / -1}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:.25rem}.mb-12{margin-bottom:3rem}.mb-2{margin-bottom:.5rem}.mb-3{margin-bottom:.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-1{margin-left:.25rem}.ml-6{margin-left:1.5rem}.ml-auto{margin-left:auto}.mt-0\.5{margin-top:.125rem}.mt-1{margin-top:.25rem}.mt-12{margin-top:3rem}.mt-2{margin-top:.5rem}.mt-3{margin-top:.75rem}.mt-4{margin-top:1rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.line-clamp-2{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.block{display:block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}.hidden{display:none}.aspect-video{aspect-ratio:16 / 9}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-2{height:.5rem}.h-20{height:5rem}.h-3{height:.75rem}.h-4{height:1rem}.h-48{height:12rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-64{height:16rem}.h-8{height:2rem}.h-auto{height:auto}.h-full{height:100%}.max-h-96{max-height:24rem}.min-h-\[100px\]{min-height:100px}.min-h-\[120px\]{min-height:120px}.min-h-\[80px\]{min-height:80px}.min-h-screen{min-height:100vh}.w-1\/2{width:50%}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-2{width:.5rem}.w-20{width:5rem}.w-24{width:6rem}.w-3{width:.75rem}.w-3\/4{width:75%}.w-32{width:8rem}.w-4{width:1rem}.w-40{width:10rem}.w-48{width:12rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-64{width:16rem}.w-8{width:2rem}.w-full{width:100%}.min-w-0{min-width:0px}.max-w-2xl{max-width:42rem}.max-w-4xl{max-width:56rem}.max-w-6xl{max-width:72rem}.max-w-7xl{max-width:80rem}.max-w-md{max-width:28rem}.max-w-xs{max-width:20rem}.flex-1{flex:1 1 0%}.flex-shrink-0{flex-shrink:0}.-translate-y-1\/2{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.scale-105{--tw-scale-x: 1.05;--tw-scale-y: 1.05;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.cursor-pointer{cursor:pointer}.list-inside{list-style-position:inside}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-1{gap:.25rem}.gap-2{gap:.5rem}.gap-3{gap:.75rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.space-y-1>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.25rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.25rem * var(--tw-space-y-reverse))}.space-y-12>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(3rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(3rem * var(--tw-space-y-reverse))}.space-y-3>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.75rem * var(--tw-space-y-reverse))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-6>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.space-y-8>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.rounded{border-radius:.25rem}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:.5rem}.rounded-md{border-radius:.375rem}.rounded-xl{border-radius:.75rem}.border{border-width:1px}.border-2{border-width:2px}.border-4{border-width:4px}.border-t{border-top-width:1px}.border-dashed{border-style:dashed}.border-base-200{border-color:var(--color-base-200)}.border-blue-200{--tw-border-opacity: 1;border-color:rgb(191 219 254 / var(--tw-border-opacity, 1))}.border-gray-100{--tw-border-opacity: 1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.border-gray-300{--tw-border-opacity: 1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.border-jobblogg-accent{--tw-border-opacity: 1;border-color:rgb(16 185 129 / var(--tw-border-opacity, 1))}.border-jobblogg-error{--tw-border-opacity: 1;border-color:rgb(220 38 38 / var(--tw-border-opacity, 1))}.border-jobblogg-neutral{--tw-border-opacity: 1;border-color:rgb(248 250 252 / var(--tw-border-opacity, 1))}.border-jobblogg-neutral-secondary{--tw-border-opacity: 1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-jobblogg-primary{--tw-border-opacity: 1;border-color:rgb(29 78 216 / var(--tw-border-opacity, 1))}.border-jobblogg-primary\/20{border-color:#1d4ed833}.border-jobblogg-warning\/20{border-color:#fbbf2433}.border-white{--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.border-t-transparent{border-top-color:transparent}.bg-base-100{background-color:var(--color-base-100)}.bg-black\/60{background-color:#0009}.bg-blue-100{--tw-bg-opacity: 1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity: 1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity: 1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-gray-900{--tw-bg-opacity: 1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1))}.bg-jobblogg-accent-soft{--tw-bg-opacity: 1;background-color:rgb(209 250 229 / var(--tw-bg-opacity, 1))}.bg-jobblogg-blue-50{--tw-bg-opacity: 1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-jobblogg-error{--tw-bg-opacity: 1;background-color:rgb(220 38 38 / var(--tw-bg-opacity, 1))}.bg-jobblogg-error-soft{--tw-bg-opacity: 1;background-color:rgb(254 226 226 / var(--tw-bg-opacity, 1))}.bg-jobblogg-neutral{--tw-bg-opacity: 1;background-color:rgb(248 250 252 / var(--tw-bg-opacity, 1))}.bg-jobblogg-neutral-secondary{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-jobblogg-primary{--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.bg-jobblogg-primary-soft{--tw-bg-opacity: 1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-jobblogg-warning{--tw-bg-opacity: 1;background-color:rgb(251 191 36 / var(--tw-bg-opacity, 1))}.bg-jobblogg-warning-soft{--tw-bg-opacity: 1;background-color:rgb(254 243 199 / var(--tw-bg-opacity, 1))}.bg-transparent{background-color:transparent}.bg-white{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/80{background-color:#fffc}.bg-opacity-20{--tw-bg-opacity: .2}.bg-gradient-to-br{background-image:linear-gradient(to bottom right,var(--tw-gradient-stops))}.bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.bg-gradient-to-t{background-image:linear-gradient(to top,var(--tw-gradient-stops))}.from-accent{--tw-gradient-from: var(--color-accent) var(--tw-gradient-from-position);--tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.from-gray-900{--tw-gradient-from: #111827 var(--tw-gradient-from-position);--tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.from-jobblogg-blue-50{--tw-gradient-from: #EFF6FF var(--tw-gradient-from-position);--tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.from-primary{--tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);--tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.to-jobblogg-indigo-50{--tw-gradient-to: #EEF2FF var(--tw-gradient-to-position)}.to-primary{--tw-gradient-to: var(--color-primary) var(--tw-gradient-to-position)}.to-secondary{--tw-gradient-to: var(--color-secondary) var(--tw-gradient-to-position)}.to-transparent{--tw-gradient-to: transparent var(--tw-gradient-to-position)}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.object-cover{-o-object-fit:cover;object-fit:cover}.p-0{padding:0}.p-2{padding:.5rem}.p-3{padding:.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.py-16{padding-top:4rem;padding-bottom:4rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.py-3{padding-top:.75rem;padding-bottom:.75rem}.py-8{padding-top:2rem;padding-bottom:2rem}.pl-10{padding-left:2.5rem}.pr-10{padding-right:2.5rem}.pt-3{padding-top:.75rem}.pt-6{padding-top:1.5rem}.text-center{text-align:center}.font-mono{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.italic{font-style:italic}.tabular-nums{--tw-numeric-spacing: tabular-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.leading-relaxed{line-height:1.625}.text-accent{color:var(--color-accent)}.text-base-content{color:var(--color-base-content)}.text-error{color:var(--color-error)}.text-gray-200{--tw-text-opacity: 1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.text-info{color:var(--color-info)}.text-jobblogg-error{--tw-text-opacity: 1;color:rgb(220 38 38 / var(--tw-text-opacity, 1))}.text-jobblogg-primary{--tw-text-opacity: 1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.text-jobblogg-text-medium{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-jobblogg-text-muted{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-jobblogg-text-strong{--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.text-jobblogg-warning{--tw-text-opacity: 1;color:rgb(251 191 36 / var(--tw-text-opacity, 1))}.text-primary{color:var(--color-primary)}.text-secondary{color:var(--color-secondary)}.text-success{color:var(--color-success)}.text-transparent{color:transparent}.text-warning{color:var(--color-warning)}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.underline{text-decoration-line:underline}.opacity-0{opacity:0}.opacity-100{opacity:1}.opacity-25{opacity:.25}.opacity-75{opacity:.75}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-sm{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.ring{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.ring-2{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.blur-sm{--tw-blur: blur(4px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.backdrop-blur-sm{--tw-backdrop-blur: blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200{transition-duration:.2s}.duration-300{transition-duration:.3s}.join{display:inline-flex;align-items:stretch;--join-ss: 0;--join-se: 0;--join-es: 0;--join-ee: 0}.join :where(.join-item){border-start-start-radius:var(--join-ss, 0);border-start-end-radius:var(--join-se, 0);border-end-start-radius:var(--join-es, 0);border-end-end-radius:var(--join-ee, 0)}.join :where(.join-item) *{--join-ss: var(--radius-field);--join-se: var(--radius-field);--join-es: var(--radius-field);--join-ee: var(--radius-field)}.join>.join-item:where(:first-child){--join-ss: var(--radius-field);--join-se: 0;--join-es: var(--radius-field);--join-ee: 0}.join :first-child:not(:last-child) :where(.join-item){--join-ss: var(--radius-field);--join-se: 0;--join-es: var(--radius-field);--join-ee: 0}.join>.join-item:where(:last-child){--join-ss: 0;--join-se: var(--radius-field);--join-es: 0;--join-ee: var(--radius-field)}.join :last-child:not(:first-child) :where(.join-item){--join-ss: 0;--join-se: var(--radius-field);--join-es: 0;--join-ee: var(--radius-field)}.join>.join-item:where(:only-child){--join-ss: var(--radius-field);--join-se: var(--radius-field);--join-es: var(--radius-field);--join-ee: var(--radius-field)}.join :only-child :where(.join-item){--join-ss: var(--radius-field);--join-se: var(--radius-field);--join-es: var(--radius-field);--join-ee: var(--radius-field)}.animate-fade-in{animation:fadeIn .5s ease-in-out}.animate-slide-up{animation:slideUp .3s ease-out}.animate-scale-in{animation:scaleIn .2s ease-out}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideUp{0%{transform:translateY(10px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes scaleIn{0%{transform:scale(.95);opacity:0}to{transform:scale(1);opacity:1}}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}.placeholder\:text-jobblogg-text-muted::-moz-placeholder{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.placeholder\:text-jobblogg-text-muted::placeholder{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.hover\:border-jobblogg-primary:hover{--tw-border-opacity: 1;border-color:rgb(29 78 216 / var(--tw-border-opacity, 1))}.hover\:bg-jobblogg-neutral:hover{--tw-bg-opacity: 1;background-color:rgb(248 250 252 / var(--tw-bg-opacity, 1))}.hover\:bg-jobblogg-primary:hover{--tw-bg-opacity: 1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.hover\:bg-jobblogg-primary-light:hover{--tw-bg-opacity: 1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}.hover\:bg-jobblogg-primary-soft:hover{--tw-bg-opacity: 1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.hover\:text-jobblogg-text-medium:hover{--tw-text-opacity: 1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.hover\:text-jobblogg-text-strong:hover{--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.hover\:text-white:hover{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.hover\:opacity-100:hover{opacity:1}.hover\:shadow-lg:hover{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.hover\:shadow-md:hover{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.hover\:shadow-xl:hover{--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus\:border-jobblogg-error:focus{--tw-border-opacity: 1;border-color:rgb(220 38 38 / var(--tw-border-opacity, 1))}.focus\:border-jobblogg-primary:focus{--tw-border-opacity: 1;border-color:rgb(29 78 216 / var(--tw-border-opacity, 1))}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus\:ring-jobblogg-error:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1))}.focus\:ring-jobblogg-primary:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity, 1))}.focus\:ring-opacity-50:focus{--tw-ring-opacity: .5}.focus\:ring-offset-2:focus{--tw-ring-offset-width: 2px}.focus\:ring-offset-white:focus{--tw-ring-offset-color: #fff}.active\:shadow-sm:active{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:resize-none:disabled{resize:none}.disabled\:bg-jobblogg-neutral:disabled{--tw-bg-opacity: 1;background-color:rgb(248 250 252 / var(--tw-bg-opacity, 1))}.disabled\:bg-jobblogg-neutral-secondary:disabled{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.disabled\:text-jobblogg-text-muted:disabled{--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.disabled\:opacity-50:disabled{opacity:.5}.disabled\:shadow-none:disabled{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.group:hover .group-hover\:rotate-90{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .group-hover\:scale-105{--tw-scale-x: 1.05;--tw-scale-y: 1.05;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group:hover .group-hover\:from-jobblogg-blue-100{--tw-gradient-from: #DBEAFE var(--tw-gradient-from-position);--tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.group:hover .group-hover\:to-jobblogg-indigo-100{--tw-gradient-to: #E0E7FF var(--tw-gradient-to-position)}.group:hover .group-hover\:text-jobblogg-primary{--tw-text-opacity: 1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.group:hover .group-hover\:text-jobblogg-primary-light{--tw-text-opacity: 1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.group:hover .group-hover\:shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}@media (min-width: 640px){.sm\:w-auto{width:auto}.sm\:flex-none{flex:none}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:flex-row{flex-direction:row}.sm\:items-center{align-items:center}}@media (min-width: 768px){.md\:left-auto{left:auto}.md\:right-4{right:1rem}.md\:max-w-sm{max-width:24rem}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (min-width: 1024px){.lg\:col-span-2{grid-column:span 2 / span 2}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}
