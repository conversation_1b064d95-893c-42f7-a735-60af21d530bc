const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-BUFpwwdV.js","assets/Heading2-D3ePk88U.js","assets/EmptyState-C2i22Zrb.js","assets/CreateProject-BhjuMJEe.js","assets/BodyText-DuggM1es.js","assets/PageLayout-B8oEbEaj.js","assets/SubmitButton-C8eu-4EH.js","assets/ProjectDetail-BSOuW_cT.js","assets/ProjectLog-D4PKsjCb.js"])))=>i.map(i=>d[i]);
(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))o(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function r(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(u){if(u.ep)return;u.ep=!0;const f=r(u);fetch(u.href,f)}})();function Gb(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var uc={exports:{}},Ul={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cg;function Xb(){if(cg)return Ul;cg=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function r(o,u,f){var d=null;if(f!==void 0&&(d=""+f),u.key!==void 0&&(d=""+u.key),"key"in u){f={};for(var v in u)v!=="key"&&(f[v]=u[v])}else f=u;return u=f.ref,{$$typeof:i,type:o,key:d,ref:u!==void 0?u:null,props:f}}return Ul.Fragment=l,Ul.jsx=r,Ul.jsxs=r,Ul}var fg;function Kb(){return fg||(fg=1,uc.exports=Xb()),uc.exports}var M=Kb(),cc={exports:{}},oe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dg;function Wb(){if(dg)return oe;dg=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),C=Symbol.iterator;function S(_){return _===null||typeof _!="object"?null:(_=C&&_[C]||_["@@iterator"],typeof _=="function"?_:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,D={};function L(_,Q,$){this.props=_,this.context=Q,this.refs=D,this.updater=$||O}L.prototype.isReactComponent={},L.prototype.setState=function(_,Q){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,Q,"setState")},L.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function T(){}T.prototype=L.prototype;function H(_,Q,$){this.props=_,this.context=Q,this.refs=D,this.updater=$||O}var Y=H.prototype=new T;Y.constructor=H,w(Y,L.prototype),Y.isPureReactComponent=!0;var F=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},Z=Object.prototype.hasOwnProperty;function re(_,Q,$,G,J,fe){return $=fe.ref,{$$typeof:i,type:_,key:Q,ref:$!==void 0?$:null,props:fe}}function X(_,Q){return re(_.type,Q,void 0,void 0,void 0,_.props)}function se(_){return typeof _=="object"&&_!==null&&_.$$typeof===i}function ie(_){var Q={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function($){return Q[$]})}var be=/\/+/g;function ve(_,Q){return typeof _=="object"&&_!==null&&_.key!=null?ie(""+_.key):Q.toString(36)}function Ue(){}function ze(_){switch(_.status){case"fulfilled":return _.value;case"rejected":throw _.reason;default:switch(typeof _.status=="string"?_.then(Ue,Ue):(_.status="pending",_.then(function(Q){_.status==="pending"&&(_.status="fulfilled",_.value=Q)},function(Q){_.status==="pending"&&(_.status="rejected",_.reason=Q)})),_.status){case"fulfilled":return _.value;case"rejected":throw _.reason}}throw _}function Oe(_,Q,$,G,J){var fe=typeof _;(fe==="undefined"||fe==="boolean")&&(_=null);var ae=!1;if(_===null)ae=!0;else switch(fe){case"bigint":case"string":case"number":ae=!0;break;case"object":switch(_.$$typeof){case i:case l:ae=!0;break;case b:return ae=_._init,Oe(ae(_._payload),Q,$,G,J)}}if(ae)return J=J(_),ae=G===""?"."+ve(_,0):G,F(J)?($="",ae!=null&&($=ae.replace(be,"$&/")+"/"),Oe(J,Q,$,"",function(nn){return nn})):J!=null&&(se(J)&&(J=X(J,$+(J.key==null||_&&_.key===J.key?"":(""+J.key).replace(be,"$&/")+"/")+ae)),Q.push(J)),1;ae=0;var ut=G===""?".":G+":";if(F(_))for(var Me=0;Me<_.length;Me++)G=_[Me],fe=ut+ve(G,Me),ae+=Oe(G,Q,$,fe,J);else if(Me=S(_),typeof Me=="function")for(_=Me.call(_),Me=0;!(G=_.next()).done;)G=G.value,fe=ut+ve(G,Me++),ae+=Oe(G,Q,$,fe,J);else if(fe==="object"){if(typeof _.then=="function")return Oe(ze(_),Q,$,G,J);throw Q=String(_),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return ae}function z(_,Q,$){if(_==null)return _;var G=[],J=0;return Oe(_,G,"","",function(fe){return Q.call($,fe,J++)}),G}function I(_){if(_._status===-1){var Q=_._result;Q=Q(),Q.then(function($){(_._status===0||_._status===-1)&&(_._status=1,_._result=$)},function($){(_._status===0||_._status===-1)&&(_._status=2,_._result=$)}),_._status===-1&&(_._status=0,_._result=Q)}if(_._status===1)return _._result.default;throw _._result}var W=typeof reportError=="function"?reportError:function(_){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof _=="object"&&_!==null&&typeof _.message=="string"?String(_.message):String(_),error:_});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",_);return}console.error(_)};function ge(){}return oe.Children={map:z,forEach:function(_,Q,$){z(_,function(){Q.apply(this,arguments)},$)},count:function(_){var Q=0;return z(_,function(){Q++}),Q},toArray:function(_){return z(_,function(Q){return Q})||[]},only:function(_){if(!se(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},oe.Component=L,oe.Fragment=r,oe.Profiler=u,oe.PureComponent=H,oe.StrictMode=o,oe.Suspense=m,oe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,oe.__COMPILER_RUNTIME={__proto__:null,c:function(_){return P.H.useMemoCache(_)}},oe.cache=function(_){return function(){return _.apply(null,arguments)}},oe.cloneElement=function(_,Q,$){if(_==null)throw Error("The argument must be a React element, but you passed "+_+".");var G=w({},_.props),J=_.key,fe=void 0;if(Q!=null)for(ae in Q.ref!==void 0&&(fe=void 0),Q.key!==void 0&&(J=""+Q.key),Q)!Z.call(Q,ae)||ae==="key"||ae==="__self"||ae==="__source"||ae==="ref"&&Q.ref===void 0||(G[ae]=Q[ae]);var ae=arguments.length-2;if(ae===1)G.children=$;else if(1<ae){for(var ut=Array(ae),Me=0;Me<ae;Me++)ut[Me]=arguments[Me+2];G.children=ut}return re(_.type,J,void 0,void 0,fe,G)},oe.createContext=function(_){return _={$$typeof:d,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null},_.Provider=_,_.Consumer={$$typeof:f,_context:_},_},oe.createElement=function(_,Q,$){var G,J={},fe=null;if(Q!=null)for(G in Q.key!==void 0&&(fe=""+Q.key),Q)Z.call(Q,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(J[G]=Q[G]);var ae=arguments.length-2;if(ae===1)J.children=$;else if(1<ae){for(var ut=Array(ae),Me=0;Me<ae;Me++)ut[Me]=arguments[Me+2];J.children=ut}if(_&&_.defaultProps)for(G in ae=_.defaultProps,ae)J[G]===void 0&&(J[G]=ae[G]);return re(_,fe,void 0,void 0,null,J)},oe.createRef=function(){return{current:null}},oe.forwardRef=function(_){return{$$typeof:v,render:_}},oe.isValidElement=se,oe.lazy=function(_){return{$$typeof:b,_payload:{_status:-1,_result:_},_init:I}},oe.memo=function(_,Q){return{$$typeof:g,type:_,compare:Q===void 0?null:Q}},oe.startTransition=function(_){var Q=P.T,$={};P.T=$;try{var G=_(),J=P.S;J!==null&&J($,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(ge,W)}catch(fe){W(fe)}finally{P.T=Q}},oe.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},oe.use=function(_){return P.H.use(_)},oe.useActionState=function(_,Q,$){return P.H.useActionState(_,Q,$)},oe.useCallback=function(_,Q){return P.H.useCallback(_,Q)},oe.useContext=function(_){return P.H.useContext(_)},oe.useDebugValue=function(){},oe.useDeferredValue=function(_,Q){return P.H.useDeferredValue(_,Q)},oe.useEffect=function(_,Q,$){var G=P.H;if(typeof $=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(_,Q)},oe.useId=function(){return P.H.useId()},oe.useImperativeHandle=function(_,Q,$){return P.H.useImperativeHandle(_,Q,$)},oe.useInsertionEffect=function(_,Q){return P.H.useInsertionEffect(_,Q)},oe.useLayoutEffect=function(_,Q){return P.H.useLayoutEffect(_,Q)},oe.useMemo=function(_,Q){return P.H.useMemo(_,Q)},oe.useOptimistic=function(_,Q){return P.H.useOptimistic(_,Q)},oe.useReducer=function(_,Q,$){return P.H.useReducer(_,Q,$)},oe.useRef=function(_){return P.H.useRef(_)},oe.useState=function(_){return P.H.useState(_)},oe.useSyncExternalStore=function(_,Q,$){return P.H.useSyncExternalStore(_,Q,$)},oe.useTransition=function(){return P.H.useTransition()},oe.version="19.1.0",oe}var hg;function Hs(){return hg||(hg=1,cc.exports=Wb()),cc.exports}var x=Hs();const q=Gb(x);var fc={exports:{}},Dl={},dc={exports:{}},hc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mg;function Zb(){return mg||(mg=1,function(i){function l(z,I){var W=z.length;z.push(I);e:for(;0<W;){var ge=W-1>>>1,_=z[ge];if(0<u(_,I))z[ge]=I,z[W]=_,W=ge;else break e}}function r(z){return z.length===0?null:z[0]}function o(z){if(z.length===0)return null;var I=z[0],W=z.pop();if(W!==I){z[0]=W;e:for(var ge=0,_=z.length,Q=_>>>1;ge<Q;){var $=2*(ge+1)-1,G=z[$],J=$+1,fe=z[J];if(0>u(G,W))J<_&&0>u(fe,G)?(z[ge]=fe,z[J]=W,ge=J):(z[ge]=G,z[$]=W,ge=$);else if(J<_&&0>u(fe,W))z[ge]=fe,z[J]=W,ge=J;else break e}}return I}function u(z,I){var W=z.sortIndex-I.sortIndex;return W!==0?W:z.id-I.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;i.unstable_now=function(){return f.now()}}else{var d=Date,v=d.now();i.unstable_now=function(){return d.now()-v}}var m=[],g=[],b=1,C=null,S=3,O=!1,w=!1,D=!1,L=!1,T=typeof setTimeout=="function"?setTimeout:null,H=typeof clearTimeout=="function"?clearTimeout:null,Y=typeof setImmediate<"u"?setImmediate:null;function F(z){for(var I=r(g);I!==null;){if(I.callback===null)o(g);else if(I.startTime<=z)o(g),I.sortIndex=I.expirationTime,l(m,I);else break;I=r(g)}}function P(z){if(D=!1,F(z),!w)if(r(m)!==null)w=!0,Z||(Z=!0,ve());else{var I=r(g);I!==null&&Oe(P,I.startTime-z)}}var Z=!1,re=-1,X=5,se=-1;function ie(){return L?!0:!(i.unstable_now()-se<X)}function be(){if(L=!1,Z){var z=i.unstable_now();se=z;var I=!0;try{e:{w=!1,D&&(D=!1,H(re),re=-1),O=!0;var W=S;try{t:{for(F(z),C=r(m);C!==null&&!(C.expirationTime>z&&ie());){var ge=C.callback;if(typeof ge=="function"){C.callback=null,S=C.priorityLevel;var _=ge(C.expirationTime<=z);if(z=i.unstable_now(),typeof _=="function"){C.callback=_,F(z),I=!0;break t}C===r(m)&&o(m),F(z)}else o(m);C=r(m)}if(C!==null)I=!0;else{var Q=r(g);Q!==null&&Oe(P,Q.startTime-z),I=!1}}break e}finally{C=null,S=W,O=!1}I=void 0}}finally{I?ve():Z=!1}}}var ve;if(typeof Y=="function")ve=function(){Y(be)};else if(typeof MessageChannel<"u"){var Ue=new MessageChannel,ze=Ue.port2;Ue.port1.onmessage=be,ve=function(){ze.postMessage(null)}}else ve=function(){T(be,0)};function Oe(z,I){re=T(function(){z(i.unstable_now())},I)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(z){z.callback=null},i.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<z?Math.floor(1e3/z):5},i.unstable_getCurrentPriorityLevel=function(){return S},i.unstable_next=function(z){switch(S){case 1:case 2:case 3:var I=3;break;default:I=S}var W=S;S=I;try{return z()}finally{S=W}},i.unstable_requestPaint=function(){L=!0},i.unstable_runWithPriority=function(z,I){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var W=S;S=z;try{return I()}finally{S=W}},i.unstable_scheduleCallback=function(z,I,W){var ge=i.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?ge+W:ge):W=ge,z){case 1:var _=-1;break;case 2:_=250;break;case 5:_=1073741823;break;case 4:_=1e4;break;default:_=5e3}return _=W+_,z={id:b++,callback:I,priorityLevel:z,startTime:W,expirationTime:_,sortIndex:-1},W>ge?(z.sortIndex=W,l(g,z),r(m)===null&&z===r(g)&&(D?(H(re),re=-1):D=!0,Oe(P,W-ge))):(z.sortIndex=_,l(m,z),w||O||(w=!0,Z||(Z=!0,ve()))),z},i.unstable_shouldYield=ie,i.unstable_wrapCallback=function(z){var I=S;return function(){var W=S;S=I;try{return z.apply(this,arguments)}finally{S=W}}}}(hc)),hc}var gg;function Fb(){return gg||(gg=1,dc.exports=Zb()),dc.exports}var mc={exports:{}},dt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pg;function Jb(){if(pg)return dt;pg=1;var i=Hs();function l(m){var g="https://react.dev/errors/"+m;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)g+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+m+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(l(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},u=Symbol.for("react.portal");function f(m,g,b){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:C==null?null:""+C,children:m,containerInfo:g,implementation:b}}var d=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(m,g){if(m==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return dt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,dt.createPortal=function(m,g){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(l(299));return f(m,g,null,b)},dt.flushSync=function(m){var g=d.T,b=o.p;try{if(d.T=null,o.p=2,m)return m()}finally{d.T=g,o.p=b,o.d.f()}},dt.preconnect=function(m,g){typeof m=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(m,g))},dt.prefetchDNS=function(m){typeof m=="string"&&o.d.D(m)},dt.preinit=function(m,g){if(typeof m=="string"&&g&&typeof g.as=="string"){var b=g.as,C=v(b,g.crossOrigin),S=typeof g.integrity=="string"?g.integrity:void 0,O=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;b==="style"?o.d.S(m,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:C,integrity:S,fetchPriority:O}):b==="script"&&o.d.X(m,{crossOrigin:C,integrity:S,fetchPriority:O,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},dt.preinitModule=function(m,g){if(typeof m=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var b=v(g.as,g.crossOrigin);o.d.M(m,{crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(m)},dt.preload=function(m,g){if(typeof m=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var b=g.as,C=v(b,g.crossOrigin);o.d.L(m,b,{crossOrigin:C,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},dt.preloadModule=function(m,g){if(typeof m=="string")if(g){var b=v(g.as,g.crossOrigin);o.d.m(m,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(m)},dt.requestFormReset=function(m){o.d.r(m)},dt.unstable_batchedUpdates=function(m,g){return m(g)},dt.useFormState=function(m,g,b){return d.H.useFormState(m,g,b)},dt.useFormStatus=function(){return d.H.useHostTransitionStatus()},dt.version="19.1.0",dt}var vg;function hp(){if(vg)return mc.exports;vg=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),mc.exports=Jb(),mc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yg;function e0(){if(yg)return Dl;yg=1;var i=Fb(),l=Hs(),r=hp();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(f(e)!==e)throw Error(o(188))}function m(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,a=t;;){var s=n.return;if(s===null)break;var c=s.alternate;if(c===null){if(a=s.return,a!==null){n=a;continue}break}if(s.child===c.child){for(c=s.child;c;){if(c===n)return v(s),e;if(c===a)return v(s),t;c=c.sibling}throw Error(o(188))}if(n.return!==a.return)n=s,a=c;else{for(var h=!1,p=s.child;p;){if(p===n){h=!0,n=s,a=c;break}if(p===a){h=!0,a=s,n=c;break}p=p.sibling}if(!h){for(p=c.child;p;){if(p===n){h=!0,n=c,a=s;break}if(p===a){h=!0,a=c,n=s;break}p=p.sibling}if(!h)throw Error(o(189))}}if(n.alternate!==a)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,C=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),H=Symbol.for("react.consumer"),Y=Symbol.for("react.context"),F=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),re=Symbol.for("react.memo"),X=Symbol.for("react.lazy"),se=Symbol.for("react.activity"),ie=Symbol.for("react.memo_cache_sentinel"),be=Symbol.iterator;function ve(e){return e===null||typeof e!="object"?null:(e=be&&e[be]||e["@@iterator"],typeof e=="function"?e:null)}var Ue=Symbol.for("react.client.reference");function ze(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ue?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case w:return"Fragment";case L:return"Profiler";case D:return"StrictMode";case P:return"Suspense";case Z:return"SuspenseList";case se:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case Y:return(e.displayName||"Context")+".Provider";case H:return(e._context.displayName||"Context")+".Consumer";case F:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case re:return t=e.displayName||null,t!==null?t:ze(e.type)||"Memo";case X:t=e._payload,e=e._init;try{return ze(e(t))}catch{}}return null}var Oe=Array.isArray,z=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W={pending:!1,data:null,method:null,action:null},ge=[],_=-1;function Q(e){return{current:e}}function $(e){0>_||(e.current=ge[_],ge[_]=null,_--)}function G(e,t){_++,ge[_]=e.current,e.current=t}var J=Q(null),fe=Q(null),ae=Q(null),ut=Q(null);function Me(e,t){switch(G(ae,t),G(fe,e),G(J,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?qm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=qm(t),e=Bm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}$(J),G(J,e)}function nn(){$(J),$(fe),$(ae)}function mn(e){e.memoizedState!==null&&G(ut,e);var t=J.current,n=Bm(t,e.type);t!==n&&(G(fe,e),G(J,n))}function hi(e){fe.current===e&&($(J),$(fe)),ut.current===e&&($(ut),Ol._currentValue=W)}var tr=Object.prototype.hasOwnProperty,Ee=i.unstable_scheduleCallback,_e=i.unstable_cancelCallback,Fe=i.unstable_shouldYield,Ne=i.unstable_requestPaint,He=i.unstable_now,Ln=i.unstable_getCurrentPriorityLevel,$e=i.unstable_ImmediatePriority,Je=i.unstable_UserBlockingPriority,bt=i.unstable_NormalPriority,nr=i.unstable_LowPriority,za=i.unstable_IdlePriority,ir=i.log,St=i.unstable_setDisableYieldValue,gt=null,at=null;function an(e){if(typeof ir=="function"&&St(e),at&&typeof at.setStrictMode=="function")try{at.setStrictMode(gt,e)}catch{}}var lt=Math.clz32?Math.clz32:Uv,Mv=Math.log,jv=Math.LN2;function Uv(e){return e>>>=0,e===0?32:31-(Mv(e)/jv|0)|0}var ar=256,lr=4194304;function mi(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function rr(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var s=0,c=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var p=a&134217727;return p!==0?(a=p&~c,a!==0?s=mi(a):(h&=p,h!==0?s=mi(h):n||(n=p&~e,n!==0&&(s=mi(n))))):(p=a&~c,p!==0?s=mi(p):h!==0?s=mi(h):n||(n=a&~e,n!==0&&(s=mi(n)))),s===0?0:t!==0&&t!==s&&(t&c)===0&&(c=s&-s,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:s}function Na(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Dv(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _f(){var e=ar;return ar<<=1,(ar&4194048)===0&&(ar=256),e}function Ef(){var e=lr;return lr<<=1,(lr&62914560)===0&&(lr=4194304),e}function Zs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function qa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Lv(e,t,n,a,s,c){var h=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,y=e.expirationTimes,k=e.hiddenUpdates;for(n=h&~n;0<n;){var N=31-lt(n),V=1<<N;p[N]=0,y[N]=-1;var j=k[N];if(j!==null)for(k[N]=null,N=0;N<j.length;N++){var U=j[N];U!==null&&(U.lane&=-536870913)}n&=~V}a!==0&&wf(e,a,0),c!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=c&~(h&~t))}function wf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-lt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function xf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-lt(n),s=1<<a;s&t|e[a]&t&&(e[a]|=t),n&=~s}}function Fs(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Af(){var e=I.p;return e!==0?e:(e=window.event,e===void 0?32:ag(e.type))}function zv(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}var zn=Math.random().toString(36).slice(2),ct="__reactFiber$"+zn,_t="__reactProps$"+zn,zi="__reactContainer$"+zn,eo="__reactEvents$"+zn,Nv="__reactListeners$"+zn,qv="__reactHandles$"+zn,Tf="__reactResources$"+zn,Ba="__reactMarker$"+zn;function to(e){delete e[ct],delete e[_t],delete e[eo],delete e[Nv],delete e[qv]}function Ni(e){var t=e[ct];if(t)return t;for(var n=e.parentNode;n;){if(t=n[zi]||n[ct]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pm(e);e!==null;){if(n=e[ct])return n;e=Pm(e)}return t}e=n,n=e.parentNode}return null}function qi(e){if(e=e[ct]||e[zi]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ha(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function Bi(e){var t=e[Tf];return t||(t=e[Tf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function et(e){e[Ba]=!0}var Cf=new Set,Of={};function gi(e,t){Hi(e,t),Hi(e+"Capture",t)}function Hi(e,t){for(Of[e]=t,e=0;e<t.length;e++)Cf.add(t[e])}var Bv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Rf={},kf={};function Hv(e){return tr.call(kf,e)?!0:tr.call(Rf,e)?!1:Bv.test(e)?kf[e]=!0:(Rf[e]=!0,!1)}function sr(e,t,n){if(Hv(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function or(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function gn(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var no,Mf;function Vi(e){if(no===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);no=t&&t[1]||"",Mf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+no+e+Mf}var io=!1;function ao(e,t){if(!e||io)return"";io=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(U){var j=U}Reflect.construct(e,[],V)}else{try{V.call()}catch(U){j=U}e.call(V.prototype)}}else{try{throw Error()}catch(U){j=U}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(U){if(U&&j&&typeof U.stack=="string")return[U.stack,j.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=a.DetermineComponentFrameRoot(),h=c[0],p=c[1];if(h&&p){var y=h.split(`
`),k=p.split(`
`);for(s=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;s<k.length&&!k[s].includes("DetermineComponentFrameRoot");)s++;if(a===y.length||s===k.length)for(a=y.length-1,s=k.length-1;1<=a&&0<=s&&y[a]!==k[s];)s--;for(;1<=a&&0<=s;a--,s--)if(y[a]!==k[s]){if(a!==1||s!==1)do if(a--,s--,0>s||y[a]!==k[s]){var N=`
`+y[a].replace(" at new "," at ");return e.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",e.displayName)),N}while(1<=a&&0<=s);break}}}finally{io=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Vi(n):""}function Vv(e){switch(e.tag){case 26:case 27:case 5:return Vi(e.type);case 16:return Vi("Lazy");case 13:return Vi("Suspense");case 19:return Vi("SuspenseList");case 0:case 15:return ao(e.type,!1);case 11:return ao(e.type.render,!1);case 1:return ao(e.type,!0);case 31:return Vi("Activity");default:return""}}function jf(e){try{var t="";do t+=Vv(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function zt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Uf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Qv(e){var t=Uf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(h){a=""+h,c.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(h){a=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ur(e){e._valueTracker||(e._valueTracker=Qv(e))}function Df(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Uf(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function cr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Pv=/[\n"\\]/g;function Nt(e){return e.replace(Pv,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function lo(e,t,n,a,s,c,h,p){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+zt(t)):e.value!==""+zt(t)&&(e.value=""+zt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?ro(e,h,zt(t)):n!=null?ro(e,h,zt(n)):a!=null&&e.removeAttribute("value"),s==null&&c!=null&&(e.defaultChecked=!!c),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+zt(p):e.removeAttribute("name")}function Lf(e,t,n,a,s,c,h,p){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+zt(n):"",t=t!=null?""+zt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}a=a??s,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=p?e.checked:!!a,e.defaultChecked=!!a,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function ro(e,t,n){t==="number"&&cr(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Qi(e,t,n,a){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&a&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,a&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function zf(e,t,n){if(t!=null&&(t=""+zt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+zt(n):""}function Nf(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(o(92));if(Oe(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=zt(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function Pi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Yv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function qf(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Yv.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Bf(e,t,n){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var s in t)a=t[s],t.hasOwnProperty(s)&&n[s]!==a&&qf(e,s,a)}else for(var c in t)t.hasOwnProperty(c)&&qf(e,c,t[c])}function so(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Iv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$v=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function fr(e){return $v.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var oo=null;function uo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Yi=null,Ii=null;function Hf(e){var t=qi(e);if(t&&(e=t.stateNode)){var n=e[_t]||null;e:switch(e=t.stateNode,t.type){case"input":if(lo(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Nt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var s=a[_t]||null;if(!s)throw Error(o(90));lo(a,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&Df(a)}break e;case"textarea":zf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Qi(e,!!n.multiple,t,!1)}}}var co=!1;function Vf(e,t,n){if(co)return e(t,n);co=!0;try{var a=e(t);return a}finally{if(co=!1,(Yi!==null||Ii!==null)&&(Wr(),Yi&&(t=Yi,e=Ii,Ii=Yi=null,Hf(t),e)))for(t=0;t<e.length;t++)Hf(e[t])}}function Va(e,t){var n=e.stateNode;if(n===null)return null;var a=n[_t]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var pn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fo=!1;if(pn)try{var Qa={};Object.defineProperty(Qa,"passive",{get:function(){fo=!0}}),window.addEventListener("test",Qa,Qa),window.removeEventListener("test",Qa,Qa)}catch{fo=!1}var Nn=null,ho=null,dr=null;function Qf(){if(dr)return dr;var e,t=ho,n=t.length,a,s="value"in Nn?Nn.value:Nn.textContent,c=s.length;for(e=0;e<n&&t[e]===s[e];e++);var h=n-e;for(a=1;a<=h&&t[n-a]===s[c-a];a++);return dr=s.slice(e,1<a?1-a:void 0)}function hr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function mr(){return!0}function Pf(){return!1}function Et(e){function t(n,a,s,c,h){this._reactName=n,this._targetInst=s,this.type=a,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(c):c[p]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?mr:Pf,this.isPropagationStopped=Pf,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=mr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=mr)},persist:function(){},isPersistent:mr}),t}var pi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},gr=Et(pi),Pa=b({},pi,{view:0,detail:0}),Gv=Et(Pa),mo,go,Ya,pr=b({},Pa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ya&&(Ya&&e.type==="mousemove"?(mo=e.screenX-Ya.screenX,go=e.screenY-Ya.screenY):go=mo=0,Ya=e),mo)},movementY:function(e){return"movementY"in e?e.movementY:go}}),Yf=Et(pr),Xv=b({},pr,{dataTransfer:0}),Kv=Et(Xv),Wv=b({},Pa,{relatedTarget:0}),po=Et(Wv),Zv=b({},pi,{animationName:0,elapsedTime:0,pseudoElement:0}),Fv=Et(Zv),Jv=b({},pi,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ey=Et(Jv),ty=b({},pi,{data:0}),If=Et(ty),ny={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},iy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ay={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ly(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ay[e])?!!t[e]:!1}function vo(){return ly}var ry=b({},Pa,{key:function(e){if(e.key){var t=ny[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=hr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?iy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vo,charCode:function(e){return e.type==="keypress"?hr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?hr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),sy=Et(ry),oy=b({},pr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$f=Et(oy),uy=b({},Pa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vo}),cy=Et(uy),fy=b({},pi,{propertyName:0,elapsedTime:0,pseudoElement:0}),dy=Et(fy),hy=b({},pr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),my=Et(hy),gy=b({},pi,{newState:0,oldState:0}),py=Et(gy),vy=[9,13,27,32],yo=pn&&"CompositionEvent"in window,Ia=null;pn&&"documentMode"in document&&(Ia=document.documentMode);var yy=pn&&"TextEvent"in window&&!Ia,Gf=pn&&(!yo||Ia&&8<Ia&&11>=Ia),Xf=" ",Kf=!1;function Wf(e,t){switch(e){case"keyup":return vy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $i=!1;function by(e,t){switch(e){case"compositionend":return Zf(t);case"keypress":return t.which!==32?null:(Kf=!0,Xf);case"textInput":return e=t.data,e===Xf&&Kf?null:e;default:return null}}function Sy(e,t){if($i)return e==="compositionend"||!yo&&Wf(e,t)?(e=Qf(),dr=ho=Nn=null,$i=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Gf&&t.locale!=="ko"?null:t.data;default:return null}}var _y={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ff(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!_y[e.type]:t==="textarea"}function Jf(e,t,n,a){Yi?Ii?Ii.push(a):Ii=[a]:Yi=a,t=ns(t,"onChange"),0<t.length&&(n=new gr("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var $a=null,Ga=null;function Ey(e){Um(e,0)}function vr(e){var t=Ha(e);if(Df(t))return e}function ed(e,t){if(e==="change")return t}var td=!1;if(pn){var bo;if(pn){var So="oninput"in document;if(!So){var nd=document.createElement("div");nd.setAttribute("oninput","return;"),So=typeof nd.oninput=="function"}bo=So}else bo=!1;td=bo&&(!document.documentMode||9<document.documentMode)}function id(){$a&&($a.detachEvent("onpropertychange",ad),Ga=$a=null)}function ad(e){if(e.propertyName==="value"&&vr(Ga)){var t=[];Jf(t,Ga,e,uo(e)),Vf(Ey,t)}}function wy(e,t,n){e==="focusin"?(id(),$a=t,Ga=n,$a.attachEvent("onpropertychange",ad)):e==="focusout"&&id()}function xy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return vr(Ga)}function Ay(e,t){if(e==="click")return vr(t)}function Ty(e,t){if(e==="input"||e==="change")return vr(t)}function Cy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:Cy;function Xa(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var s=n[a];if(!tr.call(t,s)||!Ct(e[s],t[s]))return!1}return!0}function ld(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function rd(e,t){var n=ld(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ld(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function od(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=cr(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=cr(e.document)}return t}function _o(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Oy=pn&&"documentMode"in document&&11>=document.documentMode,Gi=null,Eo=null,Ka=null,wo=!1;function ud(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;wo||Gi==null||Gi!==cr(a)||(a=Gi,"selectionStart"in a&&_o(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Ka&&Xa(Ka,a)||(Ka=a,a=ns(Eo,"onSelect"),0<a.length&&(t=new gr("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=Gi)))}function vi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xi={animationend:vi("Animation","AnimationEnd"),animationiteration:vi("Animation","AnimationIteration"),animationstart:vi("Animation","AnimationStart"),transitionrun:vi("Transition","TransitionRun"),transitionstart:vi("Transition","TransitionStart"),transitioncancel:vi("Transition","TransitionCancel"),transitionend:vi("Transition","TransitionEnd")},xo={},cd={};pn&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete Xi.animationend.animation,delete Xi.animationiteration.animation,delete Xi.animationstart.animation),"TransitionEvent"in window||delete Xi.transitionend.transition);function yi(e){if(xo[e])return xo[e];if(!Xi[e])return e;var t=Xi[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in cd)return xo[e]=t[n];return e}var fd=yi("animationend"),dd=yi("animationiteration"),hd=yi("animationstart"),Ry=yi("transitionrun"),ky=yi("transitionstart"),My=yi("transitioncancel"),md=yi("transitionend"),gd=new Map,Ao="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ao.push("scrollEnd");function Kt(e,t){gd.set(e,t),gi(t,[e])}var pd=new WeakMap;function qt(e,t){if(typeof e=="object"&&e!==null){var n=pd.get(e);return n!==void 0?n:(t={value:e,source:t,stack:jf(t)},pd.set(e,t),t)}return{value:e,source:t,stack:jf(t)}}var Bt=[],Ki=0,To=0;function yr(){for(var e=Ki,t=To=Ki=0;t<e;){var n=Bt[t];Bt[t++]=null;var a=Bt[t];Bt[t++]=null;var s=Bt[t];Bt[t++]=null;var c=Bt[t];if(Bt[t++]=null,a!==null&&s!==null){var h=a.pending;h===null?s.next=s:(s.next=h.next,h.next=s),a.pending=s}c!==0&&vd(n,s,c)}}function br(e,t,n,a){Bt[Ki++]=e,Bt[Ki++]=t,Bt[Ki++]=n,Bt[Ki++]=a,To|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Co(e,t,n,a){return br(e,t,n,a),Sr(e)}function Wi(e,t){return br(e,null,null,t),Sr(e)}function vd(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var s=!1,c=e.return;c!==null;)c.childLanes|=n,a=c.alternate,a!==null&&(a.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(s=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,s&&t!==null&&(s=31-lt(n),e=c.hiddenUpdates,a=e[s],a===null?e[s]=[t]:a.push(t),t.lane=n|536870912),c):null}function Sr(e){if(50<Sl)throw Sl=0,Uu=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Zi={};function jy(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,n,a){return new jy(e,t,n,a)}function Oo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function vn(e,t){var n=e.alternate;return n===null?(n=Ot(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function yd(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function _r(e,t,n,a,s,c){var h=0;if(a=e,typeof e=="function")Oo(e)&&(h=1);else if(typeof e=="string")h=Db(e,n,J.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case se:return e=Ot(31,n,t,s),e.elementType=se,e.lanes=c,e;case w:return bi(n.children,s,c,t);case D:h=8,s|=24;break;case L:return e=Ot(12,n,t,s|2),e.elementType=L,e.lanes=c,e;case P:return e=Ot(13,n,t,s),e.elementType=P,e.lanes=c,e;case Z:return e=Ot(19,n,t,s),e.elementType=Z,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case T:case Y:h=10;break e;case H:h=9;break e;case F:h=11;break e;case re:h=14;break e;case X:h=16,a=null;break e}h=29,n=Error(o(130,e===null?"null":typeof e,"")),a=null}return t=Ot(h,n,t,s),t.elementType=e,t.type=a,t.lanes=c,t}function bi(e,t,n,a){return e=Ot(7,e,a,t),e.lanes=n,e}function Ro(e,t,n){return e=Ot(6,e,null,t),e.lanes=n,e}function ko(e,t,n){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Fi=[],Ji=0,Er=null,wr=0,Ht=[],Vt=0,Si=null,yn=1,bn="";function _i(e,t){Fi[Ji++]=wr,Fi[Ji++]=Er,Er=e,wr=t}function bd(e,t,n){Ht[Vt++]=yn,Ht[Vt++]=bn,Ht[Vt++]=Si,Si=e;var a=yn;e=bn;var s=32-lt(a)-1;a&=~(1<<s),n+=1;var c=32-lt(t)+s;if(30<c){var h=s-s%5;c=(a&(1<<h)-1).toString(32),a>>=h,s-=h,yn=1<<32-lt(t)+s|n<<s|a,bn=c+e}else yn=1<<c|n<<s|a,bn=e}function Mo(e){e.return!==null&&(_i(e,1),bd(e,1,0))}function jo(e){for(;e===Er;)Er=Fi[--Ji],Fi[Ji]=null,wr=Fi[--Ji],Fi[Ji]=null;for(;e===Si;)Si=Ht[--Vt],Ht[Vt]=null,bn=Ht[--Vt],Ht[Vt]=null,yn=Ht[--Vt],Ht[Vt]=null}var pt=null,Ve=null,Se=!1,Ei=null,ln=!1,Uo=Error(o(519));function wi(e){var t=Error(o(418,""));throw Fa(qt(t,e)),Uo}function Sd(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[ct]=e,t[_t]=a,n){case"dialog":me("cancel",t),me("close",t);break;case"iframe":case"object":case"embed":me("load",t);break;case"video":case"audio":for(n=0;n<El.length;n++)me(El[n],t);break;case"source":me("error",t);break;case"img":case"image":case"link":me("error",t),me("load",t);break;case"details":me("toggle",t);break;case"input":me("invalid",t),Lf(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),ur(t);break;case"select":me("invalid",t);break;case"textarea":me("invalid",t),Nf(t,a.value,a.defaultValue,a.children),ur(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Nm(t.textContent,n)?(a.popover!=null&&(me("beforetoggle",t),me("toggle",t)),a.onScroll!=null&&me("scroll",t),a.onScrollEnd!=null&&me("scrollend",t),a.onClick!=null&&(t.onclick=is),t=!0):t=!1,t||wi(e)}function _d(e){for(pt=e.return;pt;)switch(pt.tag){case 5:case 13:ln=!1;return;case 27:case 3:ln=!0;return;default:pt=pt.return}}function Wa(e){if(e!==pt)return!1;if(!Se)return _d(e),Se=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Ku(e.type,e.memoizedProps)),n=!n),n&&Ve&&wi(e),_d(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ve=Zt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ve=null}}else t===27?(t=Ve,Jn(e.type)?(e=Ju,Ju=null,Ve=e):Ve=t):Ve=pt?Zt(e.stateNode.nextSibling):null;return!0}function Za(){Ve=pt=null,Se=!1}function Ed(){var e=Ei;return e!==null&&(At===null?At=e:At.push.apply(At,e),Ei=null),e}function Fa(e){Ei===null?Ei=[e]:Ei.push(e)}var Do=Q(null),xi=null,Sn=null;function qn(e,t,n){G(Do,t._currentValue),t._currentValue=n}function _n(e){e._currentValue=Do.current,$(Do)}function Lo(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function zo(e,t,n,a){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var c=s.dependencies;if(c!==null){var h=s.child;c=c.firstContext;e:for(;c!==null;){var p=c;c=s;for(var y=0;y<t.length;y++)if(p.context===t[y]){c.lanes|=n,p=c.alternate,p!==null&&(p.lanes|=n),Lo(c.return,n,e),a||(h=null);break e}c=p.next}}else if(s.tag===18){if(h=s.return,h===null)throw Error(o(341));h.lanes|=n,c=h.alternate,c!==null&&(c.lanes|=n),Lo(h,n,e),h=null}else h=s.child;if(h!==null)h.return=s;else for(h=s;h!==null;){if(h===e){h=null;break}if(s=h.sibling,s!==null){s.return=h.return,h=s;break}h=h.return}s=h}}function Ja(e,t,n,a){e=null;for(var s=t,c=!1;s!==null;){if(!c){if((s.flags&524288)!==0)c=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var h=s.alternate;if(h===null)throw Error(o(387));if(h=h.memoizedProps,h!==null){var p=s.type;Ct(s.pendingProps.value,h.value)||(e!==null?e.push(p):e=[p])}}else if(s===ut.current){if(h=s.alternate,h===null)throw Error(o(387));h.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Ol):e=[Ol])}s=s.return}e!==null&&zo(t,e,n,a),t.flags|=262144}function xr(e){for(e=e.firstContext;e!==null;){if(!Ct(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ai(e){xi=e,Sn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return wd(xi,e)}function Ar(e,t){return xi===null&&Ai(e),wd(e,t)}function wd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Sn===null){if(e===null)throw Error(o(308));Sn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Sn=Sn.next=t;return n}var Uy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Dy=i.unstable_scheduleCallback,Ly=i.unstable_NormalPriority,Ke={$$typeof:Y,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function No(){return{controller:new Uy,data:new Map,refCount:0}}function el(e){e.refCount--,e.refCount===0&&Dy(Ly,function(){e.controller.abort()})}var tl=null,qo=0,ea=0,ta=null;function zy(e,t){if(tl===null){var n=tl=[];qo=0,ea=Hu(),ta={status:"pending",value:void 0,then:function(a){n.push(a)}}}return qo++,t.then(xd,xd),t}function xd(){if(--qo===0&&tl!==null){ta!==null&&(ta.status="fulfilled");var e=tl;tl=null,ea=0,ta=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Ny(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var s=0;s<n.length;s++)(0,n[s])(t)},function(s){for(a.status="rejected",a.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),a}var Ad=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&zy(e,t),Ad!==null&&Ad(e,t)};var Ti=Q(null);function Bo(){var e=Ti.current;return e!==null?e:je.pooledCache}function Tr(e,t){t===null?G(Ti,Ti.current):G(Ti,t.pool)}function Td(){var e=Bo();return e===null?null:{parent:Ke._currentValue,pool:e}}var nl=Error(o(460)),Cd=Error(o(474)),Cr=Error(o(542)),Ho={then:function(){}};function Od(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Or(){}function Rd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Or,Or),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Md(e),e;default:if(typeof t.status=="string")t.then(Or,Or);else{if(e=je,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=a}},function(a){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Md(e),e}throw il=t,nl}}var il=null;function kd(){if(il===null)throw Error(o(459));var e=il;return il=null,e}function Md(e){if(e===nl||e===Cr)throw Error(o(483))}var Bn=!1;function Vo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Qo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Hn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Vn(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(we&2)!==0){var s=a.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),a.pending=t,t=Sr(e),vd(e,null,n),t}return br(e,a,t,n),Sr(e)}function al(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,xf(e,n)}}function Po(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var s=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?s=c=h:c=c.next=h,n=n.next}while(n!==null);c===null?s=c=t:c=c.next=t}else s=c=t;n={baseState:a.baseState,firstBaseUpdate:s,lastBaseUpdate:c,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Yo=!1;function ll(){if(Yo){var e=ta;if(e!==null)throw e}}function rl(e,t,n,a){Yo=!1;var s=e.updateQueue;Bn=!1;var c=s.firstBaseUpdate,h=s.lastBaseUpdate,p=s.shared.pending;if(p!==null){s.shared.pending=null;var y=p,k=y.next;y.next=null,h===null?c=k:h.next=k,h=y;var N=e.alternate;N!==null&&(N=N.updateQueue,p=N.lastBaseUpdate,p!==h&&(p===null?N.firstBaseUpdate=k:p.next=k,N.lastBaseUpdate=y))}if(c!==null){var V=s.baseState;h=0,N=k=y=null,p=c;do{var j=p.lane&-536870913,U=j!==p.lane;if(U?(pe&j)===j:(a&j)===j){j!==0&&j===ea&&(Yo=!0),N!==null&&(N=N.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var le=e,te=p;j=t;var Ce=n;switch(te.tag){case 1:if(le=te.payload,typeof le=="function"){V=le.call(Ce,V,j);break e}V=le;break e;case 3:le.flags=le.flags&-65537|128;case 0:if(le=te.payload,j=typeof le=="function"?le.call(Ce,V,j):le,j==null)break e;V=b({},V,j);break e;case 2:Bn=!0}}j=p.callback,j!==null&&(e.flags|=64,U&&(e.flags|=8192),U=s.callbacks,U===null?s.callbacks=[j]:U.push(j))}else U={lane:j,tag:p.tag,payload:p.payload,callback:p.callback,next:null},N===null?(k=N=U,y=V):N=N.next=U,h|=j;if(p=p.next,p===null){if(p=s.shared.pending,p===null)break;U=p,p=U.next,U.next=null,s.lastBaseUpdate=U,s.shared.pending=null}}while(!0);N===null&&(y=V),s.baseState=y,s.firstBaseUpdate=k,s.lastBaseUpdate=N,c===null&&(s.shared.lanes=0),Kn|=h,e.lanes=h,e.memoizedState=V}}function jd(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Ud(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)jd(n[e],t)}var na=Q(null),Rr=Q(0);function Dd(e,t){e=On,G(Rr,e),G(na,t),On=e|t.baseLanes}function Io(){G(Rr,On),G(na,na.current)}function $o(){On=Rr.current,$(na),$(Rr)}var Qn=0,ce=null,Ae=null,Ge=null,kr=!1,ia=!1,Ci=!1,Mr=0,sl=0,aa=null,qy=0;function Ye(){throw Error(o(321))}function Go(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ct(e[n],t[n]))return!1;return!0}function Xo(e,t,n,a,s,c){return Qn=c,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?vh:yh,Ci=!1,c=n(a,s),Ci=!1,ia&&(c=zd(t,n,a,s)),Ld(e),c}function Ld(e){z.H=Nr;var t=Ae!==null&&Ae.next!==null;if(Qn=0,Ge=Ae=ce=null,kr=!1,sl=0,aa=null,t)throw Error(o(300));e===null||tt||(e=e.dependencies,e!==null&&xr(e)&&(tt=!0))}function zd(e,t,n,a){ce=e;var s=0;do{if(ia&&(aa=null),sl=0,ia=!1,25<=s)throw Error(o(301));if(s+=1,Ge=Ae=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}z.H=Iy,c=t(n,a)}while(ia);return c}function By(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?ol(t):t,e=e.useState()[0],(Ae!==null?Ae.memoizedState:null)!==e&&(ce.flags|=1024),t}function Ko(){var e=Mr!==0;return Mr=0,e}function Wo(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Zo(e){if(kr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}kr=!1}Qn=0,Ge=Ae=ce=null,ia=!1,sl=Mr=0,aa=null}function wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ce.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Xe(){if(Ae===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Ae.next;var t=Ge===null?ce.memoizedState:Ge.next;if(t!==null)Ge=t,Ae=e;else{if(e===null)throw ce.alternate===null?Error(o(467)):Error(o(310));Ae=e,e={memoizedState:Ae.memoizedState,baseState:Ae.baseState,baseQueue:Ae.baseQueue,queue:Ae.queue,next:null},Ge===null?ce.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Fo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ol(e){var t=sl;return sl+=1,aa===null&&(aa=[]),e=Rd(aa,e,t),t=ce,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?vh:yh),e}function jr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ol(e);if(e.$$typeof===Y)return ft(e)}throw Error(o(438,String(e)))}function Jo(e){var t=null,n=ce.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=ce.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Fo(),ce.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=ie;return t.index++,n}function En(e,t){return typeof t=="function"?t(e):t}function Ur(e){var t=Xe();return eu(t,Ae,e)}function eu(e,t,n){var a=e.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=n;var s=e.baseQueue,c=a.pending;if(c!==null){if(s!==null){var h=s.next;s.next=c.next,c.next=h}t.baseQueue=s=c,a.pending=null}if(c=e.baseState,s===null)e.memoizedState=c;else{t=s.next;var p=h=null,y=null,k=t,N=!1;do{var V=k.lane&-536870913;if(V!==k.lane?(pe&V)===V:(Qn&V)===V){var j=k.revertLane;if(j===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null}),V===ea&&(N=!0);else if((Qn&j)===j){k=k.next,j===ea&&(N=!0);continue}else V={lane:0,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},y===null?(p=y=V,h=c):y=y.next=V,ce.lanes|=j,Kn|=j;V=k.action,Ci&&n(c,V),c=k.hasEagerState?k.eagerState:n(c,V)}else j={lane:V,revertLane:k.revertLane,action:k.action,hasEagerState:k.hasEagerState,eagerState:k.eagerState,next:null},y===null?(p=y=j,h=c):y=y.next=j,ce.lanes|=V,Kn|=V;k=k.next}while(k!==null&&k!==t);if(y===null?h=c:y.next=p,!Ct(c,e.memoizedState)&&(tt=!0,N&&(n=ta,n!==null)))throw n;e.memoizedState=c,e.baseState=h,e.baseQueue=y,a.lastRenderedState=c}return s===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function tu(e){var t=Xe(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var a=n.dispatch,s=n.pending,c=t.memoizedState;if(s!==null){n.pending=null;var h=s=s.next;do c=e(c,h.action),h=h.next;while(h!==s);Ct(c,t.memoizedState)||(tt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,a]}function Nd(e,t,n){var a=ce,s=Xe(),c=Se;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=t();var h=!Ct((Ae||s).memoizedState,n);h&&(s.memoizedState=n,tt=!0),s=s.queue;var p=Hd.bind(null,a,s,e);if(ul(2048,8,p,[e]),s.getSnapshot!==t||h||Ge!==null&&Ge.memoizedState.tag&1){if(a.flags|=2048,la(9,Dr(),Bd.bind(null,a,s,n,t),null),je===null)throw Error(o(349));c||(Qn&124)!==0||qd(a,t,n)}return n}function qd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t=Fo(),ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Bd(e,t,n,a){t.value=n,t.getSnapshot=a,Vd(t)&&Qd(e)}function Hd(e,t,n){return n(function(){Vd(t)&&Qd(e)})}function Vd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ct(e,n)}catch{return!0}}function Qd(e){var t=Wi(e,2);t!==null&&Ut(t,e,2)}function nu(e){var t=wt();if(typeof e=="function"){var n=e;if(e=n(),Ci){an(!0);try{n()}finally{an(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:e},t}function Pd(e,t,n,a){return e.baseState=n,eu(e,Ae,typeof a=="function"?a:En)}function Hy(e,t,n,a,s){if(zr(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){c.listeners.push(h)}};z.T!==null?n(!0):c.isTransition=!1,a(c),n=t.pending,n===null?(c.next=t.pending=c,Yd(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Yd(e,t){var n=t.action,a=t.payload,s=e.state;if(t.isTransition){var c=z.T,h={};z.T=h;try{var p=n(s,a),y=z.S;y!==null&&y(h,p),Id(e,t,p)}catch(k){iu(e,t,k)}finally{z.T=c}}else try{c=n(s,a),Id(e,t,c)}catch(k){iu(e,t,k)}}function Id(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){$d(e,t,a)},function(a){return iu(e,t,a)}):$d(e,t,n)}function $d(e,t,n){t.status="fulfilled",t.value=n,Gd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Yd(e,n)))}function iu(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,Gd(t),t=t.next;while(t!==a)}e.action=null}function Gd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Xd(e,t){return t}function Kd(e,t){if(Se){var n=je.formState;if(n!==null){e:{var a=ce;if(Se){if(Ve){t:{for(var s=Ve,c=ln;s.nodeType!==8;){if(!c){s=null;break t}if(s=Zt(s.nextSibling),s===null){s=null;break t}}c=s.data,s=c==="F!"||c==="F"?s:null}if(s){Ve=Zt(s.nextSibling),a=s.data==="F!";break e}}wi(a)}a=!1}a&&(t=n[0])}}return n=wt(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xd,lastRenderedState:t},n.queue=a,n=mh.bind(null,ce,a),a.dispatch=n,a=nu(!1),c=ou.bind(null,ce,!1,a.queue),a=wt(),s={state:t,dispatch:null,action:e,pending:null},a.queue=s,n=Hy.bind(null,ce,s,c,n),s.dispatch=n,a.memoizedState=e,[t,n,!1]}function Wd(e){var t=Xe();return Zd(t,Ae,e)}function Zd(e,t,n){if(t=eu(e,t,Xd)[0],e=Ur(En)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=ol(t)}catch(h){throw h===nl?Cr:h}else a=t;t=Xe();var s=t.queue,c=s.dispatch;return n!==t.memoizedState&&(ce.flags|=2048,la(9,Dr(),Vy.bind(null,s,n),null)),[a,c,e]}function Vy(e,t){e.action=t}function Fd(e){var t=Xe(),n=Ae;if(n!==null)return Zd(t,n,e);Xe(),t=t.memoizedState,n=Xe();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function la(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=ce.updateQueue,t===null&&(t=Fo(),ce.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Dr(){return{destroy:void 0,resource:void 0}}function Jd(){return Xe().memoizedState}function Lr(e,t,n,a){var s=wt();a=a===void 0?null:a,ce.flags|=e,s.memoizedState=la(1|t,Dr(),n,a)}function ul(e,t,n,a){var s=Xe();a=a===void 0?null:a;var c=s.memoizedState.inst;Ae!==null&&a!==null&&Go(a,Ae.memoizedState.deps)?s.memoizedState=la(t,c,n,a):(ce.flags|=e,s.memoizedState=la(1|t,c,n,a))}function eh(e,t){Lr(8390656,8,e,t)}function th(e,t){ul(2048,8,e,t)}function nh(e,t){return ul(4,2,e,t)}function ih(e,t){return ul(4,4,e,t)}function ah(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function lh(e,t,n){n=n!=null?n.concat([e]):null,ul(4,4,ah.bind(null,t,e),n)}function au(){}function rh(e,t){var n=Xe();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Go(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function sh(e,t){var n=Xe();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Go(t,a[1]))return a[0];if(a=e(),Ci){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[a,t],a}function lu(e,t,n){return n===void 0||(Qn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=cm(),ce.lanes|=e,Kn|=e,n)}function oh(e,t,n,a){return Ct(n,t)?n:na.current!==null?(e=lu(e,n,a),Ct(e,t)||(tt=!0),e):(Qn&42)===0?(tt=!0,e.memoizedState=n):(e=cm(),ce.lanes|=e,Kn|=e,t)}function uh(e,t,n,a,s){var c=I.p;I.p=c!==0&&8>c?c:8;var h=z.T,p={};z.T=p,ou(e,!1,t,n);try{var y=s(),k=z.S;if(k!==null&&k(p,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var N=Ny(y,a);cl(e,t,N,jt(e))}else cl(e,t,a,jt(e))}catch(V){cl(e,t,{then:function(){},status:"rejected",reason:V},jt())}finally{I.p=c,z.T=h}}function Qy(){}function ru(e,t,n,a){if(e.tag!==5)throw Error(o(476));var s=ch(e).queue;uh(e,s,t,W,n===null?Qy:function(){return fh(e),n(a)})}function ch(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:W,baseState:W,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:W},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:En,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function fh(e){var t=ch(e).next.queue;cl(e,t,{},jt())}function su(){return ft(Ol)}function dh(){return Xe().memoizedState}function hh(){return Xe().memoizedState}function Py(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=jt();e=Hn(n);var a=Vn(t,e,n);a!==null&&(Ut(a,t,n),al(a,t,n)),t={cache:No()},e.payload=t;return}t=t.return}}function Yy(e,t,n){var a=jt();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},zr(e)?gh(t,n):(n=Co(e,t,n,a),n!==null&&(Ut(n,e,a),ph(n,t,a)))}function mh(e,t,n){var a=jt();cl(e,t,n,a)}function cl(e,t,n,a){var s={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(zr(e))gh(t,s);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var h=t.lastRenderedState,p=c(h,n);if(s.hasEagerState=!0,s.eagerState=p,Ct(p,h))return br(e,t,s,0),je===null&&yr(),!1}catch{}finally{}if(n=Co(e,t,s,a),n!==null)return Ut(n,e,a),ph(n,t,a),!0}return!1}function ou(e,t,n,a){if(a={lane:2,revertLane:Hu(),action:a,hasEagerState:!1,eagerState:null,next:null},zr(e)){if(t)throw Error(o(479))}else t=Co(e,n,a,2),t!==null&&Ut(t,e,2)}function zr(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function gh(e,t){ia=kr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ph(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,xf(e,n)}}var Nr={readContext:ft,use:jr,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useLayoutEffect:Ye,useInsertionEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useSyncExternalStore:Ye,useId:Ye,useHostTransitionStatus:Ye,useFormState:Ye,useActionState:Ye,useOptimistic:Ye,useMemoCache:Ye,useCacheRefresh:Ye},vh={readContext:ft,use:jr,useCallback:function(e,t){return wt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:eh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Lr(4194308,4,ah.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Lr(4194308,4,e,t)},useInsertionEffect:function(e,t){Lr(4,2,e,t)},useMemo:function(e,t){var n=wt();t=t===void 0?null:t;var a=e();if(Ci){an(!0);try{e()}finally{an(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=wt();if(n!==void 0){var s=n(t);if(Ci){an(!0);try{n(t)}finally{an(!1)}}}else s=t;return a.memoizedState=a.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},a.queue=e,e=e.dispatch=Yy.bind(null,ce,e),[a.memoizedState,e]},useRef:function(e){var t=wt();return e={current:e},t.memoizedState=e},useState:function(e){e=nu(e);var t=e.queue,n=mh.bind(null,ce,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:au,useDeferredValue:function(e,t){var n=wt();return lu(n,e,t)},useTransition:function(){var e=nu(!1);return e=uh.bind(null,ce,e.queue,!0,!1),wt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=ce,s=wt();if(Se){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),je===null)throw Error(o(349));(pe&124)!==0||qd(a,t,n)}s.memoizedState=n;var c={value:n,getSnapshot:t};return s.queue=c,eh(Hd.bind(null,a,c,e),[e]),a.flags|=2048,la(9,Dr(),Bd.bind(null,a,c,n,t),null),n},useId:function(){var e=wt(),t=je.identifierPrefix;if(Se){var n=bn,a=yn;n=(a&~(1<<32-lt(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=Mr++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=qy++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:su,useFormState:Kd,useActionState:Kd,useOptimistic:function(e){var t=wt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=ou.bind(null,ce,!0,n),n.dispatch=t,[e,t]},useMemoCache:Jo,useCacheRefresh:function(){return wt().memoizedState=Py.bind(null,ce)}},yh={readContext:ft,use:jr,useCallback:rh,useContext:ft,useEffect:th,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ih,useMemo:sh,useReducer:Ur,useRef:Jd,useState:function(){return Ur(En)},useDebugValue:au,useDeferredValue:function(e,t){var n=Xe();return oh(n,Ae.memoizedState,e,t)},useTransition:function(){var e=Ur(En)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:Nd,useId:dh,useHostTransitionStatus:su,useFormState:Wd,useActionState:Wd,useOptimistic:function(e,t){var n=Xe();return Pd(n,Ae,e,t)},useMemoCache:Jo,useCacheRefresh:hh},Iy={readContext:ft,use:jr,useCallback:rh,useContext:ft,useEffect:th,useImperativeHandle:lh,useInsertionEffect:nh,useLayoutEffect:ih,useMemo:sh,useReducer:tu,useRef:Jd,useState:function(){return tu(En)},useDebugValue:au,useDeferredValue:function(e,t){var n=Xe();return Ae===null?lu(n,e,t):oh(n,Ae.memoizedState,e,t)},useTransition:function(){var e=tu(En)[0],t=Xe().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:Nd,useId:dh,useHostTransitionStatus:su,useFormState:Fd,useActionState:Fd,useOptimistic:function(e,t){var n=Xe();return Ae!==null?Pd(n,Ae,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Jo,useCacheRefresh:hh},ra=null,fl=0;function qr(e){var t=fl;return fl+=1,ra===null&&(ra=[]),Rd(ra,e,t)}function dl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Br(e,t){throw t.$$typeof===C?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function bh(e){var t=e._init;return t(e._payload)}function Sh(e){function t(A,E){if(e){var R=A.deletions;R===null?(A.deletions=[E],A.flags|=16):R.push(E)}}function n(A,E){if(!e)return null;for(;E!==null;)t(A,E),E=E.sibling;return null}function a(A){for(var E=new Map;A!==null;)A.key!==null?E.set(A.key,A):E.set(A.index,A),A=A.sibling;return E}function s(A,E){return A=vn(A,E),A.index=0,A.sibling=null,A}function c(A,E,R){return A.index=R,e?(R=A.alternate,R!==null?(R=R.index,R<E?(A.flags|=67108866,E):R):(A.flags|=67108866,E)):(A.flags|=1048576,E)}function h(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function p(A,E,R,B){return E===null||E.tag!==6?(E=Ro(R,A.mode,B),E.return=A,E):(E=s(E,R),E.return=A,E)}function y(A,E,R,B){var K=R.type;return K===w?N(A,E,R.props.children,B,R.key):E!==null&&(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===X&&bh(K)===E.type)?(E=s(E,R.props),dl(E,R),E.return=A,E):(E=_r(R.type,R.key,R.props,null,A.mode,B),dl(E,R),E.return=A,E)}function k(A,E,R,B){return E===null||E.tag!==4||E.stateNode.containerInfo!==R.containerInfo||E.stateNode.implementation!==R.implementation?(E=ko(R,A.mode,B),E.return=A,E):(E=s(E,R.children||[]),E.return=A,E)}function N(A,E,R,B,K){return E===null||E.tag!==7?(E=bi(R,A.mode,B,K),E.return=A,E):(E=s(E,R),E.return=A,E)}function V(A,E,R){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Ro(""+E,A.mode,R),E.return=A,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case S:return R=_r(E.type,E.key,E.props,null,A.mode,R),dl(R,E),R.return=A,R;case O:return E=ko(E,A.mode,R),E.return=A,E;case X:var B=E._init;return E=B(E._payload),V(A,E,R)}if(Oe(E)||ve(E))return E=bi(E,A.mode,R,null),E.return=A,E;if(typeof E.then=="function")return V(A,qr(E),R);if(E.$$typeof===Y)return V(A,Ar(A,E),R);Br(A,E)}return null}function j(A,E,R,B){var K=E!==null?E.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return K!==null?null:p(A,E,""+R,B);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case S:return R.key===K?y(A,E,R,B):null;case O:return R.key===K?k(A,E,R,B):null;case X:return K=R._init,R=K(R._payload),j(A,E,R,B)}if(Oe(R)||ve(R))return K!==null?null:N(A,E,R,B,null);if(typeof R.then=="function")return j(A,E,qr(R),B);if(R.$$typeof===Y)return j(A,E,Ar(A,R),B);Br(A,R)}return null}function U(A,E,R,B,K){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return A=A.get(R)||null,p(E,A,""+B,K);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case S:return A=A.get(B.key===null?R:B.key)||null,y(E,A,B,K);case O:return A=A.get(B.key===null?R:B.key)||null,k(E,A,B,K);case X:var de=B._init;return B=de(B._payload),U(A,E,R,B,K)}if(Oe(B)||ve(B))return A=A.get(R)||null,N(E,A,B,K,null);if(typeof B.then=="function")return U(A,E,R,qr(B),K);if(B.$$typeof===Y)return U(A,E,R,Ar(E,B),K);Br(E,B)}return null}function le(A,E,R,B){for(var K=null,de=null,ee=E,ne=E=0,it=null;ee!==null&&ne<R.length;ne++){ee.index>ne?(it=ee,ee=null):it=ee.sibling;var ye=j(A,ee,R[ne],B);if(ye===null){ee===null&&(ee=it);break}e&&ee&&ye.alternate===null&&t(A,ee),E=c(ye,E,ne),de===null?K=ye:de.sibling=ye,de=ye,ee=it}if(ne===R.length)return n(A,ee),Se&&_i(A,ne),K;if(ee===null){for(;ne<R.length;ne++)ee=V(A,R[ne],B),ee!==null&&(E=c(ee,E,ne),de===null?K=ee:de.sibling=ee,de=ee);return Se&&_i(A,ne),K}for(ee=a(ee);ne<R.length;ne++)it=U(ee,A,ne,R[ne],B),it!==null&&(e&&it.alternate!==null&&ee.delete(it.key===null?ne:it.key),E=c(it,E,ne),de===null?K=it:de.sibling=it,de=it);return e&&ee.forEach(function(ai){return t(A,ai)}),Se&&_i(A,ne),K}function te(A,E,R,B){if(R==null)throw Error(o(151));for(var K=null,de=null,ee=E,ne=E=0,it=null,ye=R.next();ee!==null&&!ye.done;ne++,ye=R.next()){ee.index>ne?(it=ee,ee=null):it=ee.sibling;var ai=j(A,ee,ye.value,B);if(ai===null){ee===null&&(ee=it);break}e&&ee&&ai.alternate===null&&t(A,ee),E=c(ai,E,ne),de===null?K=ai:de.sibling=ai,de=ai,ee=it}if(ye.done)return n(A,ee),Se&&_i(A,ne),K;if(ee===null){for(;!ye.done;ne++,ye=R.next())ye=V(A,ye.value,B),ye!==null&&(E=c(ye,E,ne),de===null?K=ye:de.sibling=ye,de=ye);return Se&&_i(A,ne),K}for(ee=a(ee);!ye.done;ne++,ye=R.next())ye=U(ee,A,ne,ye.value,B),ye!==null&&(e&&ye.alternate!==null&&ee.delete(ye.key===null?ne:ye.key),E=c(ye,E,ne),de===null?K=ye:de.sibling=ye,de=ye);return e&&ee.forEach(function($b){return t(A,$b)}),Se&&_i(A,ne),K}function Ce(A,E,R,B){if(typeof R=="object"&&R!==null&&R.type===w&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case S:e:{for(var K=R.key;E!==null;){if(E.key===K){if(K=R.type,K===w){if(E.tag===7){n(A,E.sibling),B=s(E,R.props.children),B.return=A,A=B;break e}}else if(E.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===X&&bh(K)===E.type){n(A,E.sibling),B=s(E,R.props),dl(B,R),B.return=A,A=B;break e}n(A,E);break}else t(A,E);E=E.sibling}R.type===w?(B=bi(R.props.children,A.mode,B,R.key),B.return=A,A=B):(B=_r(R.type,R.key,R.props,null,A.mode,B),dl(B,R),B.return=A,A=B)}return h(A);case O:e:{for(K=R.key;E!==null;){if(E.key===K)if(E.tag===4&&E.stateNode.containerInfo===R.containerInfo&&E.stateNode.implementation===R.implementation){n(A,E.sibling),B=s(E,R.children||[]),B.return=A,A=B;break e}else{n(A,E);break}else t(A,E);E=E.sibling}B=ko(R,A.mode,B),B.return=A,A=B}return h(A);case X:return K=R._init,R=K(R._payload),Ce(A,E,R,B)}if(Oe(R))return le(A,E,R,B);if(ve(R)){if(K=ve(R),typeof K!="function")throw Error(o(150));return R=K.call(R),te(A,E,R,B)}if(typeof R.then=="function")return Ce(A,E,qr(R),B);if(R.$$typeof===Y)return Ce(A,E,Ar(A,R),B);Br(A,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,E!==null&&E.tag===6?(n(A,E.sibling),B=s(E,R),B.return=A,A=B):(n(A,E),B=Ro(R,A.mode,B),B.return=A,A=B),h(A)):n(A,E)}return function(A,E,R,B){try{fl=0;var K=Ce(A,E,R,B);return ra=null,K}catch(ee){if(ee===nl||ee===Cr)throw ee;var de=Ot(29,ee,null,A.mode);return de.lanes=B,de.return=A,de}finally{}}}var sa=Sh(!0),_h=Sh(!1),Qt=Q(null),rn=null;function Pn(e){var t=e.alternate;G(We,We.current&1),G(Qt,e),rn===null&&(t===null||na.current!==null||t.memoizedState!==null)&&(rn=e)}function Eh(e){if(e.tag===22){if(G(We,We.current),G(Qt,e),rn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(rn=e)}}else Yn()}function Yn(){G(We,We.current),G(Qt,Qt.current)}function wn(e){$(Qt),rn===e&&(rn=null),$(We)}var We=Q(0);function Hr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Fu(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function uu(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:b({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var cu={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=jt(),s=Hn(a);s.payload=t,n!=null&&(s.callback=n),t=Vn(e,s,a),t!==null&&(Ut(t,e,a),al(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=jt(),s=Hn(a);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Vn(e,s,a),t!==null&&(Ut(t,e,a),al(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=jt(),a=Hn(n);a.tag=2,t!=null&&(a.callback=t),t=Vn(e,a,n),t!==null&&(Ut(t,e,n),al(t,e,n))}};function wh(e,t,n,a,s,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,h):t.prototype&&t.prototype.isPureReactComponent?!Xa(n,a)||!Xa(s,c):!0}function xh(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&cu.enqueueReplaceState(t,t.state,null)}function Oi(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=b({},n));for(var s in e)n[s]===void 0&&(n[s]=e[s])}return n}var Vr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ah(e){Vr(e)}function Th(e){console.error(e)}function Ch(e){Vr(e)}function Qr(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Oh(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function fu(e,t,n){return n=Hn(n),n.tag=3,n.payload={element:null},n.callback=function(){Qr(e,t)},n}function Rh(e){return e=Hn(e),e.tag=3,e}function kh(e,t,n,a){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var c=a.value;e.payload=function(){return s(c)},e.callback=function(){Oh(t,n,a)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Oh(t,n,a),typeof s!="function"&&(Wn===null?Wn=new Set([this]):Wn.add(this));var p=a.stack;this.componentDidCatch(a.value,{componentStack:p!==null?p:""})})}function $y(e,t,n,a,s){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&Ja(t,n,s,!0),n=Qt.current,n!==null){switch(n.tag){case 13:return rn===null?Lu():n.alternate===null&&Qe===0&&(Qe=3),n.flags&=-257,n.flags|=65536,n.lanes=s,a===Ho?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),Nu(e,a,s)),!1;case 22:return n.flags|=65536,a===Ho?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),Nu(e,a,s)),!1}throw Error(o(435,n.tag))}return Nu(e,a,s),Lu(),!1}if(Se)return t=Qt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,a!==Uo&&(e=Error(o(422),{cause:a}),Fa(qt(e,n)))):(a!==Uo&&(t=Error(o(423),{cause:a}),Fa(qt(t,n))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,a=qt(a,n),s=fu(e.stateNode,a,s),Po(e,s),Qe!==4&&(Qe=2)),!1;var c=Error(o(520),{cause:a});if(c=qt(c,n),bl===null?bl=[c]:bl.push(c),Qe!==4&&(Qe=2),t===null)return!0;a=qt(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=s&-s,n.lanes|=e,e=fu(n.stateNode,a,e),Po(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Wn===null||!Wn.has(c))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Rh(s),kh(s,e,n,a),Po(n,s),!1}n=n.return}while(n!==null);return!1}var Mh=Error(o(461)),tt=!1;function rt(e,t,n,a){t.child=e===null?_h(t,null,n,a):sa(t,e.child,n,a)}function jh(e,t,n,a,s){n=n.render;var c=t.ref;if("ref"in a){var h={};for(var p in a)p!=="ref"&&(h[p]=a[p])}else h=a;return Ai(t),a=Xo(e,t,n,h,c,s),p=Ko(),e!==null&&!tt?(Wo(e,t,s),xn(e,t,s)):(Se&&p&&Mo(t),t.flags|=1,rt(e,t,a,s),t.child)}function Uh(e,t,n,a,s){if(e===null){var c=n.type;return typeof c=="function"&&!Oo(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,Dh(e,t,c,a,s)):(e=_r(n.type,null,a,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!bu(e,s)){var h=c.memoizedProps;if(n=n.compare,n=n!==null?n:Xa,n(h,a)&&e.ref===t.ref)return xn(e,t,s)}return t.flags|=1,e=vn(c,a),e.ref=t.ref,e.return=t,t.child=e}function Dh(e,t,n,a,s){if(e!==null){var c=e.memoizedProps;if(Xa(c,a)&&e.ref===t.ref)if(tt=!1,t.pendingProps=a=c,bu(e,s))(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,xn(e,t,s)}return du(e,t,n,a,s)}function Lh(e,t,n){var a=t.pendingProps,s=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=c!==null?c.baseLanes|n:n,e!==null){for(s=t.child=e.child,c=0;s!==null;)c=c|s.lanes|s.childLanes,s=s.sibling;t.childLanes=c&~a}else t.childLanes=0,t.child=null;return zh(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Tr(t,c!==null?c.cachePool:null),c!==null?Dd(t,c):Io(),Eh(t);else return t.lanes=t.childLanes=536870912,zh(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(Tr(t,c.cachePool),Dd(t,c),Yn(),t.memoizedState=null):(e!==null&&Tr(t,null),Io(),Yn());return rt(e,t,s,n),t.child}function zh(e,t,n,a){var s=Bo();return s=s===null?null:{parent:Ke._currentValue,pool:s},t.memoizedState={baseLanes:n,cachePool:s},e!==null&&Tr(t,null),Io(),Eh(t),e!==null&&Ja(e,t,a,!0),null}function Pr(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function du(e,t,n,a,s){return Ai(t),n=Xo(e,t,n,a,void 0,s),a=Ko(),e!==null&&!tt?(Wo(e,t,s),xn(e,t,s)):(Se&&a&&Mo(t),t.flags|=1,rt(e,t,n,s),t.child)}function Nh(e,t,n,a,s,c){return Ai(t),t.updateQueue=null,n=zd(t,a,n,s),Ld(e),a=Ko(),e!==null&&!tt?(Wo(e,t,c),xn(e,t,c)):(Se&&a&&Mo(t),t.flags|=1,rt(e,t,n,c),t.child)}function qh(e,t,n,a,s){if(Ai(t),t.stateNode===null){var c=Zi,h=n.contextType;typeof h=="object"&&h!==null&&(c=ft(h)),c=new n(a,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=cu,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=a,c.state=t.memoizedState,c.refs={},Vo(t),h=n.contextType,c.context=typeof h=="object"&&h!==null?ft(h):Zi,c.state=t.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(uu(t,n,h,a),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(h=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),h!==c.state&&cu.enqueueReplaceState(c,c.state,null),rl(t,a,c,s),ll(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){c=t.stateNode;var p=t.memoizedProps,y=Oi(n,p);c.props=y;var k=c.context,N=n.contextType;h=Zi,typeof N=="object"&&N!==null&&(h=ft(N));var V=n.getDerivedStateFromProps;N=typeof V=="function"||typeof c.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,N||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(p||k!==h)&&xh(t,c,a,h),Bn=!1;var j=t.memoizedState;c.state=j,rl(t,a,c,s),ll(),k=t.memoizedState,p||j!==k||Bn?(typeof V=="function"&&(uu(t,n,V,a),k=t.memoizedState),(y=Bn||wh(t,n,y,a,j,k,h))?(N||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=k),c.props=a,c.state=k,c.context=h,a=y):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{c=t.stateNode,Qo(e,t),h=t.memoizedProps,N=Oi(n,h),c.props=N,V=t.pendingProps,j=c.context,k=n.contextType,y=Zi,typeof k=="object"&&k!==null&&(y=ft(k)),p=n.getDerivedStateFromProps,(k=typeof p=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h!==V||j!==y)&&xh(t,c,a,y),Bn=!1,j=t.memoizedState,c.state=j,rl(t,a,c,s),ll();var U=t.memoizedState;h!==V||j!==U||Bn||e!==null&&e.dependencies!==null&&xr(e.dependencies)?(typeof p=="function"&&(uu(t,n,p,a),U=t.memoizedState),(N=Bn||wh(t,n,N,a,j,U,y)||e!==null&&e.dependencies!==null&&xr(e.dependencies))?(k||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(a,U,y),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(a,U,y)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=U),c.props=a,c.state=U,c.context=y,a=N):(typeof c.componentDidUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&j===e.memoizedState||(t.flags|=1024),a=!1)}return c=a,Pr(e,t),a=(t.flags&128)!==0,c||a?(c=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&a?(t.child=sa(t,e.child,null,s),t.child=sa(t,null,n,s)):rt(e,t,n,s),t.memoizedState=c.state,e=t.child):e=xn(e,t,s),e}function Bh(e,t,n,a){return Za(),t.flags|=256,rt(e,t,n,a),t.child}var hu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function mu(e){return{baseLanes:e,cachePool:Td()}}function gu(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Pt),e}function Hh(e,t,n){var a=t.pendingProps,s=!1,c=(t.flags&128)!==0,h;if((h=c)||(h=e!==null&&e.memoizedState===null?!1:(We.current&2)!==0),h&&(s=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(s?Pn(t):Yn(),Se){var p=Ve,y;if(y=p){e:{for(y=p,p=ln;y.nodeType!==8;){if(!p){p=null;break e}if(y=Zt(y.nextSibling),y===null){p=null;break e}}p=y}p!==null?(t.memoizedState={dehydrated:p,treeContext:Si!==null?{id:yn,overflow:bn}:null,retryLane:536870912,hydrationErrors:null},y=Ot(18,null,null,0),y.stateNode=p,y.return=t,t.child=y,pt=t,Ve=null,y=!0):y=!1}y||wi(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return Fu(p)?t.lanes=32:t.lanes=536870912,null;wn(t)}return p=a.children,a=a.fallback,s?(Yn(),s=t.mode,p=Yr({mode:"hidden",children:p},s),a=bi(a,s,n,null),p.return=t,a.return=t,p.sibling=a,t.child=p,s=t.child,s.memoizedState=mu(n),s.childLanes=gu(e,h,n),t.memoizedState=hu,a):(Pn(t),pu(t,p))}if(y=e.memoizedState,y!==null&&(p=y.dehydrated,p!==null)){if(c)t.flags&256?(Pn(t),t.flags&=-257,t=vu(e,t,n)):t.memoizedState!==null?(Yn(),t.child=e.child,t.flags|=128,t=null):(Yn(),s=a.fallback,p=t.mode,a=Yr({mode:"visible",children:a.children},p),s=bi(s,p,n,null),s.flags|=2,a.return=t,s.return=t,a.sibling=s,t.child=a,sa(t,e.child,null,n),a=t.child,a.memoizedState=mu(n),a.childLanes=gu(e,h,n),t.memoizedState=hu,t=s);else if(Pn(t),Fu(p)){if(h=p.nextSibling&&p.nextSibling.dataset,h)var k=h.dgst;h=k,a=Error(o(419)),a.stack="",a.digest=h,Fa({value:a,source:null,stack:null}),t=vu(e,t,n)}else if(tt||Ja(e,t,n,!1),h=(n&e.childLanes)!==0,tt||h){if(h=je,h!==null&&(a=n&-n,a=(a&42)!==0?1:Fs(a),a=(a&(h.suspendedLanes|n))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Wi(e,a),Ut(h,e,a),Mh;p.data==="$?"||Lu(),t=vu(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,Ve=Zt(p.nextSibling),pt=t,Se=!0,Ei=null,ln=!1,e!==null&&(Ht[Vt++]=yn,Ht[Vt++]=bn,Ht[Vt++]=Si,yn=e.id,bn=e.overflow,Si=t),t=pu(t,a.children),t.flags|=4096);return t}return s?(Yn(),s=a.fallback,p=t.mode,y=e.child,k=y.sibling,a=vn(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,k!==null?s=vn(k,s):(s=bi(s,p,n,null),s.flags|=2),s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,p=e.child.memoizedState,p===null?p=mu(n):(y=p.cachePool,y!==null?(k=Ke._currentValue,y=y.parent!==k?{parent:k,pool:k}:y):y=Td(),p={baseLanes:p.baseLanes|n,cachePool:y}),s.memoizedState=p,s.childLanes=gu(e,h,n),t.memoizedState=hu,a):(Pn(t),n=e.child,e=n.sibling,n=vn(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=n,t.memoizedState=null,n)}function pu(e,t){return t=Yr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Yr(e,t){return e=Ot(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function vu(e,t,n){return sa(t,e.child,null,n),e=pu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Vh(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Lo(e.return,t,n)}function yu(e,t,n,a,s){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:s}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=n,c.tailMode=s)}function Qh(e,t,n){var a=t.pendingProps,s=a.revealOrder,c=a.tail;if(rt(e,t,a.children,n),a=We.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vh(e,n,t);else if(e.tag===19)Vh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(G(We,a),s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Hr(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),yu(t,!1,s,n,c);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Hr(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}yu(t,!0,n,null,c);break;case"together":yu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function xn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Ja(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=vn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=vn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&xr(e)))}function Gy(e,t,n){switch(t.tag){case 3:Me(t,t.stateNode.containerInfo),qn(t,Ke,e.memoizedState.cache),Za();break;case 27:case 5:mn(t);break;case 4:Me(t,t.stateNode.containerInfo);break;case 10:qn(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Pn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Hh(e,t,n):(Pn(t),e=xn(e,t,n),e!==null?e.sibling:null);Pn(t);break;case 19:var s=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(Ja(e,t,n,!1),a=(n&t.childLanes)!==0),s){if(a)return Qh(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),G(We,We.current),a)break;return null;case 22:case 23:return t.lanes=0,Lh(e,t,n);case 24:qn(t,Ke,e.memoizedState.cache)}return xn(e,t,n)}function Ph(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)tt=!0;else{if(!bu(e,n)&&(t.flags&128)===0)return tt=!1,Gy(e,t,n);tt=(e.flags&131072)!==0}else tt=!1,Se&&(t.flags&1048576)!==0&&bd(t,wr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,s=a._init;if(a=s(a._payload),t.type=a,typeof a=="function")Oo(a)?(e=Oi(a,e),t.tag=1,t=qh(null,t,a,e,n)):(t.tag=0,t=du(null,t,a,e,n));else{if(a!=null){if(s=a.$$typeof,s===F){t.tag=11,t=jh(null,t,a,e,n);break e}else if(s===re){t.tag=14,t=Uh(null,t,a,e,n);break e}}throw t=ze(a)||a,Error(o(306,t,""))}}return t;case 0:return du(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,s=Oi(a,t.pendingProps),qh(e,t,a,s,n);case 3:e:{if(Me(t,t.stateNode.containerInfo),e===null)throw Error(o(387));a=t.pendingProps;var c=t.memoizedState;s=c.element,Qo(e,t),rl(t,a,null,n);var h=t.memoizedState;if(a=h.cache,qn(t,Ke,a),a!==c.cache&&zo(t,[Ke],n,!0),ll(),a=h.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Bh(e,t,a,n);break e}else if(a!==s){s=qt(Error(o(424)),t),Fa(s),t=Bh(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ve=Zt(e.firstChild),pt=t,Se=!0,Ei=null,ln=!0,n=_h(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Za(),a===s){t=xn(e,t,n);break e}rt(e,t,a,n)}t=t.child}return t;case 26:return Pr(e,t),e===null?(n=Gm(t.type,null,t.pendingProps,null))?t.memoizedState=n:Se||(n=t.type,e=t.pendingProps,a=as(ae.current).createElement(n),a[ct]=t,a[_t]=e,ot(a,n,e),et(a),t.stateNode=a):t.memoizedState=Gm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return mn(t),e===null&&Se&&(a=t.stateNode=Ym(t.type,t.pendingProps,ae.current),pt=t,ln=!0,s=Ve,Jn(t.type)?(Ju=s,Ve=Zt(a.firstChild)):Ve=s),rt(e,t,t.pendingProps.children,n),Pr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((s=a=Ve)&&(a=_b(a,t.type,t.pendingProps,ln),a!==null?(t.stateNode=a,pt=t,Ve=Zt(a.firstChild),ln=!1,s=!0):s=!1),s||wi(t)),mn(t),s=t.type,c=t.pendingProps,h=e!==null?e.memoizedProps:null,a=c.children,Ku(s,c)?a=null:h!==null&&Ku(s,h)&&(t.flags|=32),t.memoizedState!==null&&(s=Xo(e,t,By,null,null,n),Ol._currentValue=s),Pr(e,t),rt(e,t,a,n),t.child;case 6:return e===null&&Se&&((e=n=Ve)&&(n=Eb(n,t.pendingProps,ln),n!==null?(t.stateNode=n,pt=t,Ve=null,e=!0):e=!1),e||wi(t)),null;case 13:return Hh(e,t,n);case 4:return Me(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=sa(t,null,a,n):rt(e,t,a,n),t.child;case 11:return jh(e,t,t.type,t.pendingProps,n);case 7:return rt(e,t,t.pendingProps,n),t.child;case 8:return rt(e,t,t.pendingProps.children,n),t.child;case 12:return rt(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,qn(t,t.type,a.value),rt(e,t,a.children,n),t.child;case 9:return s=t.type._context,a=t.pendingProps.children,Ai(t),s=ft(s),a=a(s),t.flags|=1,rt(e,t,a,n),t.child;case 14:return Uh(e,t,t.type,t.pendingProps,n);case 15:return Dh(e,t,t.type,t.pendingProps,n);case 19:return Qh(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=Yr(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=vn(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Lh(e,t,n);case 24:return Ai(t),a=ft(Ke),e===null?(s=Bo(),s===null&&(s=je,c=No(),s.pooledCache=c,c.refCount++,c!==null&&(s.pooledCacheLanes|=n),s=c),t.memoizedState={parent:a,cache:s},Vo(t),qn(t,Ke,s)):((e.lanes&n)!==0&&(Qo(e,t),rl(t,null,null,n),ll()),s=e.memoizedState,c=t.memoizedState,s.parent!==a?(s={parent:a,cache:a},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),qn(t,Ke,a)):(a=c.cache,qn(t,Ke,a),a!==s.cache&&zo(t,[Ke],n,!0))),rt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function An(e){e.flags|=4}function Yh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Fm(t)){if(t=Qt.current,t!==null&&((pe&4194048)===pe?rn!==null:(pe&62914560)!==pe&&(pe&536870912)===0||t!==rn))throw il=Ho,Cd;e.flags|=8192}}function Ir(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ef():536870912,e.lanes|=t,fa|=t)}function hl(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags&65011712,a|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags,a|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function Xy(e,t,n){var a=t.pendingProps;switch(jo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return qe(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),_n(Ke),nn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Wa(t)?An(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Ed())),qe(t),null;case 26:return n=t.memoizedState,e===null?(An(t),n!==null?(qe(t),Yh(t,n)):(qe(t),t.flags&=-16777217)):n?n!==e.memoizedState?(An(t),qe(t),Yh(t,n)):(qe(t),t.flags&=-16777217):(e.memoizedProps!==a&&An(t),qe(t),t.flags&=-16777217),null;case 27:hi(t),n=ae.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&An(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return qe(t),null}e=J.current,Wa(t)?Sd(t):(e=Ym(s,a,n),t.stateNode=e,An(t))}return qe(t),null;case 5:if(hi(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&An(t);else{if(!a){if(t.stateNode===null)throw Error(o(166));return qe(t),null}if(e=J.current,Wa(t))Sd(t);else{switch(s=as(ae.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?s.createElement("select",{is:a.is}):s.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?s.createElement(n,{is:a.is}):s.createElement(n)}}e[ct]=t,e[_t]=a;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(ot(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&An(t)}}return qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&An(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(o(166));if(e=ae.current,Wa(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,s=pt,s!==null)switch(s.tag){case 27:case 5:a=s.memoizedProps}e[ct]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Nm(e.nodeValue,n)),e||wi(t)}else e=as(e).createTextNode(a),e[ct]=t,t.stateNode=e}return qe(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=Wa(t),a!==null&&a.dehydrated!==null){if(e===null){if(!s)throw Error(o(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[ct]=t}else Za(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;qe(t),s=!1}else s=Ed(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(wn(t),t):(wn(t),null)}if(wn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,s=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(s=a.alternate.memoizedState.cachePool.pool);var c=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(c=a.memoizedState.cachePool.pool),c!==s&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Ir(t,t.updateQueue),qe(t),null;case 4:return nn(),e===null&&Yu(t.stateNode.containerInfo),qe(t),null;case 10:return _n(t.type),qe(t),null;case 19:if($(We),s=t.memoizedState,s===null)return qe(t),null;if(a=(t.flags&128)!==0,c=s.rendering,c===null)if(a)hl(s,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Hr(e),c!==null){for(t.flags|=128,hl(s,!1),e=c.updateQueue,t.updateQueue=e,Ir(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)yd(n,e),n=n.sibling;return G(We,We.current&1|2),t.child}e=e.sibling}s.tail!==null&&He()>Xr&&(t.flags|=128,a=!0,hl(s,!1),t.lanes=4194304)}else{if(!a)if(e=Hr(c),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Ir(t,e),hl(s,!0),s.tail===null&&s.tailMode==="hidden"&&!c.alternate&&!Se)return qe(t),null}else 2*He()-s.renderingStartTime>Xr&&n!==536870912&&(t.flags|=128,a=!0,hl(s,!1),t.lanes=4194304);s.isBackwards?(c.sibling=t.child,t.child=c):(e=s.last,e!==null?e.sibling=c:t.child=c,s.last=c)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=He(),t.sibling=null,e=We.current,G(We,a?e&1|2:e&1),t):(qe(t),null);case 22:case 23:return wn(t),$o(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),n=t.updateQueue,n!==null&&Ir(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&$(Ti),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),_n(Ke),qe(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function Ky(e,t){switch(jo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return _n(Ke),nn(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return hi(t),null;case 13:if(wn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));Za()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(We),null;case 4:return nn(),null;case 10:return _n(t.type),null;case 22:case 23:return wn(t),$o(),e!==null&&$(Ti),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return _n(Ke),null;case 25:return null;default:return null}}function Ih(e,t){switch(jo(t),t.tag){case 3:_n(Ke),nn();break;case 26:case 27:case 5:hi(t);break;case 4:nn();break;case 13:wn(t);break;case 19:$(We);break;case 10:_n(t.type);break;case 22:case 23:wn(t),$o(),e!==null&&$(Ti);break;case 24:_n(Ke)}}function ml(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var s=a.next;n=s;do{if((n.tag&e)===e){a=void 0;var c=n.create,h=n.inst;a=c(),h.destroy=a}n=n.next}while(n!==s)}}catch(p){Re(t,t.return,p)}}function In(e,t,n){try{var a=t.updateQueue,s=a!==null?a.lastEffect:null;if(s!==null){var c=s.next;a=c;do{if((a.tag&e)===e){var h=a.inst,p=h.destroy;if(p!==void 0){h.destroy=void 0,s=t;var y=n,k=p;try{k()}catch(N){Re(s,y,N)}}}a=a.next}while(a!==c)}}catch(N){Re(t,t.return,N)}}function $h(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Ud(t,n)}catch(a){Re(e,e.return,a)}}}function Gh(e,t,n){n.props=Oi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){Re(e,t,a)}}function gl(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(s){Re(e,t,s)}}function sn(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(s){Re(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){Re(e,t,s)}else n.current=null}function Xh(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(s){Re(e,e.return,s)}}function Su(e,t,n){try{var a=e.stateNode;pb(a,e.type,n,t),a[_t]=t}catch(s){Re(e,e.return,s)}}function Kh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Jn(e.type)||e.tag===4}function _u(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Jn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Eu(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=is));else if(a!==4&&(a===27&&Jn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Eu(e,t,n),e=e.sibling;e!==null;)Eu(e,t,n),e=e.sibling}function $r(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Jn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for($r(e,t,n),e=e.sibling;e!==null;)$r(e,t,n),e=e.sibling}function Wh(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);ot(t,a,n),t[ct]=e,t[_t]=n}catch(c){Re(e,e.return,c)}}var Tn=!1,Ie=!1,wu=!1,Zh=typeof WeakSet=="function"?WeakSet:Set,nt=null;function Wy(e,t){if(e=e.containerInfo,Gu=cs,e=od(e),_o(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var s=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var h=0,p=-1,y=-1,k=0,N=0,V=e,j=null;t:for(;;){for(var U;V!==n||s!==0&&V.nodeType!==3||(p=h+s),V!==c||a!==0&&V.nodeType!==3||(y=h+a),V.nodeType===3&&(h+=V.nodeValue.length),(U=V.firstChild)!==null;)j=V,V=U;for(;;){if(V===e)break t;if(j===n&&++k===s&&(p=h),j===c&&++N===a&&(y=h),(U=V.nextSibling)!==null)break;V=j,j=V.parentNode}V=U}n=p===-1||y===-1?null:{start:p,end:y}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xu={focusedElem:e,selectionRange:n},cs=!1,nt=t;nt!==null;)if(t=nt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,nt=e;else for(;nt!==null;){switch(t=nt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,s=c.memoizedProps,c=c.memoizedState,a=n.stateNode;try{var le=Oi(n.type,s,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(le,c),a.__reactInternalSnapshotBeforeUpdate=e}catch(te){Re(n,n.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Zu(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Zu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,nt=e;break}nt=t.return}}function Fh(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:$n(e,n),a&4&&ml(5,n);break;case 1:if($n(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(h){Re(n,n.return,h)}else{var s=Oi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Re(n,n.return,h)}}a&64&&$h(n),a&512&&gl(n,n.return);break;case 3:if($n(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Ud(e,t)}catch(h){Re(n,n.return,h)}}break;case 27:t===null&&a&4&&Wh(n);case 26:case 5:$n(e,n),t===null&&a&4&&Xh(n),a&512&&gl(n,n.return);break;case 12:$n(e,n);break;case 13:$n(e,n),a&4&&tm(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=lb.bind(null,n),wb(e,n))));break;case 22:if(a=n.memoizedState!==null||Tn,!a){t=t!==null&&t.memoizedState!==null||Ie,s=Tn;var c=Ie;Tn=a,(Ie=t)&&!c?Gn(e,n,(n.subtreeFlags&8772)!==0):$n(e,n),Tn=s,Ie=c}break;case 30:break;default:$n(e,n)}}function Jh(e){var t=e.alternate;t!==null&&(e.alternate=null,Jh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&to(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var De=null,xt=!1;function Cn(e,t,n){for(n=n.child;n!==null;)em(e,t,n),n=n.sibling}function em(e,t,n){if(at&&typeof at.onCommitFiberUnmount=="function")try{at.onCommitFiberUnmount(gt,n)}catch{}switch(n.tag){case 26:Ie||sn(n,t),Cn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ie||sn(n,t);var a=De,s=xt;Jn(n.type)&&(De=n.stateNode,xt=!1),Cn(e,t,n),xl(n.stateNode),De=a,xt=s;break;case 5:Ie||sn(n,t);case 6:if(a=De,s=xt,De=null,Cn(e,t,n),De=a,xt=s,De!==null)if(xt)try{(De.nodeType===9?De.body:De.nodeName==="HTML"?De.ownerDocument.body:De).removeChild(n.stateNode)}catch(c){Re(n,t,c)}else try{De.removeChild(n.stateNode)}catch(c){Re(n,t,c)}break;case 18:De!==null&&(xt?(e=De,Qm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),jl(e)):Qm(De,n.stateNode));break;case 4:a=De,s=xt,De=n.stateNode.containerInfo,xt=!0,Cn(e,t,n),De=a,xt=s;break;case 0:case 11:case 14:case 15:Ie||In(2,n,t),Ie||In(4,n,t),Cn(e,t,n);break;case 1:Ie||(sn(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&Gh(n,t,a)),Cn(e,t,n);break;case 21:Cn(e,t,n);break;case 22:Ie=(a=Ie)||n.memoizedState!==null,Cn(e,t,n),Ie=a;break;default:Cn(e,t,n)}}function tm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{jl(e)}catch(n){Re(t,t.return,n)}}function Zy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Zh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Zh),t;default:throw Error(o(435,e.tag))}}function xu(e,t){var n=Zy(e);t.forEach(function(a){var s=rb.bind(null,e,a);n.has(a)||(n.add(a),a.then(s,s))})}function Rt(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var s=n[a],c=e,h=t,p=h;e:for(;p!==null;){switch(p.tag){case 27:if(Jn(p.type)){De=p.stateNode,xt=!1;break e}break;case 5:De=p.stateNode,xt=!1;break e;case 3:case 4:De=p.stateNode.containerInfo,xt=!0;break e}p=p.return}if(De===null)throw Error(o(160));em(c,h,s),De=null,xt=!1,c=s.alternate,c!==null&&(c.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)nm(t,e),t=t.sibling}var Wt=null;function nm(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rt(t,e),kt(e),a&4&&(In(3,e,e.return),ml(3,e),In(5,e,e.return));break;case 1:Rt(t,e),kt(e),a&512&&(Ie||n===null||sn(n,n.return)),a&64&&Tn&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var s=Wt;if(Rt(t,e),kt(e),a&512&&(Ie||n===null||sn(n,n.return)),a&4){var c=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,s=s.ownerDocument||s;t:switch(a){case"title":c=s.getElementsByTagName("title")[0],(!c||c[Ba]||c[ct]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=s.createElement(a),s.head.insertBefore(c,s.querySelector("head > title"))),ot(c,a,n),c[ct]=e,et(c),a=c;break e;case"link":var h=Wm("link","href",s).get(a+(n.href||""));if(h){for(var p=0;p<h.length;p++)if(c=h[p],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(p,1);break t}}c=s.createElement(a),ot(c,a,n),s.head.appendChild(c);break;case"meta":if(h=Wm("meta","content",s).get(a+(n.content||""))){for(p=0;p<h.length;p++)if(c=h[p],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(p,1);break t}}c=s.createElement(a),ot(c,a,n),s.head.appendChild(c);break;default:throw Error(o(468,a))}c[ct]=e,et(c),a=c}e.stateNode=a}else Zm(s,e.type,e.stateNode);else e.stateNode=Km(s,a,e.memoizedProps);else c!==a?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,a===null?Zm(s,e.type,e.stateNode):Km(s,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Su(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rt(t,e),kt(e),a&512&&(Ie||n===null||sn(n,n.return)),n!==null&&a&4&&Su(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rt(t,e),kt(e),a&512&&(Ie||n===null||sn(n,n.return)),e.flags&32){s=e.stateNode;try{Pi(s,"")}catch(U){Re(e,e.return,U)}}a&4&&e.stateNode!=null&&(s=e.memoizedProps,Su(e,s,n!==null?n.memoizedProps:s)),a&1024&&(wu=!0);break;case 6:if(Rt(t,e),kt(e),a&4){if(e.stateNode===null)throw Error(o(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(U){Re(e,e.return,U)}}break;case 3:if(ss=null,s=Wt,Wt=ls(t.containerInfo),Rt(t,e),Wt=s,kt(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{jl(t.containerInfo)}catch(U){Re(e,e.return,U)}wu&&(wu=!1,im(e));break;case 4:a=Wt,Wt=ls(e.stateNode.containerInfo),Rt(t,e),kt(e),Wt=a;break;case 12:Rt(t,e),kt(e);break;case 13:Rt(t,e),kt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(ku=He()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,xu(e,a)));break;case 22:s=e.memoizedState!==null;var y=n!==null&&n.memoizedState!==null,k=Tn,N=Ie;if(Tn=k||s,Ie=N||y,Rt(t,e),Ie=N,Tn=k,kt(e),a&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(n===null||y||Tn||Ie||Ri(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){y=n=t;try{if(c=y.stateNode,s)h=c.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{p=y.stateNode;var V=y.memoizedProps.style,j=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=j==null||typeof j=="boolean"?"":(""+j).trim()}}catch(U){Re(y,y.return,U)}}}else if(t.tag===6){if(n===null){y=t;try{y.stateNode.nodeValue=s?"":y.memoizedProps}catch(U){Re(y,y.return,U)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,xu(e,n))));break;case 19:Rt(t,e),kt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,xu(e,a)));break;case 30:break;case 21:break;default:Rt(t,e),kt(e)}}function kt(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(Kh(a)){n=a;break}a=a.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var s=n.stateNode,c=_u(e);$r(e,c,s);break;case 5:var h=n.stateNode;n.flags&32&&(Pi(h,""),n.flags&=-33);var p=_u(e);$r(e,p,h);break;case 3:case 4:var y=n.stateNode.containerInfo,k=_u(e);Eu(e,k,y);break;default:throw Error(o(161))}}catch(N){Re(e,e.return,N)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function im(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;im(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function $n(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Fh(e,t.alternate,t),t=t.sibling}function Ri(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:In(4,t,t.return),Ri(t);break;case 1:sn(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Gh(t,t.return,n),Ri(t);break;case 27:xl(t.stateNode);case 26:case 5:sn(t,t.return),Ri(t);break;case 22:t.memoizedState===null&&Ri(t);break;case 30:Ri(t);break;default:Ri(t)}e=e.sibling}}function Gn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,s=e,c=t,h=c.flags;switch(c.tag){case 0:case 11:case 15:Gn(s,c,n),ml(4,c);break;case 1:if(Gn(s,c,n),a=c,s=a.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(k){Re(a,a.return,k)}if(a=c,s=a.updateQueue,s!==null){var p=a.stateNode;try{var y=s.shared.hiddenCallbacks;if(y!==null)for(s.shared.hiddenCallbacks=null,s=0;s<y.length;s++)jd(y[s],p)}catch(k){Re(a,a.return,k)}}n&&h&64&&$h(c),gl(c,c.return);break;case 27:Wh(c);case 26:case 5:Gn(s,c,n),n&&a===null&&h&4&&Xh(c),gl(c,c.return);break;case 12:Gn(s,c,n);break;case 13:Gn(s,c,n),n&&h&4&&tm(s,c);break;case 22:c.memoizedState===null&&Gn(s,c,n),gl(c,c.return);break;case 30:break;default:Gn(s,c,n)}t=t.sibling}}function Au(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&el(n))}function Tu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&el(e))}function on(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)am(e,t,n,a),t=t.sibling}function am(e,t,n,a){var s=t.flags;switch(t.tag){case 0:case 11:case 15:on(e,t,n,a),s&2048&&ml(9,t);break;case 1:on(e,t,n,a);break;case 3:on(e,t,n,a),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&el(e)));break;case 12:if(s&2048){on(e,t,n,a),e=t.stateNode;try{var c=t.memoizedProps,h=c.id,p=c.onPostCommit;typeof p=="function"&&p(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){Re(t,t.return,y)}}else on(e,t,n,a);break;case 13:on(e,t,n,a);break;case 23:break;case 22:c=t.stateNode,h=t.alternate,t.memoizedState!==null?c._visibility&2?on(e,t,n,a):pl(e,t):c._visibility&2?on(e,t,n,a):(c._visibility|=2,oa(e,t,n,a,(t.subtreeFlags&10256)!==0)),s&2048&&Au(h,t);break;case 24:on(e,t,n,a),s&2048&&Tu(t.alternate,t);break;default:on(e,t,n,a)}}function oa(e,t,n,a,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,h=t,p=n,y=a,k=h.flags;switch(h.tag){case 0:case 11:case 15:oa(c,h,p,y,s),ml(8,h);break;case 23:break;case 22:var N=h.stateNode;h.memoizedState!==null?N._visibility&2?oa(c,h,p,y,s):pl(c,h):(N._visibility|=2,oa(c,h,p,y,s)),s&&k&2048&&Au(h.alternate,h);break;case 24:oa(c,h,p,y,s),s&&k&2048&&Tu(h.alternate,h);break;default:oa(c,h,p,y,s)}t=t.sibling}}function pl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,s=a.flags;switch(a.tag){case 22:pl(n,a),s&2048&&Au(a.alternate,a);break;case 24:pl(n,a),s&2048&&Tu(a.alternate,a);break;default:pl(n,a)}t=t.sibling}}var vl=8192;function ua(e){if(e.subtreeFlags&vl)for(e=e.child;e!==null;)lm(e),e=e.sibling}function lm(e){switch(e.tag){case 26:ua(e),e.flags&vl&&e.memoizedState!==null&&zb(Wt,e.memoizedState,e.memoizedProps);break;case 5:ua(e);break;case 3:case 4:var t=Wt;Wt=ls(e.stateNode.containerInfo),ua(e),Wt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=vl,vl=16777216,ua(e),vl=t):ua(e));break;default:ua(e)}}function rm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function yl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];nt=a,om(a,e)}rm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sm(e),e=e.sibling}function sm(e){switch(e.tag){case 0:case 11:case 15:yl(e),e.flags&2048&&In(9,e,e.return);break;case 3:yl(e);break;case 12:yl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Gr(e)):yl(e);break;default:yl(e)}}function Gr(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];nt=a,om(a,e)}rm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:In(8,t,t.return),Gr(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Gr(t));break;default:Gr(t)}e=e.sibling}}function om(e,t){for(;nt!==null;){var n=nt;switch(n.tag){case 0:case 11:case 15:In(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:el(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,nt=a;else e:for(n=e;nt!==null;){a=nt;var s=a.sibling,c=a.return;if(Jh(a),a===n){nt=null;break e}if(s!==null){s.return=c,nt=s;break e}nt=c}}}var Fy={getCacheForType:function(e){var t=ft(Ke),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Jy=typeof WeakMap=="function"?WeakMap:Map,we=0,je=null,he=null,pe=0,xe=0,Mt=null,Xn=!1,ca=!1,Cu=!1,On=0,Qe=0,Kn=0,ki=0,Ou=0,Pt=0,fa=0,bl=null,At=null,Ru=!1,ku=0,Xr=1/0,Kr=null,Wn=null,st=0,Zn=null,da=null,ha=0,Mu=0,ju=null,um=null,Sl=0,Uu=null;function jt(){if((we&2)!==0&&pe!==0)return pe&-pe;if(z.T!==null){var e=ea;return e!==0?e:Hu()}return Af()}function cm(){Pt===0&&(Pt=(pe&536870912)===0||Se?_f():536870912);var e=Qt.current;return e!==null&&(e.flags|=32),Pt}function Ut(e,t,n){(e===je&&(xe===2||xe===9)||e.cancelPendingCommit!==null)&&(ma(e,0),Fn(e,pe,Pt,!1)),qa(e,n),((we&2)===0||e!==je)&&(e===je&&((we&2)===0&&(ki|=n),Qe===4&&Fn(e,pe,Pt,!1)),un(e))}function fm(e,t,n){if((we&6)!==0)throw Error(o(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Na(e,t),s=a?nb(e,t):zu(e,t,!0),c=a;do{if(s===0){ca&&!a&&Fn(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!eb(n)){s=zu(e,t,!1),c=!1;continue}if(s===2){if(c=t,e.errorRecoveryDisabledLanes&c)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var p=e;s=bl;var y=p.current.memoizedState.isDehydrated;if(y&&(ma(p,h).flags|=256),h=zu(p,h,!1),h!==2){if(Cu&&!y){p.errorRecoveryDisabledLanes|=c,ki|=c,s=4;break e}c=At,At=s,c!==null&&(At===null?At=c:At.push.apply(At,c))}s=h}if(c=!1,s!==2)continue}}if(s===1){ma(e,0),Fn(e,t,0,!0);break}e:{switch(a=e,c=s,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Fn(a,t,Pt,!Xn);break e;case 2:At=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(s=ku+300-He(),10<s)){if(Fn(a,t,Pt,!Xn),rr(a,0,!0)!==0)break e;a.timeoutHandle=Hm(dm.bind(null,a,n,At,Kr,Ru,t,Pt,ki,fa,Xn,c,2,-0,0),s);break e}dm(a,n,At,Kr,Ru,t,Pt,ki,fa,Xn,c,0,-0,0)}}break}while(!0);un(e)}function dm(e,t,n,a,s,c,h,p,y,k,N,V,j,U){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(Cl={stylesheets:null,count:0,unsuspend:Lb},lm(t),V=Nb(),V!==null)){e.cancelPendingCommit=V(bm.bind(null,e,t,c,n,a,s,h,p,y,N,1,j,U)),Fn(e,c,h,!k);return}bm(e,t,c,n,a,s,h,p,y)}function eb(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var s=n[a],c=s.getSnapshot;s=s.value;try{if(!Ct(c(),s))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fn(e,t,n,a){t&=~Ou,t&=~ki,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var s=t;0<s;){var c=31-lt(s),h=1<<c;a[c]=-1,s&=~h}n!==0&&wf(e,n,t)}function Wr(){return(we&6)===0?(_l(0),!1):!0}function Du(){if(he!==null){if(xe===0)var e=he.return;else e=he,Sn=xi=null,Zo(e),ra=null,fl=0,e=he;for(;e!==null;)Ih(e.alternate,e),e=e.return;he=null}}function ma(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,yb(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Du(),je=e,he=n=vn(e.current,null),pe=t,xe=0,Mt=null,Xn=!1,ca=Na(e,t),Cu=!1,fa=Pt=Ou=ki=Kn=Qe=0,At=bl=null,Ru=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var s=31-lt(a),c=1<<s;t|=e[s],a&=~c}return On=t,yr(),n}function hm(e,t){ce=null,z.H=Nr,t===nl||t===Cr?(t=kd(),xe=3):t===Cd?(t=kd(),xe=4):xe=t===Mh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Mt=t,he===null&&(Qe=1,Qr(e,qt(t,e.current)))}function mm(){var e=z.H;return z.H=Nr,e===null?Nr:e}function gm(){var e=z.A;return z.A=Fy,e}function Lu(){Qe=4,Xn||(pe&4194048)!==pe&&Qt.current!==null||(ca=!0),(Kn&134217727)===0&&(ki&134217727)===0||je===null||Fn(je,pe,Pt,!1)}function zu(e,t,n){var a=we;we|=2;var s=mm(),c=gm();(je!==e||pe!==t)&&(Kr=null,ma(e,t)),t=!1;var h=Qe;e:do try{if(xe!==0&&he!==null){var p=he,y=Mt;switch(xe){case 8:Du(),h=6;break e;case 3:case 2:case 9:case 6:Qt.current===null&&(t=!0);var k=xe;if(xe=0,Mt=null,ga(e,p,y,k),n&&ca){h=0;break e}break;default:k=xe,xe=0,Mt=null,ga(e,p,y,k)}}tb(),h=Qe;break}catch(N){hm(e,N)}while(!0);return t&&e.shellSuspendCounter++,Sn=xi=null,we=a,z.H=s,z.A=c,he===null&&(je=null,pe=0,yr()),h}function tb(){for(;he!==null;)pm(he)}function nb(e,t){var n=we;we|=2;var a=mm(),s=gm();je!==e||pe!==t?(Kr=null,Xr=He()+500,ma(e,t)):ca=Na(e,t);e:do try{if(xe!==0&&he!==null){t=he;var c=Mt;t:switch(xe){case 1:xe=0,Mt=null,ga(e,t,c,1);break;case 2:case 9:if(Od(c)){xe=0,Mt=null,vm(t);break}t=function(){xe!==2&&xe!==9||je!==e||(xe=7),un(e)},c.then(t,t);break e;case 3:xe=7;break e;case 4:xe=5;break e;case 7:Od(c)?(xe=0,Mt=null,vm(t)):(xe=0,Mt=null,ga(e,t,c,7));break;case 5:var h=null;switch(he.tag){case 26:h=he.memoizedState;case 5:case 27:var p=he;if(!h||Fm(h)){xe=0,Mt=null;var y=p.sibling;if(y!==null)he=y;else{var k=p.return;k!==null?(he=k,Zr(k)):he=null}break t}}xe=0,Mt=null,ga(e,t,c,5);break;case 6:xe=0,Mt=null,ga(e,t,c,6);break;case 8:Du(),Qe=6;break e;default:throw Error(o(462))}}ib();break}catch(N){hm(e,N)}while(!0);return Sn=xi=null,z.H=a,z.A=s,we=n,he!==null?0:(je=null,pe=0,yr(),Qe)}function ib(){for(;he!==null&&!Fe();)pm(he)}function pm(e){var t=Ph(e.alternate,e,On);e.memoizedProps=e.pendingProps,t===null?Zr(e):he=t}function vm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Nh(n,t,t.pendingProps,t.type,void 0,pe);break;case 11:t=Nh(n,t,t.pendingProps,t.type.render,t.ref,pe);break;case 5:Zo(t);default:Ih(n,t),t=he=yd(t,On),t=Ph(n,t,On)}e.memoizedProps=e.pendingProps,t===null?Zr(e):he=t}function ga(e,t,n,a){Sn=xi=null,Zo(t),ra=null,fl=0;var s=t.return;try{if($y(e,s,t,n,pe)){Qe=1,Qr(e,qt(n,e.current)),he=null;return}}catch(c){if(s!==null)throw he=s,c;Qe=1,Qr(e,qt(n,e.current)),he=null;return}t.flags&32768?(Se||a===1?e=!0:ca||(pe&536870912)!==0?e=!1:(Xn=e=!0,(a===2||a===9||a===3||a===6)&&(a=Qt.current,a!==null&&a.tag===13&&(a.flags|=16384))),ym(t,e)):Zr(t)}function Zr(e){var t=e;do{if((t.flags&32768)!==0){ym(t,Xn);return}e=t.return;var n=Xy(t.alternate,t,On);if(n!==null){he=n;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);Qe===0&&(Qe=5)}function ym(e,t){do{var n=Ky(e.alternate,e);if(n!==null){n.flags&=32767,he=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){he=e;return}he=e=n}while(e!==null);Qe=6,he=null}function bm(e,t,n,a,s,c,h,p,y){e.cancelPendingCommit=null;do Fr();while(st!==0);if((we&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=To,Lv(e,n,c,h,p,y),e===je&&(he=je=null,pe=0),da=t,Zn=e,ha=n,Mu=c,ju=s,um=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,sb(bt,function(){return xm(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=z.T,z.T=null,s=I.p,I.p=2,h=we,we|=4;try{Wy(e,t,n)}finally{we=h,I.p=s,z.T=a}}st=1,Sm(),_m(),Em()}}function Sm(){if(st===1){st=0;var e=Zn,t=da,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=z.T,z.T=null;var a=I.p;I.p=2;var s=we;we|=4;try{nm(t,e);var c=Xu,h=od(e.containerInfo),p=c.focusedElem,y=c.selectionRange;if(h!==p&&p&&p.ownerDocument&&sd(p.ownerDocument.documentElement,p)){if(y!==null&&_o(p)){var k=y.start,N=y.end;if(N===void 0&&(N=k),"selectionStart"in p)p.selectionStart=k,p.selectionEnd=Math.min(N,p.value.length);else{var V=p.ownerDocument||document,j=V&&V.defaultView||window;if(j.getSelection){var U=j.getSelection(),le=p.textContent.length,te=Math.min(y.start,le),Ce=y.end===void 0?te:Math.min(y.end,le);!U.extend&&te>Ce&&(h=Ce,Ce=te,te=h);var A=rd(p,te),E=rd(p,Ce);if(A&&E&&(U.rangeCount!==1||U.anchorNode!==A.node||U.anchorOffset!==A.offset||U.focusNode!==E.node||U.focusOffset!==E.offset)){var R=V.createRange();R.setStart(A.node,A.offset),U.removeAllRanges(),te>Ce?(U.addRange(R),U.extend(E.node,E.offset)):(R.setEnd(E.node,E.offset),U.addRange(R))}}}}for(V=[],U=p;U=U.parentNode;)U.nodeType===1&&V.push({element:U,left:U.scrollLeft,top:U.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var B=V[p];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}cs=!!Gu,Xu=Gu=null}finally{we=s,I.p=a,z.T=n}}e.current=t,st=2}}function _m(){if(st===2){st=0;var e=Zn,t=da,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=z.T,z.T=null;var a=I.p;I.p=2;var s=we;we|=4;try{Fh(e,t.alternate,t)}finally{we=s,I.p=a,z.T=n}}st=3}}function Em(){if(st===4||st===3){st=0,Ne();var e=Zn,t=da,n=ha,a=um;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?st=5:(st=0,da=Zn=null,wm(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Wn=null),Js(n),t=t.stateNode,at&&typeof at.onCommitFiberRoot=="function")try{at.onCommitFiberRoot(gt,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=z.T,s=I.p,I.p=2,z.T=null;try{for(var c=e.onRecoverableError,h=0;h<a.length;h++){var p=a[h];c(p.value,{componentStack:p.stack})}}finally{z.T=t,I.p=s}}(ha&3)!==0&&Fr(),un(e),s=e.pendingLanes,(n&4194090)!==0&&(s&42)!==0?e===Uu?Sl++:(Sl=0,Uu=e):Sl=0,_l(0)}}function wm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,el(t)))}function Fr(e){return Sm(),_m(),Em(),xm()}function xm(){if(st!==5)return!1;var e=Zn,t=Mu;Mu=0;var n=Js(ha),a=z.T,s=I.p;try{I.p=32>n?32:n,z.T=null,n=ju,ju=null;var c=Zn,h=ha;if(st=0,da=Zn=null,ha=0,(we&6)!==0)throw Error(o(331));var p=we;if(we|=4,sm(c.current),am(c,c.current,h,n),we=p,_l(0,!1),at&&typeof at.onPostCommitFiberRoot=="function")try{at.onPostCommitFiberRoot(gt,c)}catch{}return!0}finally{I.p=s,z.T=a,wm(e,t)}}function Am(e,t,n){t=qt(n,t),t=fu(e.stateNode,t,2),e=Vn(e,t,2),e!==null&&(qa(e,2),un(e))}function Re(e,t,n){if(e.tag===3)Am(e,e,n);else for(;t!==null;){if(t.tag===3){Am(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Wn===null||!Wn.has(a))){e=qt(n,e),n=Rh(2),a=Vn(t,n,2),a!==null&&(kh(n,a,t,e),qa(a,2),un(a));break}}t=t.return}}function Nu(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new Jy;var s=new Set;a.set(t,s)}else s=a.get(t),s===void 0&&(s=new Set,a.set(t,s));s.has(n)||(Cu=!0,s.add(n),e=ab.bind(null,e,t,n),t.then(e,e))}function ab(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,je===e&&(pe&n)===n&&(Qe===4||Qe===3&&(pe&62914560)===pe&&300>He()-ku?(we&2)===0&&ma(e,0):Ou|=n,fa===pe&&(fa=0)),un(e)}function Tm(e,t){t===0&&(t=Ef()),e=Wi(e,t),e!==null&&(qa(e,t),un(e))}function lb(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Tm(e,n)}function rb(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(t),Tm(e,n)}function sb(e,t){return Ee(e,t)}var Jr=null,pa=null,qu=!1,es=!1,Bu=!1,Mi=0;function un(e){e!==pa&&e.next===null&&(pa===null?Jr=pa=e:pa=pa.next=e),es=!0,qu||(qu=!0,ub())}function _l(e,t){if(!Bu&&es){Bu=!0;do for(var n=!1,a=Jr;a!==null;){if(e!==0){var s=a.pendingLanes;if(s===0)var c=0;else{var h=a.suspendedLanes,p=a.pingedLanes;c=(1<<31-lt(42|e)+1)-1,c&=s&~(h&~p),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,km(a,c))}else c=pe,c=rr(a,a===je?c:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(c&3)===0||Na(a,c)||(n=!0,km(a,c));a=a.next}while(n);Bu=!1}}function ob(){Cm()}function Cm(){es=qu=!1;var e=0;Mi!==0&&(vb()&&(e=Mi),Mi=0);for(var t=He(),n=null,a=Jr;a!==null;){var s=a.next,c=Om(a,t);c===0?(a.next=null,n===null?Jr=s:n.next=s,s===null&&(pa=n)):(n=a,(e!==0||(c&3)!==0)&&(es=!0)),a=s}_l(e)}function Om(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,s=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var h=31-lt(c),p=1<<h,y=s[h];y===-1?((p&n)===0||(p&a)!==0)&&(s[h]=Dv(p,t)):y<=t&&(e.expiredLanes|=p),c&=~p}if(t=je,n=pe,n=rr(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(xe===2||xe===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&_e(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Na(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&_e(a),Js(n)){case 2:case 8:n=Je;break;case 32:n=bt;break;case 268435456:n=za;break;default:n=bt}return a=Rm.bind(null,e),n=Ee(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&_e(a),e.callbackPriority=2,e.callbackNode=null,2}function Rm(e,t){if(st!==0&&st!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Fr()&&e.callbackNode!==n)return null;var a=pe;return a=rr(e,e===je?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(fm(e,a,t),Om(e,He()),e.callbackNode!=null&&e.callbackNode===n?Rm.bind(null,e):null)}function km(e,t){if(Fr())return null;fm(e,t,!0)}function ub(){bb(function(){(we&6)!==0?Ee($e,ob):Cm()})}function Hu(){return Mi===0&&(Mi=_f()),Mi}function Mm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:fr(""+e)}function jm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function cb(e,t,n,a,s){if(t==="submit"&&n&&n.stateNode===s){var c=Mm((s[_t]||null).action),h=a.submitter;h&&(t=(t=h[_t]||null)?Mm(t.formAction):h.getAttribute("formAction"),t!==null&&(c=t,h=null));var p=new gr("action","action",null,a,s);e.push({event:p,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Mi!==0){var y=h?jm(s,h):new FormData(s);ru(n,{pending:!0,data:y,method:s.method,action:c},null,y)}}else typeof c=="function"&&(p.preventDefault(),y=h?jm(s,h):new FormData(s),ru(n,{pending:!0,data:y,method:s.method,action:c},c,y))},currentTarget:s}]})}}for(var Vu=0;Vu<Ao.length;Vu++){var Qu=Ao[Vu],fb=Qu.toLowerCase(),db=Qu[0].toUpperCase()+Qu.slice(1);Kt(fb,"on"+db)}Kt(fd,"onAnimationEnd"),Kt(dd,"onAnimationIteration"),Kt(hd,"onAnimationStart"),Kt("dblclick","onDoubleClick"),Kt("focusin","onFocus"),Kt("focusout","onBlur"),Kt(Ry,"onTransitionRun"),Kt(ky,"onTransitionStart"),Kt(My,"onTransitionCancel"),Kt(md,"onTransitionEnd"),Hi("onMouseEnter",["mouseout","mouseover"]),Hi("onMouseLeave",["mouseout","mouseover"]),Hi("onPointerEnter",["pointerout","pointerover"]),Hi("onPointerLeave",["pointerout","pointerover"]),gi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),gi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),gi("onBeforeInput",["compositionend","keypress","textInput","paste"]),gi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),gi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),gi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var El="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),hb=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(El));function Um(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],s=a.event;a=a.listeners;e:{var c=void 0;if(t)for(var h=a.length-1;0<=h;h--){var p=a[h],y=p.instance,k=p.currentTarget;if(p=p.listener,y!==c&&s.isPropagationStopped())break e;c=p,s.currentTarget=k;try{c(s)}catch(N){Vr(N)}s.currentTarget=null,c=y}else for(h=0;h<a.length;h++){if(p=a[h],y=p.instance,k=p.currentTarget,p=p.listener,y!==c&&s.isPropagationStopped())break e;c=p,s.currentTarget=k;try{c(s)}catch(N){Vr(N)}s.currentTarget=null,c=y}}}}function me(e,t){var n=t[eo];n===void 0&&(n=t[eo]=new Set);var a=e+"__bubble";n.has(a)||(Dm(t,e,2,!1),n.add(a))}function Pu(e,t,n){var a=0;t&&(a|=4),Dm(n,e,a,t)}var ts="_reactListening"+Math.random().toString(36).slice(2);function Yu(e){if(!e[ts]){e[ts]=!0,Cf.forEach(function(n){n!=="selectionchange"&&(hb.has(n)||Pu(n,!1,e),Pu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ts]||(t[ts]=!0,Pu("selectionchange",!1,t))}}function Dm(e,t,n,a){switch(ag(t)){case 2:var s=Hb;break;case 8:s=Vb;break;default:s=ac}n=s.bind(null,t,n,e),s=void 0,!fo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),a?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Iu(e,t,n,a,s){var c=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var h=a.tag;if(h===3||h===4){var p=a.stateNode.containerInfo;if(p===s)break;if(h===4)for(h=a.return;h!==null;){var y=h.tag;if((y===3||y===4)&&h.stateNode.containerInfo===s)return;h=h.return}for(;p!==null;){if(h=Ni(p),h===null)return;if(y=h.tag,y===5||y===6||y===26||y===27){a=c=h;continue e}p=p.parentNode}}a=a.return}Vf(function(){var k=c,N=uo(n),V=[];e:{var j=gd.get(e);if(j!==void 0){var U=gr,le=e;switch(e){case"keypress":if(hr(n)===0)break e;case"keydown":case"keyup":U=sy;break;case"focusin":le="focus",U=po;break;case"focusout":le="blur",U=po;break;case"beforeblur":case"afterblur":U=po;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Yf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=Kv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=cy;break;case fd:case dd:case hd:U=Fv;break;case md:U=dy;break;case"scroll":case"scrollend":U=Gv;break;case"wheel":U=my;break;case"copy":case"cut":case"paste":U=ey;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=$f;break;case"toggle":case"beforetoggle":U=py}var te=(t&4)!==0,Ce=!te&&(e==="scroll"||e==="scrollend"),A=te?j!==null?j+"Capture":null:j;te=[];for(var E=k,R;E!==null;){var B=E;if(R=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||R===null||A===null||(B=Va(E,A),B!=null&&te.push(wl(E,B,R))),Ce)break;E=E.return}0<te.length&&(j=new U(j,le,null,n,N),V.push({event:j,listeners:te}))}}if((t&7)===0){e:{if(j=e==="mouseover"||e==="pointerover",U=e==="mouseout"||e==="pointerout",j&&n!==oo&&(le=n.relatedTarget||n.fromElement)&&(Ni(le)||le[zi]))break e;if((U||j)&&(j=N.window===N?N:(j=N.ownerDocument)?j.defaultView||j.parentWindow:window,U?(le=n.relatedTarget||n.toElement,U=k,le=le?Ni(le):null,le!==null&&(Ce=f(le),te=le.tag,le!==Ce||te!==5&&te!==27&&te!==6)&&(le=null)):(U=null,le=k),U!==le)){if(te=Yf,B="onMouseLeave",A="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(te=$f,B="onPointerLeave",A="onPointerEnter",E="pointer"),Ce=U==null?j:Ha(U),R=le==null?j:Ha(le),j=new te(B,E+"leave",U,n,N),j.target=Ce,j.relatedTarget=R,B=null,Ni(N)===k&&(te=new te(A,E+"enter",le,n,N),te.target=R,te.relatedTarget=Ce,B=te),Ce=B,U&&le)t:{for(te=U,A=le,E=0,R=te;R;R=va(R))E++;for(R=0,B=A;B;B=va(B))R++;for(;0<E-R;)te=va(te),E--;for(;0<R-E;)A=va(A),R--;for(;E--;){if(te===A||A!==null&&te===A.alternate)break t;te=va(te),A=va(A)}te=null}else te=null;U!==null&&Lm(V,j,U,te,!1),le!==null&&Ce!==null&&Lm(V,Ce,le,te,!0)}}e:{if(j=k?Ha(k):window,U=j.nodeName&&j.nodeName.toLowerCase(),U==="select"||U==="input"&&j.type==="file")var K=ed;else if(Ff(j))if(td)K=Ty;else{K=xy;var de=wy}else U=j.nodeName,!U||U.toLowerCase()!=="input"||j.type!=="checkbox"&&j.type!=="radio"?k&&so(k.elementType)&&(K=ed):K=Ay;if(K&&(K=K(e,k))){Jf(V,K,n,N);break e}de&&de(e,j,k),e==="focusout"&&k&&j.type==="number"&&k.memoizedProps.value!=null&&ro(j,"number",j.value)}switch(de=k?Ha(k):window,e){case"focusin":(Ff(de)||de.contentEditable==="true")&&(Gi=de,Eo=k,Ka=null);break;case"focusout":Ka=Eo=Gi=null;break;case"mousedown":wo=!0;break;case"contextmenu":case"mouseup":case"dragend":wo=!1,ud(V,n,N);break;case"selectionchange":if(Oy)break;case"keydown":case"keyup":ud(V,n,N)}var ee;if(yo)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else $i?Wf(e,n)&&(ne="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ne="onCompositionStart");ne&&(Gf&&n.locale!=="ko"&&($i||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&$i&&(ee=Qf()):(Nn=N,ho="value"in Nn?Nn.value:Nn.textContent,$i=!0)),de=ns(k,ne),0<de.length&&(ne=new If(ne,e,null,n,N),V.push({event:ne,listeners:de}),ee?ne.data=ee:(ee=Zf(n),ee!==null&&(ne.data=ee)))),(ee=yy?by(e,n):Sy(e,n))&&(ne=ns(k,"onBeforeInput"),0<ne.length&&(de=new If("onBeforeInput","beforeinput",null,n,N),V.push({event:de,listeners:ne}),de.data=ee)),cb(V,e,k,n,N)}Um(V,t)})}function wl(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ns(e,t){for(var n=t+"Capture",a=[];e!==null;){var s=e,c=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||c===null||(s=Va(e,n),s!=null&&a.unshift(wl(e,s,c)),s=Va(e,t),s!=null&&a.push(wl(e,s,c))),e.tag===3)return a;e=e.return}return[]}function va(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Lm(e,t,n,a,s){for(var c=t._reactName,h=[];n!==null&&n!==a;){var p=n,y=p.alternate,k=p.stateNode;if(p=p.tag,y!==null&&y===a)break;p!==5&&p!==26&&p!==27||k===null||(y=k,s?(k=Va(n,c),k!=null&&h.unshift(wl(n,k,y))):s||(k=Va(n,c),k!=null&&h.push(wl(n,k,y)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var mb=/\r\n?/g,gb=/\u0000|\uFFFD/g;function zm(e){return(typeof e=="string"?e:""+e).replace(mb,`
`).replace(gb,"")}function Nm(e,t){return t=zm(t),zm(e)===t}function is(){}function Te(e,t,n,a,s,c){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Pi(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Pi(e,""+a);break;case"className":or(e,"class",a);break;case"tabIndex":or(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":or(e,n,a);break;case"style":Bf(e,a,c);break;case"data":if(t!=="object"){or(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=fr(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&Te(e,t,"name",s.name,s,null),Te(e,t,"formEncType",s.formEncType,s,null),Te(e,t,"formMethod",s.formMethod,s,null),Te(e,t,"formTarget",s.formTarget,s,null)):(Te(e,t,"encType",s.encType,s,null),Te(e,t,"method",s.method,s,null),Te(e,t,"target",s.target,s,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=fr(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=is);break;case"onScroll":a!=null&&me("scroll",e);break;case"onScrollEnd":a!=null&&me("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=fr(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":me("beforetoggle",e),me("toggle",e),sr(e,"popover",a);break;case"xlinkActuate":gn(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":gn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":gn(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":gn(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":gn(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":gn(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":gn(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":gn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":gn(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":sr(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Iv.get(n)||n,sr(e,n,a))}}function $u(e,t,n,a,s,c){switch(n){case"style":Bf(e,a,c);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=n}}break;case"children":typeof a=="string"?Pi(e,a):(typeof a=="number"||typeof a=="bigint")&&Pi(e,""+a);break;case"onScroll":a!=null&&me("scroll",e);break;case"onScrollEnd":a!=null&&me("scrollend",e);break;case"onClick":a!=null&&(e.onclick=is);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Of.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),t=n.slice(2,s?n.length-7:void 0),c=e[_t]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,s),typeof a=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,s);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):sr(e,n,a)}}}function ot(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":me("error",e),me("load",e);var a=!1,s=!1,c;for(c in n)if(n.hasOwnProperty(c)){var h=n[c];if(h!=null)switch(c){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Te(e,t,c,h,n,null)}}s&&Te(e,t,"srcSet",n.srcSet,n,null),a&&Te(e,t,"src",n.src,n,null);return;case"input":me("invalid",e);var p=c=h=s=null,y=null,k=null;for(a in n)if(n.hasOwnProperty(a)){var N=n[a];if(N!=null)switch(a){case"name":s=N;break;case"type":h=N;break;case"checked":y=N;break;case"defaultChecked":k=N;break;case"value":c=N;break;case"defaultValue":p=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(o(137,t));break;default:Te(e,t,a,N,n,null)}}Lf(e,c,p,y,k,h,s,!1),ur(e);return;case"select":me("invalid",e),a=h=c=null;for(s in n)if(n.hasOwnProperty(s)&&(p=n[s],p!=null))switch(s){case"value":c=p;break;case"defaultValue":h=p;break;case"multiple":a=p;default:Te(e,t,s,p,n,null)}t=c,n=h,e.multiple=!!a,t!=null?Qi(e,!!a,t,!1):n!=null&&Qi(e,!!a,n,!0);return;case"textarea":me("invalid",e),c=s=a=null;for(h in n)if(n.hasOwnProperty(h)&&(p=n[h],p!=null))switch(h){case"value":a=p;break;case"defaultValue":s=p;break;case"children":c=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(91));break;default:Te(e,t,h,p,n,null)}Nf(e,a,s,c),ur(e);return;case"option":for(y in n)if(n.hasOwnProperty(y)&&(a=n[y],a!=null))switch(y){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Te(e,t,y,a,n,null)}return;case"dialog":me("beforetoggle",e),me("toggle",e),me("cancel",e),me("close",e);break;case"iframe":case"object":me("load",e);break;case"video":case"audio":for(a=0;a<El.length;a++)me(El[a],e);break;case"image":me("error",e),me("load",e);break;case"details":me("toggle",e);break;case"embed":case"source":case"link":me("error",e),me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(k in n)if(n.hasOwnProperty(k)&&(a=n[k],a!=null))switch(k){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Te(e,t,k,a,n,null)}return;default:if(so(t)){for(N in n)n.hasOwnProperty(N)&&(a=n[N],a!==void 0&&$u(e,t,N,a,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(a=n[p],a!=null&&Te(e,t,p,a,n,null))}function pb(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,c=null,h=null,p=null,y=null,k=null,N=null;for(U in n){var V=n[U];if(n.hasOwnProperty(U)&&V!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":y=V;default:a.hasOwnProperty(U)||Te(e,t,U,null,a,V)}}for(var j in a){var U=a[j];if(V=n[j],a.hasOwnProperty(j)&&(U!=null||V!=null))switch(j){case"type":c=U;break;case"name":s=U;break;case"checked":k=U;break;case"defaultChecked":N=U;break;case"value":h=U;break;case"defaultValue":p=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(o(137,t));break;default:U!==V&&Te(e,t,j,U,a,V)}}lo(e,h,p,y,k,N,c,s);return;case"select":U=h=p=j=null;for(c in n)if(y=n[c],n.hasOwnProperty(c)&&y!=null)switch(c){case"value":break;case"multiple":U=y;default:a.hasOwnProperty(c)||Te(e,t,c,null,a,y)}for(s in a)if(c=a[s],y=n[s],a.hasOwnProperty(s)&&(c!=null||y!=null))switch(s){case"value":j=c;break;case"defaultValue":p=c;break;case"multiple":h=c;default:c!==y&&Te(e,t,s,c,a,y)}t=p,n=h,a=U,j!=null?Qi(e,!!n,j,!1):!!a!=!!n&&(t!=null?Qi(e,!!n,t,!0):Qi(e,!!n,n?[]:"",!1));return;case"textarea":U=j=null;for(p in n)if(s=n[p],n.hasOwnProperty(p)&&s!=null&&!a.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Te(e,t,p,null,a,s)}for(h in a)if(s=a[h],c=n[h],a.hasOwnProperty(h)&&(s!=null||c!=null))switch(h){case"value":j=s;break;case"defaultValue":U=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==c&&Te(e,t,h,s,a,c)}zf(e,j,U);return;case"option":for(var le in n)if(j=n[le],n.hasOwnProperty(le)&&j!=null&&!a.hasOwnProperty(le))switch(le){case"selected":e.selected=!1;break;default:Te(e,t,le,null,a,j)}for(y in a)if(j=a[y],U=n[y],a.hasOwnProperty(y)&&j!==U&&(j!=null||U!=null))switch(y){case"selected":e.selected=j&&typeof j!="function"&&typeof j!="symbol";break;default:Te(e,t,y,j,a,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in n)j=n[te],n.hasOwnProperty(te)&&j!=null&&!a.hasOwnProperty(te)&&Te(e,t,te,null,a,j);for(k in a)if(j=a[k],U=n[k],a.hasOwnProperty(k)&&j!==U&&(j!=null||U!=null))switch(k){case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(o(137,t));break;default:Te(e,t,k,j,a,U)}return;default:if(so(t)){for(var Ce in n)j=n[Ce],n.hasOwnProperty(Ce)&&j!==void 0&&!a.hasOwnProperty(Ce)&&$u(e,t,Ce,void 0,a,j);for(N in a)j=a[N],U=n[N],!a.hasOwnProperty(N)||j===U||j===void 0&&U===void 0||$u(e,t,N,j,a,U);return}}for(var A in n)j=n[A],n.hasOwnProperty(A)&&j!=null&&!a.hasOwnProperty(A)&&Te(e,t,A,null,a,j);for(V in a)j=a[V],U=n[V],!a.hasOwnProperty(V)||j===U||j==null&&U==null||Te(e,t,V,j,a,U)}var Gu=null,Xu=null;function as(e){return e.nodeType===9?e:e.ownerDocument}function qm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Bm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ku(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wu=null;function vb(){var e=window.event;return e&&e.type==="popstate"?e===Wu?!1:(Wu=e,!0):(Wu=null,!1)}var Hm=typeof setTimeout=="function"?setTimeout:void 0,yb=typeof clearTimeout=="function"?clearTimeout:void 0,Vm=typeof Promise=="function"?Promise:void 0,bb=typeof queueMicrotask=="function"?queueMicrotask:typeof Vm<"u"?function(e){return Vm.resolve(null).then(e).catch(Sb)}:Hm;function Sb(e){setTimeout(function(){throw e})}function Jn(e){return e==="head"}function Qm(e,t){var n=t,a=0,s=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<a&&8>a){n=a;var h=e.ownerDocument;if(n&1&&xl(h.documentElement),n&2&&xl(h.body),n&4)for(n=h.head,xl(n),h=n.firstChild;h;){var p=h.nextSibling,y=h.nodeName;h[Ba]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=p}}if(s===0){e.removeChild(c),jl(t);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:a=n.charCodeAt(0)-48;else a=0;n=c}while(n);jl(t)}function Zu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Zu(n),to(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function _b(e,t,n,a){for(;e.nodeType===1;){var s=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ba])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Zt(e.nextSibling),e===null)break}return null}function Eb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Zt(e.nextSibling),e===null))return null;return e}function Fu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function wb(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Zt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ju=null;function Pm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Ym(e,t,n){switch(t=as(n),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function xl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);to(e)}var Yt=new Map,Im=new Set;function ls(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Rn=I.d;I.d={f:xb,r:Ab,D:Tb,C:Cb,L:Ob,m:Rb,X:Mb,S:kb,M:jb};function xb(){var e=Rn.f(),t=Wr();return e||t}function Ab(e){var t=qi(e);t!==null&&t.tag===5&&t.type==="form"?fh(t):Rn.r(e)}var ya=typeof document>"u"?null:document;function $m(e,t,n){var a=ya;if(a&&typeof t=="string"&&t){var s=Nt(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),Im.has(s)||(Im.add(s),e={rel:e,crossOrigin:n,href:t},a.querySelector(s)===null&&(t=a.createElement("link"),ot(t,"link",e),et(t),a.head.appendChild(t)))}}function Tb(e){Rn.D(e),$m("dns-prefetch",e,null)}function Cb(e,t){Rn.C(e,t),$m("preconnect",e,t)}function Ob(e,t,n){Rn.L(e,t,n);var a=ya;if(a&&e&&t){var s='link[rel="preload"][as="'+Nt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Nt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Nt(n.imageSizes)+'"]')):s+='[href="'+Nt(e)+'"]';var c=s;switch(t){case"style":c=ba(e);break;case"script":c=Sa(e)}Yt.has(c)||(e=b({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Yt.set(c,e),a.querySelector(s)!==null||t==="style"&&a.querySelector(Al(c))||t==="script"&&a.querySelector(Tl(c))||(t=a.createElement("link"),ot(t,"link",e),et(t),a.head.appendChild(t)))}}function Rb(e,t){Rn.m(e,t);var n=ya;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+Nt(a)+'"][href="'+Nt(e)+'"]',c=s;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Sa(e)}if(!Yt.has(c)&&(e=b({rel:"modulepreload",href:e},t),Yt.set(c,e),n.querySelector(s)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Tl(c)))return}a=n.createElement("link"),ot(a,"link",e),et(a),n.head.appendChild(a)}}}function kb(e,t,n){Rn.S(e,t,n);var a=ya;if(a&&e){var s=Bi(a).hoistableStyles,c=ba(e);t=t||"default";var h=s.get(c);if(!h){var p={loading:0,preload:null};if(h=a.querySelector(Al(c)))p.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Yt.get(c))&&ec(e,n);var y=h=a.createElement("link");et(y),ot(y,"link",e),y._p=new Promise(function(k,N){y.onload=k,y.onerror=N}),y.addEventListener("load",function(){p.loading|=1}),y.addEventListener("error",function(){p.loading|=2}),p.loading|=4,rs(h,t,a)}h={type:"stylesheet",instance:h,count:1,state:p},s.set(c,h)}}}function Mb(e,t){Rn.X(e,t);var n=ya;if(n&&e){var a=Bi(n).hoistableScripts,s=Sa(e),c=a.get(s);c||(c=n.querySelector(Tl(s)),c||(e=b({src:e,async:!0},t),(t=Yt.get(s))&&tc(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(s,c))}}function jb(e,t){Rn.M(e,t);var n=ya;if(n&&e){var a=Bi(n).hoistableScripts,s=Sa(e),c=a.get(s);c||(c=n.querySelector(Tl(s)),c||(e=b({src:e,async:!0,type:"module"},t),(t=Yt.get(s))&&tc(e,t),c=n.createElement("script"),et(c),ot(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(s,c))}}function Gm(e,t,n,a){var s=(s=ae.current)?ls(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ba(n.href),n=Bi(s).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ba(n.href);var c=Bi(s).hoistableStyles,h=c.get(e);if(h||(s=s.ownerDocument||s,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,h),(c=s.querySelector(Al(e)))&&!c._p&&(h.instance=c,h.state.loading=5),Yt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Yt.set(e,n),c||Ub(s,e,n,h.state))),t&&a===null)throw Error(o(528,""));return h}if(t&&a!==null)throw Error(o(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Sa(n),n=Bi(s).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function ba(e){return'href="'+Nt(e)+'"'}function Al(e){return'link[rel="stylesheet"]['+e+"]"}function Xm(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function Ub(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),ot(t,"link",n),et(t),e.head.appendChild(t))}function Sa(e){return'[src="'+Nt(e)+'"]'}function Tl(e){return"script[async]"+e}function Km(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Nt(n.href)+'"]');if(a)return t.instance=a,et(a),a;var s=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),et(a),ot(a,"style",s),rs(a,n.precedence,e),t.instance=a;case"stylesheet":s=ba(n.href);var c=e.querySelector(Al(s));if(c)return t.state.loading|=4,t.instance=c,et(c),c;a=Xm(n),(s=Yt.get(s))&&ec(a,s),c=(e.ownerDocument||e).createElement("link"),et(c);var h=c;return h._p=new Promise(function(p,y){h.onload=p,h.onerror=y}),ot(c,"link",a),t.state.loading|=4,rs(c,n.precedence,e),t.instance=c;case"script":return c=Sa(n.src),(s=e.querySelector(Tl(c)))?(t.instance=s,et(s),s):(a=n,(s=Yt.get(c))&&(a=b({},n),tc(a,s)),e=e.ownerDocument||e,s=e.createElement("script"),et(s),ot(s,"link",a),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,rs(a,n.precedence,e));return t.instance}function rs(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=a.length?a[a.length-1]:null,c=s,h=0;h<a.length;h++){var p=a[h];if(p.dataset.precedence===t)c=p;else if(c!==s)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function ec(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function tc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ss=null;function Wm(e,t,n){if(ss===null){var a=new Map,s=ss=new Map;s.set(n,a)}else s=ss,a=s.get(n),a||(a=new Map,s.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),s=0;s<n.length;s++){var c=n[s];if(!(c[Ba]||c[ct]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var h=c.getAttribute(t)||"";h=e+h;var p=a.get(h);p?p.push(c):a.set(h,[c])}}return a}function Zm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Db(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Fm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Cl=null;function Lb(){}function zb(e,t,n){if(Cl===null)throw Error(o(475));var a=Cl;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=ba(n.href),c=e.querySelector(Al(s));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=os.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=c,et(c);return}c=e.ownerDocument||e,n=Xm(n),(s=Yt.get(s))&&ec(n,s),c=c.createElement("link"),et(c);var h=c;h._p=new Promise(function(p,y){h.onload=p,h.onerror=y}),ot(c,"link",n),t.instance=c}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=os.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Nb(){if(Cl===null)throw Error(o(475));var e=Cl;return e.stylesheets&&e.count===0&&nc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&nc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function os(){if(this.count--,this.count===0){if(this.stylesheets)nc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var us=null;function nc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,us=new Map,t.forEach(qb,e),us=null,os.call(e))}function qb(e,t){if(!(t.state.loading&4)){var n=us.get(e);if(n)var a=n.get(null);else{n=new Map,us.set(e,n);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<s.length;c++){var h=s[c];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),a=h)}a&&n.set(null,a)}s=t.instance,h=s.getAttribute("data-precedence"),c=n.get(h)||a,c===a&&n.set(null,s),n.set(h,s),this.count++,a=os.bind(this),s.addEventListener("load",a),s.addEventListener("error",a),c?c.parentNode.insertBefore(s,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var Ol={$$typeof:Y,Provider:null,Consumer:null,_currentValue:W,_currentValue2:W,_threadCount:0};function Bb(e,t,n,a,s,c,h,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.hiddenUpdates=Zs(null),this.identifierPrefix=a,this.onUncaughtError=s,this.onCaughtError=c,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function Jm(e,t,n,a,s,c,h,p,y,k,N,V){return e=new Bb(e,t,n,h,p,y,k,V),t=1,c===!0&&(t|=24),c=Ot(3,null,null,t),e.current=c,c.stateNode=e,t=No(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:a,isDehydrated:n,cache:t},Vo(c),e}function eg(e){return e?(e=Zi,e):Zi}function tg(e,t,n,a,s,c){s=eg(s),a.context===null?a.context=s:a.pendingContext=s,a=Hn(t),a.payload={element:n},c=c===void 0?null:c,c!==null&&(a.callback=c),n=Vn(e,a,t),n!==null&&(Ut(n,e,t),al(n,e,t))}function ng(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ic(e,t){ng(e,t),(e=e.alternate)&&ng(e,t)}function ig(e){if(e.tag===13){var t=Wi(e,67108864);t!==null&&Ut(t,e,67108864),ic(e,67108864)}}var cs=!0;function Hb(e,t,n,a){var s=z.T;z.T=null;var c=I.p;try{I.p=2,ac(e,t,n,a)}finally{I.p=c,z.T=s}}function Vb(e,t,n,a){var s=z.T;z.T=null;var c=I.p;try{I.p=8,ac(e,t,n,a)}finally{I.p=c,z.T=s}}function ac(e,t,n,a){if(cs){var s=lc(a);if(s===null)Iu(e,t,a,fs,n),lg(e,a);else if(Pb(s,e,t,n,a))a.stopPropagation();else if(lg(e,a),t&4&&-1<Qb.indexOf(e)){for(;s!==null;){var c=qi(s);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var h=mi(c.pendingLanes);if(h!==0){var p=c;for(p.pendingLanes|=2,p.entangledLanes|=2;h;){var y=1<<31-lt(h);p.entanglements[1]|=y,h&=~y}un(c),(we&6)===0&&(Xr=He()+500,_l(0))}}break;case 13:p=Wi(c,2),p!==null&&Ut(p,c,2),Wr(),ic(c,2)}if(c=lc(a),c===null&&Iu(e,t,a,fs,n),c===s)break;s=c}s!==null&&a.stopPropagation()}else Iu(e,t,a,null,n)}}function lc(e){return e=uo(e),rc(e)}var fs=null;function rc(e){if(fs=null,e=Ni(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return fs=e,null}function ag(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ln()){case $e:return 2;case Je:return 8;case bt:case nr:return 32;case za:return 268435456;default:return 32}default:return 32}}var sc=!1,ei=null,ti=null,ni=null,Rl=new Map,kl=new Map,ii=[],Qb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lg(e,t){switch(e){case"focusin":case"focusout":ei=null;break;case"dragenter":case"dragleave":ti=null;break;case"mouseover":case"mouseout":ni=null;break;case"pointerover":case"pointerout":Rl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":kl.delete(t.pointerId)}}function Ml(e,t,n,a,s,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:c,targetContainers:[s]},t!==null&&(t=qi(t),t!==null&&ig(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Pb(e,t,n,a,s){switch(t){case"focusin":return ei=Ml(ei,e,t,n,a,s),!0;case"dragenter":return ti=Ml(ti,e,t,n,a,s),!0;case"mouseover":return ni=Ml(ni,e,t,n,a,s),!0;case"pointerover":var c=s.pointerId;return Rl.set(c,Ml(Rl.get(c)||null,e,t,n,a,s)),!0;case"gotpointercapture":return c=s.pointerId,kl.set(c,Ml(kl.get(c)||null,e,t,n,a,s)),!0}return!1}function rg(e){var t=Ni(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,zv(e.priority,function(){if(n.tag===13){var a=jt();a=Fs(a);var s=Wi(n,a);s!==null&&Ut(s,n,a),ic(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ds(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=lc(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);oo=a,n.target.dispatchEvent(a),oo=null}else return t=qi(n),t!==null&&ig(t),e.blockedOn=n,!1;t.shift()}return!0}function sg(e,t,n){ds(e)&&n.delete(t)}function Yb(){sc=!1,ei!==null&&ds(ei)&&(ei=null),ti!==null&&ds(ti)&&(ti=null),ni!==null&&ds(ni)&&(ni=null),Rl.forEach(sg),kl.forEach(sg)}function hs(e,t){e.blockedOn===t&&(e.blockedOn=null,sc||(sc=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Yb)))}var ms=null;function og(e){ms!==e&&(ms=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){ms===e&&(ms=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],s=e[t+2];if(typeof a!="function"){if(rc(a||n)===null)continue;break}var c=qi(n);c!==null&&(e.splice(t,3),t-=3,ru(c,{pending:!0,data:s,method:n.method,action:a},a,s))}}))}function jl(e){function t(y){return hs(y,e)}ei!==null&&hs(ei,e),ti!==null&&hs(ti,e),ni!==null&&hs(ni,e),Rl.forEach(t),kl.forEach(t);for(var n=0;n<ii.length;n++){var a=ii[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<ii.length&&(n=ii[0],n.blockedOn===null);)rg(n),n.blockedOn===null&&ii.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var s=n[a],c=n[a+1],h=s[_t]||null;if(typeof c=="function")h||og(n);else if(h){var p=null;if(c&&c.hasAttribute("formAction")){if(s=c,h=c[_t]||null)p=h.formAction;else if(rc(s)!==null)continue}else p=h.action;typeof p=="function"?n[a+1]=p:(n.splice(a,3),a-=3),og(n)}}}function oc(e){this._internalRoot=e}gs.prototype.render=oc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var n=t.current,a=jt();tg(n,a,e,t,null,null)},gs.prototype.unmount=oc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tg(e.current,2,null,e,null,null),Wr(),t[zi]=null}};function gs(e){this._internalRoot=e}gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Af();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ii.length&&t!==0&&t<ii[n].priority;n++);ii.splice(n,0,e),n===0&&rg(e)}};var ug=l.version;if(ug!=="19.1.0")throw Error(o(527,ug,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=m(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var Ib={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ps=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ps.isDisabled&&ps.supportsFiber)try{gt=ps.inject(Ib),at=ps}catch{}}return Dl.createRoot=function(e,t){if(!u(e))throw Error(o(299));var n=!1,a="",s=Ah,c=Th,h=Ch,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=Jm(e,1,!1,null,null,n,a,s,c,h,p,null),e[zi]=t.current,Yu(e),new oc(t)},Dl.hydrateRoot=function(e,t,n){if(!u(e))throw Error(o(299));var a=!1,s="",c=Ah,h=Th,p=Ch,y=null,k=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(y=n.unstable_transitionCallbacks),n.formState!==void 0&&(k=n.formState)),t=Jm(e,1,!0,t,n??null,a,s,c,h,p,y,k),t.context=eg(null),n=t.current,a=jt(),a=Fs(a),s=Hn(a),s.callback=null,Vn(n,s,a),n=a,t.current.lanes=n,qa(t,n),un(t),e[zi]=t.current,Yu(e),new gs(t)},Dl.version="19.1.0",Dl}var bg;function t0(){if(bg)return fc.exports;bg=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),fc.exports=e0(),fc.exports}var n0=t0(),dn=[],$t=[],i0=Uint8Array,gc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var _a=0,a0=gc.length;_a<a0;++_a)dn[_a]=gc[_a],$t[gc.charCodeAt(_a)]=_a;$t[45]=62;$t[95]=63;function l0(i){var l=i.length;if(l%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=i.indexOf("=");r===-1&&(r=l);var o=r===l?0:4-r%4;return[r,o]}function r0(i,l,r){return(l+r)*3/4-r}function Pl(i){var l,r=l0(i),o=r[0],u=r[1],f=new i0(r0(i,o,u)),d=0,v=u>0?o-4:o,m;for(m=0;m<v;m+=4)l=$t[i.charCodeAt(m)]<<18|$t[i.charCodeAt(m+1)]<<12|$t[i.charCodeAt(m+2)]<<6|$t[i.charCodeAt(m+3)],f[d++]=l>>16&255,f[d++]=l>>8&255,f[d++]=l&255;return u===2&&(l=$t[i.charCodeAt(m)]<<2|$t[i.charCodeAt(m+1)]>>4,f[d++]=l&255),u===1&&(l=$t[i.charCodeAt(m)]<<10|$t[i.charCodeAt(m+1)]<<4|$t[i.charCodeAt(m+2)]>>2,f[d++]=l>>8&255,f[d++]=l&255),f}function s0(i){return dn[i>>18&63]+dn[i>>12&63]+dn[i>>6&63]+dn[i&63]}function o0(i,l,r){for(var o,u=[],f=l;f<r;f+=3)o=(i[f]<<16&16711680)+(i[f+1]<<8&65280)+(i[f+2]&255),u.push(s0(o));return u.join("")}function Yl(i){for(var l,r=i.length,o=r%3,u=[],f=16383,d=0,v=r-o;d<v;d+=f)u.push(o0(i,d,d+f>v?v:d+f));return o===1?(l=i[r-1],u.push(dn[l>>2]+dn[l<<4&63]+"==")):o===2&&(l=(i[r-2]<<8)+i[r-1],u.push(dn[l>>10]+dn[l>>4&63]+dn[l<<2&63]+"=")),u.join("")}function Mn(i){if(i===void 0)return{};if(!mp(i))throw new Error(`The arguments to a Convex function must be an object. Received: ${i}`);return i}function u0(i){if(typeof i>"u")throw new Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");if(typeof i!="string")throw new Error(`Invalid deployment address: found ${i}".`);if(!(i.startsWith("http:")||i.startsWith("https:")))throw new Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${i}".`);try{new URL(i)}catch{throw new Error(`Invalid deployment address: "${i}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}if(i.endsWith(".convex.site"))throw new Error(`Invalid deployment address: "${i}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}function mp(i){const l=typeof i=="object",r=Object.getPrototypeOf(i),o=r===null||r===Object.prototype||r?.constructor?.name==="Object";return l&&o}const gp=!0,Oa=BigInt("-9223372036854775808"),sf=BigInt("9223372036854775807"),Hc=BigInt("0"),c0=BigInt("8"),f0=BigInt("256");function pp(i){return Number.isNaN(i)||!Number.isFinite(i)||Object.is(i,-0)}function d0(i){i<Hc&&(i-=Oa+Oa);let l=i.toString(16);l.length%2===1&&(l="0"+l);const r=new Uint8Array(new ArrayBuffer(8));let o=0;for(const u of l.match(/.{2}/g).reverse())r.set([parseInt(u,16)],o++),i>>=c0;return Yl(r)}function h0(i){const l=Pl(i);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);let r=Hc,o=Hc;for(const u of l)r+=BigInt(u)*f0**o,o++;return r>sf&&(r+=Oa+Oa),r}function m0(i){if(i<Oa||sf<i)throw new Error(`BigInt ${i} does not fit into a 64-bit signed integer.`);const l=new ArrayBuffer(8);return new DataView(l).setBigInt64(0,i,!0),Yl(new Uint8Array(l))}function g0(i){const l=Pl(i);if(l.byteLength!==8)throw new Error(`Received ${l.byteLength} bytes, expected 8 for $integer`);return new DataView(l.buffer).getBigInt64(0,!0)}const p0=DataView.prototype.setBigInt64?m0:d0,v0=DataView.prototype.getBigInt64?g0:h0,Sg=1024;function vp(i){if(i.length>Sg)throw new Error(`Field name ${i} exceeds maximum field name length ${Sg}.`);if(i.startsWith("$"))throw new Error(`Field name ${i} starts with a '$', which is reserved.`);for(let l=0;l<i.length;l+=1){const r=i.charCodeAt(l);if(r<32||r>=127)throw new Error(`Field name ${i} has invalid character '${i[l]}': Field names can only contain non-control ASCII characters`)}}function Ra(i){if(i===null||typeof i=="boolean"||typeof i=="number"||typeof i=="string")return i;if(Array.isArray(i))return i.map(o=>Ra(o));if(typeof i!="object")throw new Error(`Unexpected type of ${i}`);const l=Object.entries(i);if(l.length===1){const o=l[0][0];if(o==="$bytes"){if(typeof i.$bytes!="string")throw new Error(`Malformed $bytes field on ${i}`);return Pl(i.$bytes).buffer}if(o==="$integer"){if(typeof i.$integer!="string")throw new Error(`Malformed $integer field on ${i}`);return v0(i.$integer)}if(o==="$float"){if(typeof i.$float!="string")throw new Error(`Malformed $float field on ${i}`);const u=Pl(i.$float);if(u.byteLength!==8)throw new Error(`Received ${u.byteLength} bytes, expected 8 for $float`);const d=new DataView(u.buffer).getFloat64(0,gp);if(!pp(d))throw new Error(`Float ${d} should be encoded as a number`);return d}if(o==="$set")throw new Error("Received a Set which is no longer supported as a Convex type.");if(o==="$map")throw new Error("Received a Map which is no longer supported as a Convex type.")}const r={};for(const[o,u]of Object.entries(i))vp(o),r[o]=Ra(u);return r}function ql(i){return JSON.stringify(i,(l,r)=>r===void 0?"undefined":typeof r=="bigint"?`${r.toString()}n`:r)}function Vc(i,l,r,o){if(i===void 0){const d=r&&` (present at path ${r} in original object ${ql(l)})`;throw new Error(`undefined is not a valid Convex value${d}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`)}if(i===null)return i;if(typeof i=="bigint"){if(i<Oa||sf<i)throw new Error(`BigInt ${i} does not fit into a 64-bit signed integer.`);return{$integer:p0(i)}}if(typeof i=="number")if(pp(i)){const d=new ArrayBuffer(8);return new DataView(d).setFloat64(0,i,gp),{$float:Yl(new Uint8Array(d))}}else return i;if(typeof i=="boolean"||typeof i=="string")return i;if(i instanceof ArrayBuffer)return{$bytes:Yl(new Uint8Array(i))};if(Array.isArray(i))return i.map((d,v)=>Vc(d,l,r+`[${v}]`));if(i instanceof Set)throw new Error(pc(r,"Set",[...i],l));if(i instanceof Map)throw new Error(pc(r,"Map",[...i],l));if(!mp(i)){const d=i?.constructor?.name,v=d?`${d} `:"";throw new Error(pc(r,v,i,l))}const u={},f=Object.entries(i);f.sort(([d,v],[m,g])=>d===m?0:d<m?-1:1);for(const[d,v]of f)v!==void 0&&(vp(d),u[d]=Vc(v,l,r+`.${d}`));return u}function pc(i,l,r,o){return i?`${l}${ql(r)} is not a supported Convex type (present at path ${i} in original object ${ql(o)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`:`${l}${ql(r)} is not a supported Convex type.`}function ci(i){return Vc(i,i,"")}var y0=Object.defineProperty,b0=(i,l,r)=>l in i?y0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,vc=(i,l,r)=>b0(i,typeof l!="symbol"?l+"":l,r),_g,Eg;const S0=Symbol.for("ConvexError");class Qc extends(Eg=Error,_g=S0,Eg){constructor(l){super(typeof l=="string"?l:ql(l)),vc(this,"name","ConvexError"),vc(this,"data"),vc(this,_g,!0),this.data=l}}const yp=()=>Array.from({length:4},()=>0);yp();yp();const wg="1.25.0";var _0=Object.defineProperty,E0=(i,l,r)=>l in i?_0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,xg=(i,l,r)=>E0(i,typeof l!="symbol"?l+"":l,r);const w0="color:rgb(0, 145, 255)";function bp(i){switch(i){case"query":return"Q";case"mutation":return"M";case"action":return"A";case"any":return"?"}}class Sp{constructor(l){xg(this,"_onLogLineFuncs"),xg(this,"_verbose"),this._onLogLineFuncs={},this._verbose=l.verbose}addLogLineListener(l){let r=Math.random().toString(36).substring(2,15);for(let o=0;o<10&&this._onLogLineFuncs[r]!==void 0;o++)r=Math.random().toString(36).substring(2,15);return this._onLogLineFuncs[r]=l,()=>{delete this._onLogLineFuncs[r]}}logVerbose(...l){if(this._verbose)for(const r of Object.values(this._onLogLineFuncs))r("debug",`${new Date().toISOString()}`,...l)}log(...l){for(const r of Object.values(this._onLogLineFuncs))r("info",...l)}warn(...l){for(const r of Object.values(this._onLogLineFuncs))r("warn",...l)}error(...l){for(const r of Object.values(this._onLogLineFuncs))r("error",...l)}}function _p(i){const l=new Sp(i);return l.addLogLineListener((r,...o)=>{switch(r){case"debug":console.debug(...o);break;case"info":console.log(...o);break;case"warn":console.warn(...o);break;case"error":console.error(...o);break;default:console.log(...o)}}),l}function Ep(i){return new Sp(i)}function Ds(i,l,r,o,u){const f=bp(r);if(typeof u=="object"&&(u=`ConvexError ${JSON.stringify(u.errorData,null,2)}`),l==="info"){const d=u.match(/^\[.*?\] /);if(d===null){i.error(`[CONVEX ${f}(${o})] Could not parse console.log`);return}const v=u.slice(1,d[0].length-2),m=u.slice(d[0].length);i.log(`%c[CONVEX ${f}(${o})] [${v}]`,w0,m)}else i.error(`[CONVEX ${f}(${o})] ${u}`)}function x0(i,l){const r=`[CONVEX FATAL ERROR] ${l}`;return i.error(r),new Error(r)}function Ta(i,l,r){return`[CONVEX ${bp(i)}(${l})] ${r.errorMessage}
  Called by client`}function Pc(i,l){return l.data=i.errorData,l}function Ls(i){const l=i.split(":");let r,o;return l.length===1?(r=l[0],o="default"):(r=l.slice(0,l.length-1).join(":"),o=l[l.length-1]),r.endsWith(".js")&&(r=r.slice(0,-3)),`${r}:${o}`}function Li(i,l){return JSON.stringify({udfPath:Ls(i),args:ci(l)})}var A0=Object.defineProperty,T0=(i,l,r)=>l in i?A0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,cn=(i,l,r)=>T0(i,typeof l!="symbol"?l+"":l,r);class C0{constructor(){cn(this,"nextQueryId"),cn(this,"querySetVersion"),cn(this,"querySet"),cn(this,"queryIdToToken"),cn(this,"identityVersion"),cn(this,"auth"),cn(this,"outstandingQueriesOlderThanRestart"),cn(this,"outstandingAuthOlderThanRestart"),cn(this,"paused"),cn(this,"pendingQuerySetModifications"),this.nextQueryId=0,this.querySetVersion=0,this.identityVersion=0,this.querySet=new Map,this.queryIdToToken=new Map,this.outstandingQueriesOlderThanRestart=new Set,this.outstandingAuthOlderThanRestart=!1,this.paused=!1,this.pendingQuerySetModifications=new Map}hasSyncedPastLastReconnect(){return this.outstandingQueriesOlderThanRestart.size===0&&!this.outstandingAuthOlderThanRestart}markAuthCompletion(){this.outstandingAuthOlderThanRestart=!1}subscribe(l,r,o,u){const f=Ls(l),d=Li(f,r),v=this.querySet.get(d);if(v!==void 0)return v.numSubscribers+=1,{queryToken:d,modification:null,unsubscribe:()=>this.removeSubscriber(d)};{const m=this.nextQueryId++,g={id:m,canonicalizedUdfPath:f,args:r,numSubscribers:1,journal:o,componentPath:u};this.querySet.set(d,g),this.queryIdToToken.set(m,d);const b=this.querySetVersion,C=this.querySetVersion+1,S={type:"Add",queryId:m,udfPath:f,args:[ci(r)],journal:o,componentPath:u};return this.paused?this.pendingQuerySetModifications.set(m,S):this.querySetVersion=C,{queryToken:d,modification:{type:"ModifyQuerySet",baseVersion:b,newVersion:C,modifications:[S]},unsubscribe:()=>this.removeSubscriber(d)}}}transition(l){for(const r of l.modifications)switch(r.type){case"QueryUpdated":case"QueryFailed":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);const o=r.journal;if(o!==void 0){const u=this.queryIdToToken.get(r.queryId);u!==void 0&&(this.querySet.get(u).journal=o)}break}case"QueryRemoved":{this.outstandingQueriesOlderThanRestart.delete(r.queryId);break}default:throw new Error(`Invalid modification ${r.type}`)}}queryId(l,r){const o=Ls(l),u=Li(o,r),f=this.querySet.get(u);return f!==void 0?f.id:null}isCurrentOrNewerAuthVersion(l){return l>=this.identityVersion}setAuth(l){this.auth={tokenType:"User",value:l};const r=this.identityVersion;return this.paused||(this.identityVersion=r+1),{type:"Authenticate",baseVersion:r,...this.auth}}setAdminAuth(l,r){const o={tokenType:"Admin",value:l,impersonating:r};this.auth=o;const u=this.identityVersion;return this.paused||(this.identityVersion=u+1),{type:"Authenticate",baseVersion:u,...o}}clearAuth(){this.auth=void 0,this.markAuthCompletion();const l=this.identityVersion;return this.paused||(this.identityVersion=l+1),{type:"Authenticate",tokenType:"None",baseVersion:l}}hasAuth(){return!!this.auth}isNewAuth(l){return this.auth?.value!==l}queryPath(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).canonicalizedUdfPath:null}queryArgs(l){const r=this.queryIdToToken.get(l);return r?this.querySet.get(r).args:null}queryToken(l){return this.queryIdToToken.get(l)??null}queryJournal(l){return this.querySet.get(l)?.journal}restart(l){this.unpause(),this.outstandingQueriesOlderThanRestart.clear();const r=[];for(const f of this.querySet.values()){const d={type:"Add",queryId:f.id,udfPath:f.canonicalizedUdfPath,args:[ci(f.args)],journal:f.journal,componentPath:f.componentPath};r.push(d),l.has(f.id)||this.outstandingQueriesOlderThanRestart.add(f.id)}this.querySetVersion=1;const o={type:"ModifyQuerySet",baseVersion:0,newVersion:1,modifications:r};if(!this.auth)return this.identityVersion=0,[o,void 0];this.outstandingAuthOlderThanRestart=!0;const u={type:"Authenticate",baseVersion:0,...this.auth};return this.identityVersion=1,[o,u]}pause(){this.paused=!0}resume(){const l=this.pendingQuerySetModifications.size>0?{type:"ModifyQuerySet",baseVersion:this.querySetVersion,newVersion:++this.querySetVersion,modifications:Array.from(this.pendingQuerySetModifications.values())}:void 0,r=this.auth!==void 0?{type:"Authenticate",baseVersion:this.identityVersion++,...this.auth}:void 0;return this.unpause(),[l,r]}unpause(){this.paused=!1,this.pendingQuerySetModifications.clear()}removeSubscriber(l){const r=this.querySet.get(l);if(r.numSubscribers>1)return r.numSubscribers-=1,null;{this.querySet.delete(l),this.queryIdToToken.delete(r.id),this.outstandingQueriesOlderThanRestart.delete(r.id);const o=this.querySetVersion,u=this.querySetVersion+1,f={type:"Remove",queryId:r.id};return this.paused?this.pendingQuerySetModifications.has(r.id)?this.pendingQuerySetModifications.delete(r.id):this.pendingQuerySetModifications.set(r.id,f):this.querySetVersion=u,{type:"ModifyQuerySet",baseVersion:o,newVersion:u,modifications:[f]}}}}var O0=Object.defineProperty,R0=(i,l,r)=>l in i?O0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,vs=(i,l,r)=>R0(i,typeof l!="symbol"?l+"":l,r);class k0{constructor(l){this.logger=l,vs(this,"inflightRequests"),vs(this,"requestsOlderThanRestart"),vs(this,"inflightMutationsCount",0),vs(this,"inflightActionsCount",0),this.inflightRequests=new Map,this.requestsOlderThanRestart=new Set}request(l,r){return new Promise(u=>{const f=r?"Requested":"NotSent";this.inflightRequests.set(l.requestId,{message:l,status:{status:f,requestedAt:new Date,onResult:u}}),l.type==="Mutation"?this.inflightMutationsCount++:l.type==="Action"&&this.inflightActionsCount++})}onResponse(l){const r=this.inflightRequests.get(l.requestId);if(r===void 0||r.status.status==="Completed")return null;const o=r.message.type==="Mutation"?"mutation":"action",u=r.message.udfPath;for(const m of l.logLines)Ds(this.logger,"info",o,u,m);const f=r.status;let d,v;if(l.success)d={success:!0,logLines:l.logLines,value:Ra(l.result)},v=()=>f.onResult(d);else{const m=l.result,{errorData:g}=l;Ds(this.logger,"error",o,u,m),d={success:!1,errorMessage:m,errorData:g!==void 0?Ra(g):void 0,logLines:l.logLines},v=()=>f.onResult(d)}return l.type==="ActionResponse"||!l.success?(v(),this.inflightRequests.delete(l.requestId),this.requestsOlderThanRestart.delete(l.requestId),r.message.type==="Action"?this.inflightActionsCount--:r.message.type==="Mutation"&&this.inflightMutationsCount--,{requestId:l.requestId,result:d}):(r.status={status:"Completed",result:d,ts:l.ts,onResolve:v},null)}removeCompleted(l){const r=new Map;for(const[o,u]of this.inflightRequests.entries()){const f=u.status;f.status==="Completed"&&f.ts.lessThanOrEqual(l)&&(f.onResolve(),r.set(o,f.result),u.message.type==="Mutation"?this.inflightMutationsCount--:u.message.type==="Action"&&this.inflightActionsCount--,this.inflightRequests.delete(o),this.requestsOlderThanRestart.delete(o))}return r}restart(){this.requestsOlderThanRestart=new Set(this.inflightRequests.keys());const l=[];for(const[r,o]of this.inflightRequests){if(o.status.status==="NotSent"){o.status.status="Requested",l.push(o.message);continue}if(o.message.type==="Mutation")l.push(o.message);else if(o.message.type==="Action"){if(this.inflightRequests.delete(r),this.requestsOlderThanRestart.delete(r),this.inflightActionsCount--,o.status.status==="Completed")throw new Error("Action should never be in 'Completed' state");o.status.onResult({success:!1,errorMessage:"Connection lost while action was in flight",logLines:[]})}}return l}resume(){const l=[];for(const[,r]of this.inflightRequests)if(r.status.status==="NotSent"){r.status.status="Requested",l.push(r.message);continue}return l}hasIncompleteRequests(){for(const l of this.inflightRequests.values())if(l.status.status==="Requested")return!0;return!1}hasInflightRequests(){return this.inflightRequests.size>0}hasSyncedPastLastReconnect(){return this.requestsOlderThanRestart.size===0}timeOfOldestInflightRequest(){if(this.inflightRequests.size===0)return null;let l=Date.now();for(const r of this.inflightRequests.values())r.status.status!=="Completed"&&r.status.requestedAt.getTime()<l&&(l=r.status.requestedAt.getTime());return new Date(l)}inflightMutations(){return this.inflightMutationsCount}inflightActions(){return this.inflightActionsCount}}const Il=Symbol.for("functionName"),M0=Symbol.for("toReferencePath");function j0(i){return i[M0]??null}function U0(i){return i.startsWith("function://")}function D0(i){let l;if(typeof i=="string")U0(i)?l={functionHandle:i}:l={name:i};else if(i[Il])l={name:i[Il]};else{const r=j0(i);if(!r)throw new Error(`${i} is not a functionReference`);l={reference:r}}return l}function Lt(i){const l=D0(i);if(l.name===void 0)throw l.functionHandle!==void 0?new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${l.functionHandle}`):l.reference!==void 0?new Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${l.reference}`):new Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(l)}`);if(typeof i=="string")return i;const r=i[Il];if(!r)throw new Error(`${i} is not a functionReference`);return r}function wp(i){return{[Il]:i}}function xp(i=[]){const l={get(r,o){if(typeof o=="string"){const u=[...i,o];return xp(u)}else if(o===Il){if(i.length<2){const d=["api",...i].join(".");throw new Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${d}\``)}const u=i.slice(0,-1).join("/"),f=i[i.length-1];return f==="default"?u:u+":"+f}else return o===Symbol.toStringTag?"FunctionReference":void 0}};return new Proxy({},l)}const L0=xp();var z0=Object.defineProperty,N0=(i,l,r)=>l in i?z0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,zs=(i,l,r)=>N0(i,typeof l!="symbol"?l+"":l,r);class $l{constructor(l){zs(this,"queryResults"),zs(this,"modifiedQueries"),this.queryResults=l,this.modifiedQueries=[]}getQuery(l,...r){const o=Mn(r[0]),u=Lt(l),f=this.queryResults.get(Li(u,o));if(f!==void 0)return $l.queryValue(f.result)}getAllQueries(l){const r=[],o=Lt(l);for(const u of this.queryResults.values())u.udfPath===Ls(o)&&r.push({args:u.args,value:$l.queryValue(u.result)});return r}setQuery(l,r,o){const u=Mn(r),f=Lt(l),d=Li(f,u);let v;o===void 0?v=void 0:v={success:!0,value:o,logLines:[]};const m={udfPath:f,args:u,result:v};this.queryResults.set(d,m),this.modifiedQueries.push(d)}static queryValue(l){if(l!==void 0)return l.success?l.value:void 0}}class q0{constructor(){zs(this,"queryResults"),zs(this,"optimisticUpdates"),this.queryResults=new Map,this.optimisticUpdates=[]}ingestQueryResultsFromServer(l,r){this.optimisticUpdates=this.optimisticUpdates.filter(d=>!r.has(d.mutationId));const o=this.queryResults;this.queryResults=new Map(l);const u=new $l(this.queryResults);for(const d of this.optimisticUpdates)d.update(u);const f=[];for(const[d,v]of this.queryResults){const m=o.get(d);(m===void 0||m.result!==v.result)&&f.push(d)}return f}applyOptimisticUpdate(l,r){this.optimisticUpdates.push({update:l,mutationId:r});const o=new $l(this.queryResults);return l(o),o.modifiedQueries}rawQueryResult(l){return this.queryResults.get(l)}queryResult(l){const r=this.queryResults.get(l);if(r===void 0)return;const o=r.result;if(o!==void 0){if(o.success)return o.value;throw o.errorData!==void 0?Pc(o,new Qc(Ta("query",r.udfPath,o))):new Error(Ta("query",r.udfPath,o))}}hasQueryResult(l){return this.queryResults.get(l)!==void 0}queryLogs(l){return this.queryResults.get(l)?.result?.logLines}}var B0=Object.defineProperty,H0=(i,l,r)=>l in i?B0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,yc=(i,l,r)=>H0(i,typeof l!="symbol"?l+"":l,r);class Dt{constructor(l,r){yc(this,"low"),yc(this,"high"),yc(this,"__isUnsignedLong__"),this.low=l|0,this.high=r|0,this.__isUnsignedLong__=!0}static isLong(l){return(l&&l.__isUnsignedLong__)===!0}static fromBytesLE(l){return new Dt(l[0]|l[1]<<8|l[2]<<16|l[3]<<24,l[4]|l[5]<<8|l[6]<<16|l[7]<<24)}toBytesLE(){const l=this.high,r=this.low;return[r&255,r>>>8&255,r>>>16&255,r>>>24,l&255,l>>>8&255,l>>>16&255,l>>>24]}static fromNumber(l){return isNaN(l)||l<0?Ag:l>=V0?Q0:new Dt(l%Bl|0,l/Bl|0)}toString(){return(BigInt(this.high)*BigInt(Bl)+BigInt(this.low)).toString()}equals(l){return Dt.isLong(l)||(l=Dt.fromValue(l)),this.high>>>31===1&&l.high>>>31===1?!1:this.high===l.high&&this.low===l.low}notEquals(l){return!this.equals(l)}comp(l){return Dt.isLong(l)||(l=Dt.fromValue(l)),this.equals(l)?0:l.high>>>0>this.high>>>0||l.high===this.high&&l.low>>>0>this.low>>>0?-1:1}lessThanOrEqual(l){return this.comp(l)<=0}static fromValue(l){return typeof l=="number"?Dt.fromNumber(l):new Dt(l.low,l.high)}}const Ag=new Dt(0,0),Tg=65536,Bl=Tg*Tg,V0=Bl*Bl,Q0=new Dt(-1,-1);var P0=Object.defineProperty,Y0=(i,l,r)=>l in i?P0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,ys=(i,l,r)=>Y0(i,typeof l!="symbol"?l+"":l,r);class Cg{constructor(l,r){ys(this,"version"),ys(this,"remoteQuerySet"),ys(this,"queryPath"),ys(this,"logger"),this.version={querySet:0,ts:Dt.fromNumber(0),identity:0},this.remoteQuerySet=new Map,this.queryPath=l,this.logger=r}transition(l){const r=l.startVersion;if(this.version.querySet!==r.querySet||this.version.ts.notEquals(r.ts)||this.version.identity!==r.identity)throw new Error(`Invalid start version: ${r.ts.toString()}:${r.querySet}`);for(const o of l.modifications)switch(o.type){case"QueryUpdated":{const u=this.queryPath(o.queryId);if(u)for(const d of o.logLines)Ds(this.logger,"info","query",u,d);const f=Ra(o.value??null);this.remoteQuerySet.set(o.queryId,{success:!0,value:f,logLines:o.logLines});break}case"QueryFailed":{const u=this.queryPath(o.queryId);if(u)for(const d of o.logLines)Ds(this.logger,"info","query",u,d);const{errorData:f}=o;this.remoteQuerySet.set(o.queryId,{success:!1,errorMessage:o.errorMessage,errorData:f!==void 0?Ra(f):void 0,logLines:o.logLines});break}case"QueryRemoved":{this.remoteQuerySet.delete(o.queryId);break}default:throw new Error(`Invalid modification ${o.type}`)}this.version=l.endVersion}remoteQueryResults(){return this.remoteQuerySet}timestamp(){return this.version.ts}}function bc(i){const l=Pl(i);return Dt.fromBytesLE(Array.from(l))}function I0(i){const l=new Uint8Array(i.toBytesLE());return Yl(l)}function $0(i){switch(i.type){case"FatalError":case"AuthError":case"ActionResponse":case"Ping":return{...i};case"MutationResponse":return i.success?{...i,ts:bc(i.ts)}:{...i};case"Transition":return{...i,startVersion:{...i.startVersion,ts:bc(i.startVersion.ts)},endVersion:{...i.endVersion,ts:bc(i.endVersion.ts)}}}}function G0(i){switch(i.type){case"Authenticate":case"ModifyQuerySet":case"Mutation":case"Action":case"Event":return{...i};case"Connect":return i.maxObservedTimestamp!==void 0?{...i,maxObservedTimestamp:I0(i.maxObservedTimestamp)}:{...i,maxObservedTimestamp:void 0}}}var X0=Object.defineProperty,K0=(i,l,r)=>l in i?X0(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,ht=(i,l,r)=>K0(i,typeof l!="symbol"?l+"":l,r);const W0=1e3,Z0=1001,F0=1005,J0=4040,Ap={InternalServerError:{timeout:1e3},SubscriptionsWorkerFullError:{timeout:3e3},TooManyConcurrentRequests:{timeout:3e3},CommitterFullError:{timeout:3e3},AwsTooManyRequestsException:{timeout:3e3},ExecuteFullError:{timeout:3e3},SystemTimeoutError:{timeout:3e3},ExpiredInQueue:{timeout:3e3},VectorIndexesUnavailable:{timeout:1e3},SearchIndexesUnavailable:{timeout:1e3},VectorIndexTooLarge:{timeout:3e3},SearchIndexTooLarge:{timeout:3e3},TooManyWritesInTimePeriod:{timeout:3e3}};function eS(i){if(i===void 0)return"Unknown";for(const l of Object.keys(Ap))if(i.startsWith(l))return l;return"Unknown"}class tS{constructor(l,r,o,u){ht(this,"socket"),ht(this,"connectionCount"),ht(this,"_hasEverConnected",!1),ht(this,"lastCloseReason"),ht(this,"defaultInitialBackoff"),ht(this,"maxBackoff"),ht(this,"retries"),ht(this,"serverInactivityThreshold"),ht(this,"reconnectDueToServerInactivityTimeout"),ht(this,"uri"),ht(this,"onOpen"),ht(this,"onResume"),ht(this,"onMessage"),ht(this,"webSocketConstructor"),ht(this,"logger"),ht(this,"onServerDisconnectError"),this.webSocketConstructor=o,this.socket={state:"disconnected"},this.connectionCount=0,this.lastCloseReason="InitialConnect",this.defaultInitialBackoff=1e3,this.maxBackoff=16e3,this.retries=0,this.serverInactivityThreshold=3e4,this.reconnectDueToServerInactivityTimeout=null,this.uri=l,this.onOpen=r.onOpen,this.onResume=r.onResume,this.onMessage=r.onMessage,this.onServerDisconnectError=r.onServerDisconnectError,this.logger=u,this.connect()}setSocketState(l){this.socket=l,this._logVerbose(`socket state changed: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}`)}connect(){if(this.socket.state==="terminated")return;if(this.socket.state!=="disconnected"&&this.socket.state!=="stopped")throw new Error("Didn't start connection from disconnected state: "+this.socket.state);const l=new this.webSocketConstructor(this.uri);this._logVerbose("constructed WebSocket"),this.setSocketState({state:"connecting",ws:l,paused:"no"}),this.resetServerInactivityTimeout(),l.onopen=()=>{if(this.logger.logVerbose("begin ws.onopen"),this.socket.state!=="connecting")throw new Error("onopen called with socket not in connecting state");this.setSocketState({state:"ready",ws:l,paused:this.socket.paused==="yes"?"uninitialized":"no"}),this.resetServerInactivityTimeout(),this.socket.paused==="no"&&(this._hasEverConnected=!0,this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})),this.lastCloseReason!=="InitialConnect"&&this.logger.log("WebSocket reconnected"),this.connectionCount+=1,this.lastCloseReason=null},l.onerror=r=>{const o=r.message;this.logger.log(`WebSocket error: ${o}`)},l.onmessage=r=>{this.resetServerInactivityTimeout();const o=$0(JSON.parse(r.data));this._logVerbose(`received ws message with type ${o.type}`),this.onMessage(o).hasSyncedPastLastReconnect&&(this.retries=0)},l.onclose=r=>{if(this._logVerbose("begin ws.onclose"),this.lastCloseReason===null&&(this.lastCloseReason=r.reason??"OnCloseInvoked"),r.code!==W0&&r.code!==Z0&&r.code!==F0&&r.code!==J0){let u=`WebSocket closed with code ${r.code}`;r.reason&&(u+=`: ${r.reason}`),this.logger.log(u),this.onServerDisconnectError&&r.reason&&this.onServerDisconnectError(u)}const o=eS(r.reason);this.scheduleReconnect(o)}}socketState(){return this.socket.state}sendMessage(l){const r={type:l.type,...l.type==="Authenticate"&&l.tokenType==="User"?{value:`...${l.value.slice(-7)}`}:{}};if(this.socket.state==="ready"&&this.socket.paused==="no"){const o=G0(l),u=JSON.stringify(o);try{this.socket.ws.send(u)}catch(f){this.logger.log(`Failed to send message on WebSocket, reconnecting: ${f}`),this.closeAndReconnect("FailedToSendMessage")}return this._logVerbose(`sent message with type ${l.type}: ${JSON.stringify(r)}`),!0}return this._logVerbose(`message not sent (socket state: ${this.socket.state}, paused: ${"paused"in this.socket?this.socket.paused:void 0}): ${JSON.stringify(r)}`),!1}resetServerInactivityTimeout(){this.socket.state!=="terminated"&&(this.reconnectDueToServerInactivityTimeout!==null&&(clearTimeout(this.reconnectDueToServerInactivityTimeout),this.reconnectDueToServerInactivityTimeout=null),this.reconnectDueToServerInactivityTimeout=setTimeout(()=>{this.closeAndReconnect("InactiveServer")},this.serverInactivityThreshold))}scheduleReconnect(l){this.socket={state:"disconnected"};const r=this.nextBackoff(l);this.logger.log(`Attempting reconnect in ${r}ms`),setTimeout(()=>this.connect(),r)}closeAndReconnect(l){switch(this._logVerbose(`begin closeAndReconnect with reason ${l}`),this.socket.state){case"disconnected":case"terminated":case"stopped":return;case"connecting":case"ready":{this.lastCloseReason=l,this.close(),this.scheduleReconnect("client");return}default:this.socket}}close(){switch(this.socket.state){case"disconnected":case"terminated":case"stopped":return Promise.resolve();case"connecting":{const l=this.socket.ws;return new Promise(r=>{l.onclose=()=>{this._logVerbose("Closed after connecting"),r()},l.onopen=()=>{this._logVerbose("Opened after connecting"),l.close()}})}case"ready":{this._logVerbose("ws.close called");const l=this.socket.ws,r=new Promise(o=>{l.onclose=()=>{o()}});return l.close(),r}default:return this.socket,Promise.resolve()}}terminate(){switch(this.reconnectDueToServerInactivityTimeout&&clearTimeout(this.reconnectDueToServerInactivityTimeout),this.socket.state){case"terminated":case"stopped":case"disconnected":case"connecting":case"ready":{const l=this.close();return this.setSocketState({state:"terminated"}),l}default:throw this.socket,new Error(`Invalid websocket state: ${this.socket.state}`)}}stop(){switch(this.socket.state){case"terminated":return Promise.resolve();case"connecting":case"stopped":case"disconnected":case"ready":{const l=this.close();return this.socket={state:"stopped"},l}default:return this.socket,Promise.resolve()}}tryRestart(){switch(this.socket.state){case"stopped":break;case"terminated":case"connecting":case"ready":case"disconnected":this.logger.logVerbose("Restart called without stopping first");return;default:this.socket}this.connect()}pause(){switch(this.socket.state){case"disconnected":case"stopped":case"terminated":return;case"connecting":case"ready":{this.socket={...this.socket,paused:"yes"};return}default:{this.socket;return}}}resume(){switch(this.socket.state){case"connecting":this.socket={...this.socket,paused:"no"};return;case"ready":this.socket.paused==="uninitialized"?(this.socket={...this.socket,paused:"no"},this.onOpen({connectionCount:this.connectionCount,lastCloseReason:this.lastCloseReason})):this.socket.paused==="yes"&&(this.socket={...this.socket,paused:"no"},this.onResume());return;case"terminated":case"stopped":case"disconnected":return;default:this.socket}this.connect()}connectionState(){return{isConnected:this.socket.state==="ready",hasEverConnected:this._hasEverConnected,connectionCount:this.connectionCount,connectionRetries:this.retries}}_logVerbose(l){this.logger.logVerbose(l)}nextBackoff(l){const o=(l==="client"?100:l==="Unknown"?this.defaultInitialBackoff:Ap[l].timeout)*Math.pow(2,this.retries);this.retries+=1;const u=Math.min(o,this.maxBackoff),f=u*(Math.random()-.5);return u+f}}function nS(){return iS()}function iS(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,i=>{const l=Math.random()*16|0;return(i==="x"?l:l&3|8).toString(16)})}class Nl extends Error{}Nl.prototype.name="InvalidTokenError";function aS(i){return decodeURIComponent(atob(i).replace(/(.)/g,(l,r)=>{let o=r.charCodeAt(0).toString(16).toUpperCase();return o.length<2&&(o="0"+o),"%"+o}))}function lS(i){let l=i.replace(/-/g,"+").replace(/_/g,"/");switch(l.length%4){case 0:break;case 2:l+="==";break;case 3:l+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return aS(l)}catch{return atob(l)}}function rS(i,l){if(typeof i!="string")throw new Nl("Invalid token specified: must be a string");l||(l={});const r=l.header===!0?0:1,o=i.split(".")[r];if(typeof o!="string")throw new Nl(`Invalid token specified: missing part #${r+1}`);let u;try{u=lS(o)}catch(f){throw new Nl(`Invalid token specified: invalid base64 for part #${r+1} (${f.message})`)}try{return JSON.parse(u)}catch(f){throw new Nl(`Invalid token specified: invalid json for part #${r+1} (${f.message})`)}}var sS=Object.defineProperty,oS=(i,l,r)=>l in i?sS(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,It=(i,l,r)=>oS(i,typeof l!="symbol"?l+"":l,r);const uS=20*24*60*60*1e3,Og=2;class cS{constructor(l,r,o){It(this,"authState",{state:"noAuth"}),It(this,"configVersion",0),It(this,"syncState"),It(this,"authenticate"),It(this,"stopSocket"),It(this,"tryRestartSocket"),It(this,"pauseSocket"),It(this,"resumeSocket"),It(this,"clearAuth"),It(this,"logger"),It(this,"refreshTokenLeewaySeconds"),It(this,"tokenConfirmationAttempts",0),this.syncState=l,this.authenticate=r.authenticate,this.stopSocket=r.stopSocket,this.tryRestartSocket=r.tryRestartSocket,this.pauseSocket=r.pauseSocket,this.resumeSocket=r.resumeSocket,this.clearAuth=r.clearAuth,this.logger=o.logger,this.refreshTokenLeewaySeconds=o.refreshTokenLeewaySeconds}async setConfig(l,r){this.resetAuthState(),this._logVerbose("pausing WS for auth token fetch"),this.pauseSocket();const o=await this.fetchTokenAndGuardAgainstRace(l,{forceRefreshToken:!1});o.isFromOutdatedConfig||(o.value?(this.setAuthState({state:"waitingForServerConfirmationOfCachedToken",config:{fetchToken:l,onAuthChange:r},hasRetried:!1}),this.authenticate(o.value)):(this.setAuthState({state:"initialRefetch",config:{fetchToken:l,onAuthChange:r}}),await this.refetchToken()),this._logVerbose("resuming WS after auth token fetch"),this.resumeSocket())}onTransition(l){if(this.syncState.isCurrentOrNewerAuthVersion(l.endVersion.identity)&&!(l.endVersion.identity<=l.startVersion.identity)){if(this.authState.state==="waitingForServerConfirmationOfCachedToken"){this._logVerbose("server confirmed auth token is valid"),this.refetchToken(),this.authState.config.onAuthChange(!0);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this._logVerbose("server confirmed new auth token is valid"),this.scheduleTokenRefetch(this.authState.token),this.tokenConfirmationAttempts=0,this.authState.hadAuth||this.authState.config.onAuthChange(!0))}}onAuthError(l){if(l.authUpdateAttempted===!1&&(this.authState.state==="waitingForServerConfirmationOfFreshToken"||this.authState.state==="waitingForServerConfirmationOfCachedToken")){this._logVerbose("ignoring non-auth token expired error");return}const{baseVersion:r}=l;if(!this.syncState.isCurrentOrNewerAuthVersion(r+1)){this._logVerbose("ignoring auth error for previous auth attempt");return}this.tryToReauthenticate(l)}async tryToReauthenticate(l){if(this._logVerbose(`attempting to reauthenticate: ${l.error}`),this.authState.state==="noAuth"||this.authState.state==="waitingForServerConfirmationOfFreshToken"&&this.tokenConfirmationAttempts>=Og){this.logger.error(`Failed to authenticate: "${l.error}", check your server auth config`),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.authState.state!=="noAuth"&&this.setAndReportAuthFailed(this.authState.config.onAuthChange);return}this.authState.state==="waitingForServerConfirmationOfFreshToken"&&(this.tokenConfirmationAttempts++,this._logVerbose(`retrying reauthentication, ${Og-this.tokenConfirmationAttempts} attempts remaining`)),await this.stopSocket();const r=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});r.isFromOutdatedConfig||(r.value&&this.syncState.isNewAuth(r.value)?(this.authenticate(r.value),this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",config:this.authState.config,token:r.value,hadAuth:this.authState.state==="notRefetching"||this.authState.state==="waitingForScheduledRefetch"})):(this._logVerbose("reauthentication failed, could not fetch a new token"),this.syncState.hasAuth()&&this.syncState.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this.tryRestartSocket())}async refetchToken(){if(this.authState.state==="noAuth")return;this._logVerbose("refetching auth token");const l=await this.fetchTokenAndGuardAgainstRace(this.authState.config.fetchToken,{forceRefreshToken:!0});l.isFromOutdatedConfig||(l.value?this.syncState.isNewAuth(l.value)?(this.setAuthState({state:"waitingForServerConfirmationOfFreshToken",hadAuth:this.syncState.hasAuth(),token:l.value,config:this.authState.config}),this.authenticate(l.value)):this.setAuthState({state:"notRefetching",config:this.authState.config}):(this._logVerbose("refetching token failed"),this.syncState.hasAuth()&&this.clearAuth(),this.setAndReportAuthFailed(this.authState.config.onAuthChange)),this._logVerbose("restarting WS after auth token fetch (if currently stopped)"),this.tryRestartSocket())}scheduleTokenRefetch(l){if(this.authState.state==="noAuth")return;const r=this.decodeToken(l);if(!r){this.logger.error("Auth token is not a valid JWT, cannot refetch the token");return}const{iat:o,exp:u}=r;if(!o||!u){this.logger.error("Auth token does not have required fields, cannot refetch the token");return}const f=u-o;if(f<=2){this.logger.error("Auth token does not live long enough, cannot refetch the token");return}let d=Math.min(uS,(f-this.refreshTokenLeewaySeconds)*1e3);d<=0&&(this.logger.warn(`Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${f}s`),d=0);const v=setTimeout(()=>{this._logVerbose("running scheduled token refetch"),this.refetchToken()},d);this.setAuthState({state:"waitingForScheduledRefetch",refetchTokenTimeoutId:v,config:this.authState.config}),this._logVerbose(`scheduled preemptive auth token refetching in ${d}ms`)}async fetchTokenAndGuardAgainstRace(l,r){const o=++this.configVersion;this._logVerbose(`fetching token with config version ${o}`);const u=await l(r);return this.configVersion!==o?(this._logVerbose(`stale config version, expected ${o}, got ${this.configVersion}`),{isFromOutdatedConfig:!0}):{isFromOutdatedConfig:!1,value:u}}stop(){this.resetAuthState(),this.configVersion++,this._logVerbose(`config version bumped to ${this.configVersion}`)}setAndReportAuthFailed(l){l(!1),this.resetAuthState()}resetAuthState(){this.setAuthState({state:"noAuth"})}setAuthState(l){const r=l.state==="waitingForServerConfirmationOfFreshToken"?{hadAuth:l.hadAuth,state:l.state,token:`...${l.token.slice(-7)}`}:{state:l.state};switch(this._logVerbose(`setting auth state to ${JSON.stringify(r)}`),l.state){case"waitingForScheduledRefetch":case"notRefetching":case"noAuth":this.tokenConfirmationAttempts=0;break}this.authState.state==="waitingForScheduledRefetch"&&(clearTimeout(this.authState.refetchTokenTimeoutId),this.syncState.markAuthCompletion()),this.authState=l}decodeToken(l){try{return rS(l)}catch(r){return this._logVerbose(`Error decoding token: ${r instanceof Error?r.message:"Unknown error"}`),null}}_logVerbose(l){this.logger.logVerbose(`${l} [v${this.configVersion}]`)}}const fS=["convexClientConstructed","convexWebSocketOpen","convexFirstMessageReceived"];function dS(i,l){const r={sessionId:l};typeof performance>"u"||!performance.mark||performance.mark(i,{detail:r})}function hS(i){let l=i.name.slice(6);return l=l.charAt(0).toLowerCase()+l.slice(1),{name:l,startTime:i.startTime}}function mS(i){if(typeof performance>"u"||!performance.getEntriesByName)return[];const l=[];for(const r of fS){const o=performance.getEntriesByName(r).filter(u=>u.entryType==="mark").filter(u=>u.detail.sessionId===i);l.push(...o)}return l.map(hS)}var gS=Object.defineProperty,pS=(i,l,r)=>l in i?gS(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,mt=(i,l,r)=>pS(i,typeof l!="symbol"?l+"":l,r);class vS{constructor(l,r,o){if(mt(this,"address"),mt(this,"state"),mt(this,"requestManager"),mt(this,"webSocketManager"),mt(this,"authenticationManager"),mt(this,"remoteQuerySet"),mt(this,"optimisticQueryResults"),mt(this,"_transitionHandlerCounter",0),mt(this,"_nextRequestId"),mt(this,"_onTransitionFns",new Map),mt(this,"_sessionId"),mt(this,"firstMessageReceived",!1),mt(this,"debug"),mt(this,"logger"),mt(this,"maxObservedTimestamp"),mt(this,"mark",S=>{this.debug&&dS(S,this.sessionId)}),typeof l=="object")throw new Error("Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.");o?.skipConvexDeploymentUrlCheck!==!0&&u0(l),o={...o};const u=o.authRefreshTokenLeewaySeconds??2;let f=o.webSocketConstructor;if(!f&&typeof WebSocket>"u")throw new Error("No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient");f=f||WebSocket,this.debug=o.reportDebugInfoToConvex??!1,this.address=l,this.logger=o.logger===!1?Ep({verbose:o.verbose??!1}):o.logger!==!0&&o.logger?o.logger:_p({verbose:o.verbose??!1});const d=l.search("://");if(d===-1)throw new Error("Provided address was not an absolute URL.");const v=l.substring(d+3),m=l.substring(0,d);let g;if(m==="http")g="ws";else if(m==="https")g="wss";else throw new Error(`Unknown parent protocol ${m}`);const b=`${g}://${v}/api/${wg}/sync`;this.state=new C0,this.remoteQuerySet=new Cg(S=>this.state.queryPath(S),this.logger),this.requestManager=new k0(this.logger),this.authenticationManager=new cS(this.state,{authenticate:S=>{const O=this.state.setAuth(S);return this.webSocketManager.sendMessage(O),O.baseVersion},stopSocket:()=>this.webSocketManager.stop(),tryRestartSocket:()=>this.webSocketManager.tryRestart(),pauseSocket:()=>{this.webSocketManager.pause(),this.state.pause()},resumeSocket:()=>this.webSocketManager.resume(),clearAuth:()=>{this.clearAuth()}},{logger:this.logger,refreshTokenLeewaySeconds:u}),this.optimisticQueryResults=new q0,this.addOnTransitionHandler(S=>{r(S.queries.map(O=>O.token))}),this._nextRequestId=0,this._sessionId=nS();const{unsavedChangesWarning:C}=o;if(typeof window>"u"||typeof window.addEventListener>"u"){if(C===!0)throw new Error("unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.")}else C!==!1&&window.addEventListener("beforeunload",S=>{if(this.requestManager.hasIncompleteRequests()){S.preventDefault();const O="Are you sure you want to leave? Your changes may not be saved.";return(S||window.event).returnValue=O,O}});this.webSocketManager=new tS(b,{onOpen:S=>{this.mark("convexWebSocketOpen"),this.webSocketManager.sendMessage({...S,type:"Connect",sessionId:this._sessionId,maxObservedTimestamp:this.maxObservedTimestamp});const O=new Set(this.remoteQuerySet.remoteQueryResults().keys());this.remoteQuerySet=new Cg(L=>this.state.queryPath(L),this.logger);const[w,D]=this.state.restart(O);D&&this.webSocketManager.sendMessage(D),this.webSocketManager.sendMessage(w);for(const L of this.requestManager.restart())this.webSocketManager.sendMessage(L)},onResume:()=>{const[S,O]=this.state.resume();O&&this.webSocketManager.sendMessage(O),S&&this.webSocketManager.sendMessage(S);for(const w of this.requestManager.resume())this.webSocketManager.sendMessage(w)},onMessage:S=>{switch(this.firstMessageReceived||(this.firstMessageReceived=!0,this.mark("convexFirstMessageReceived"),this.reportMarks()),S.type){case"Transition":{this.observedTimestamp(S.endVersion.ts),this.authenticationManager.onTransition(S),this.remoteQuerySet.transition(S),this.state.transition(S);const O=this.requestManager.removeCompleted(this.remoteQuerySet.timestamp());this.notifyOnQueryResultChanges(O);break}case"MutationResponse":{S.success&&this.observedTimestamp(S.ts);const O=this.requestManager.onResponse(S);O!==null&&this.notifyOnQueryResultChanges(new Map([[O.requestId,O.result]]));break}case"ActionResponse":{this.requestManager.onResponse(S);break}case"AuthError":{this.authenticationManager.onAuthError(S);break}case"FatalError":{const O=x0(this.logger,S.error);throw this.webSocketManager.terminate(),O}}return{hasSyncedPastLastReconnect:this.hasSyncedPastLastReconnect()}},onServerDisconnectError:o.onServerDisconnectError},f,this.logger),this.mark("convexClientConstructed")}hasSyncedPastLastReconnect(){return this.requestManager.hasSyncedPastLastReconnect()||this.state.hasSyncedPastLastReconnect()}observedTimestamp(l){(this.maxObservedTimestamp===void 0||this.maxObservedTimestamp.lessThanOrEqual(l))&&(this.maxObservedTimestamp=l)}getMaxObservedTimestamp(){return this.maxObservedTimestamp}notifyOnQueryResultChanges(l){const r=this.remoteQuerySet.remoteQueryResults(),o=new Map;for(const[f,d]of r){const v=this.state.queryToken(f);if(v!==null){const m={result:d,udfPath:this.state.queryPath(f),args:this.state.queryArgs(f)};o.set(v,m)}}const u=this.optimisticQueryResults.ingestQueryResultsFromServer(o,new Set(l.keys()));this.handleTransition({queries:u.map(f=>{const d=this.optimisticQueryResults.rawQueryResult(f);return{token:f,modification:{kind:"Updated",result:d.result}}}),reflectedMutations:Array.from(l).map(([f,d])=>({requestId:f,result:d})),timestamp:this.remoteQuerySet.timestamp()})}handleTransition(l){for(const r of this._onTransitionFns.values())r(l)}addOnTransitionHandler(l){const r=this._transitionHandlerCounter++;return this._onTransitionFns.set(r,l),()=>this._onTransitionFns.delete(r)}setAuth(l,r){this.authenticationManager.setConfig(l,r)}hasAuth(){return this.state.hasAuth()}setAdminAuth(l,r){const o=this.state.setAdminAuth(l,r);this.webSocketManager.sendMessage(o)}clearAuth(){const l=this.state.clearAuth();this.webSocketManager.sendMessage(l)}subscribe(l,r,o){const u=Mn(r),{modification:f,queryToken:d,unsubscribe:v}=this.state.subscribe(l,u,o?.journal,o?.componentPath);return f!==null&&this.webSocketManager.sendMessage(f),{queryToken:d,unsubscribe:()=>{const m=v();m&&this.webSocketManager.sendMessage(m)}}}localQueryResult(l,r){const o=Mn(r),u=Li(l,o);return this.optimisticQueryResults.queryResult(u)}localQueryResultByToken(l){return this.optimisticQueryResults.queryResult(l)}hasLocalQueryResultByToken(l){return this.optimisticQueryResults.hasQueryResult(l)}localQueryLogs(l,r){const o=Mn(r),u=Li(l,o);return this.optimisticQueryResults.queryLogs(u)}queryJournal(l,r){const o=Mn(r),u=Li(l,o);return this.state.queryJournal(u)}connectionState(){const l=this.webSocketManager.connectionState();return{hasInflightRequests:this.requestManager.hasInflightRequests(),isWebSocketConnected:l.isConnected,hasEverConnected:l.hasEverConnected,connectionCount:l.connectionCount,connectionRetries:l.connectionRetries,timeOfOldestInflightRequest:this.requestManager.timeOfOldestInflightRequest(),inflightMutations:this.requestManager.inflightMutations(),inflightActions:this.requestManager.inflightActions()}}async mutation(l,r,o){const u=await this.mutationInternal(l,r,o);if(!u.success)throw u.errorData!==void 0?Pc(u,new Qc(Ta("mutation",l,u))):new Error(Ta("mutation",l,u));return u.value}async mutationInternal(l,r,o,u){const{mutationPromise:f}=this.enqueueMutation(l,r,o,u);return f}enqueueMutation(l,r,o,u){const f=Mn(r);this.tryReportLongDisconnect();const d=this.nextRequestId;if(this._nextRequestId++,o!==void 0){const b=o.optimisticUpdate;if(b!==void 0){const C=w=>{b(w,f)instanceof Promise&&this.logger.warn("Optimistic update handler returned a Promise. Optimistic updates should be synchronous.")},O=this.optimisticQueryResults.applyOptimisticUpdate(C,d).map(w=>{const D=this.localQueryResultByToken(w);return{token:w,modification:{kind:"Updated",result:D===void 0?void 0:{success:!0,value:D,logLines:[]}}}});this.handleTransition({queries:O,reflectedMutations:[],timestamp:this.remoteQuerySet.timestamp()})}}const v={type:"Mutation",requestId:d,udfPath:l,componentPath:u,args:[ci(f)]},m=this.webSocketManager.sendMessage(v),g=this.requestManager.request(v,m);return{requestId:d,mutationPromise:g}}async action(l,r){const o=await this.actionInternal(l,r);if(!o.success)throw o.errorData!==void 0?Pc(o,new Qc(Ta("action",l,o))):new Error(Ta("action",l,o));return o.value}async actionInternal(l,r,o){const u=Mn(r),f=this.nextRequestId;this._nextRequestId++,this.tryReportLongDisconnect();const d={type:"Action",requestId:f,udfPath:l,componentPath:o,args:[ci(u)]},v=this.webSocketManager.sendMessage(d);return this.requestManager.request(d,v)}async close(){return this.authenticationManager.stop(),this.webSocketManager.terminate()}get url(){return this.address}get nextRequestId(){return this._nextRequestId}get sessionId(){return this._sessionId}reportMarks(){if(this.debug){const l=mS(this.sessionId);this.webSocketManager.sendMessage({type:"Event",eventType:"ClientConnect",event:l})}}tryReportLongDisconnect(){if(!this.debug)return;const l=this.connectionState().timeOfOldestInflightRequest;if(l===null||Date.now()-l.getTime()<=60*1e3)return;const r=`${this.address}/api/debug_event`;fetch(r,{method:"POST",headers:{"Content-Type":"application/json","Convex-Client":`npm-${wg}`},body:JSON.stringify({event:"LongWebsocketDisconnect"})}).then(o=>{o.ok||this.logger.warn("Analytics request failed with response:",o.body)}).catch(o=>{this.logger.warn("Analytics response failed with error:",o)})}}var yS=Object.defineProperty,bS=(i,l,r)=>l in i?yS(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,li=(i,l,r)=>bS(i,typeof l!="symbol"?l+"":l,r);if(typeof q>"u")throw new Error("Required dependency 'react' not found");function Tp(i,l,r){function o(u){return wS(u),l.mutation(i,u,{optimisticUpdate:r})}return o.withOptimisticUpdate=function(f){if(r!==void 0)throw new Error(`Already specified optimistic update for mutation ${Lt(i)}`);return Tp(i,l,f)},o}class SS{constructor(l,r){if(li(this,"address"),li(this,"cachedSync"),li(this,"listeners"),li(this,"options"),li(this,"closed",!1),li(this,"_logger"),li(this,"adminAuth"),li(this,"fakeUserIdentity"),l===void 0)throw new Error("No address provided to ConvexReactClient.\nIf trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\nIf running locally, make sure to run `convex dev` and ensure the .env.local file is populated.");if(typeof l!="string")throw new Error(`ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof l} instead.`);if(!l.includes("://"))throw new Error("Provided address was not an absolute URL.");this.address=l,this.listeners=new Map,this._logger=r?.logger===!1?Ep({verbose:r?.verbose??!1}):r?.logger!==!0&&r?.logger?r.logger:_p({verbose:r?.verbose??!1}),this.options={...r,logger:this._logger}}get url(){return this.address}get sync(){if(this.closed)throw new Error("ConvexReactClient has already been closed.");return this.cachedSync?this.cachedSync:(this.cachedSync=new vS(this.address,l=>this.transition(l),this.options),this.adminAuth&&this.cachedSync.setAdminAuth(this.adminAuth,this.fakeUserIdentity),this.cachedSync)}setAuth(l,r){if(typeof l=="string")throw new Error("Passing a string to ConvexReactClient.setAuth is no longer supported, please upgrade to passing in an async function to handle reauthentication.");this.sync.setAuth(l,r??(()=>{}))}clearAuth(){this.sync.clearAuth()}setAdminAuth(l,r){if(this.adminAuth=l,this.fakeUserIdentity=r,this.closed)throw new Error("ConvexReactClient has already been closed.");this.cachedSync&&this.sync.setAdminAuth(l,r)}watchQuery(l,...r){const[o,u]=r,f=Lt(l);return{onUpdate:d=>{const{queryToken:v,unsubscribe:m}=this.sync.subscribe(f,o,u),g=this.listeners.get(v);return g!==void 0?g.add(d):this.listeners.set(v,new Set([d])),()=>{if(this.closed)return;const b=this.listeners.get(v);b.delete(d),b.size===0&&this.listeners.delete(v),m()}},localQueryResult:()=>{if(this.cachedSync)return this.cachedSync.localQueryResult(f,o)},localQueryLogs:()=>{if(this.cachedSync)return this.cachedSync.localQueryLogs(f,o)},journal:()=>{if(this.cachedSync)return this.cachedSync.queryJournal(f,o)}}}mutation(l,...r){const[o,u]=r,f=Lt(l);return this.sync.mutation(f,o,u)}action(l,...r){const o=Lt(l);return this.sync.action(o,...r)}query(l,...r){const o=this.watchQuery(l,...r),u=o.localQueryResult();return u!==void 0?Promise.resolve(u):new Promise((f,d)=>{const v=o.onUpdate(()=>{v();try{f(o.localQueryResult())}catch(m){d(m)}})})}connectionState(){return this.sync.connectionState()}get logger(){return this._logger}async close(){if(this.closed=!0,this.listeners=new Map,this.cachedSync){const l=this.cachedSync;this.cachedSync=void 0,await l.close()}}transition(l){for(const r of l){const o=this.listeners.get(r);if(o)for(const u of o)u()}}}const of=q.createContext(void 0);function _S(){return x.useContext(of)}const ES=({client:i,children:l})=>q.createElement(of.Provider,{value:i},l);function lx(i,...l){const r=l[0]==="skip",o=l[0]==="skip"?{}:Mn(l[0]),u=typeof i=="string"?wp(i):i,f=Lt(u),d=x.useMemo(()=>r?{}:{query:{query:u,args:o}},[JSON.stringify(ci(o)),f,r]),m=OS(d).query;if(m instanceof Error)throw m;return m}function Rg(i){const l=typeof i=="string"?wp(i):i,r=x.useContext(of);if(r===void 0)throw new Error("Could not find Convex client! `useMutation` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");return x.useMemo(()=>Tp(l,r),[r,Lt(l)])}function wS(i){if(typeof i=="object"&&i!==null&&"bubbles"in i&&"persist"in i&&"isDefaultPrevented"in i)throw new Error("Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like `const handler = () => myMutation();` and using `handler` in the event handler.")}var xS=Object.defineProperty,AS=(i,l,r)=>l in i?xS(i,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[l]=r,Sc=(i,l,r)=>AS(i,typeof l!="symbol"?l+"":l,r);class TS{constructor(l){Sc(this,"createWatch"),Sc(this,"queries"),Sc(this,"listeners"),this.createWatch=l,this.queries={},this.listeners=new Set}setQueries(l){for(const r of Object.keys(l)){const{query:o,args:u}=l[r];if(Lt(o),this.queries[r]===void 0)this.addQuery(r,o,u);else{const f=this.queries[r];(Lt(o)!==Lt(f.query)||JSON.stringify(ci(u))!==JSON.stringify(ci(f.args)))&&(this.removeQuery(r),this.addQuery(r,o,u))}}for(const r of Object.keys(this.queries))l[r]===void 0&&this.removeQuery(r)}subscribe(l){return this.listeners.add(l),()=>{this.listeners.delete(l)}}getLocalResults(l){const r={};for(const o of Object.keys(l)){const{query:u,args:f}=l[o];Lt(u);const d=this.createWatch(u,f);let v;try{v=d.localQueryResult()}catch(m){if(m instanceof Error)v=m;else throw m}r[o]=v}return r}setCreateWatch(l){this.createWatch=l;for(const r of Object.keys(this.queries)){const{query:o,args:u,watch:f}=this.queries[r],d=f.journal();this.removeQuery(r),this.addQuery(r,o,u,d)}}destroy(){for(const l of Object.keys(this.queries))this.removeQuery(l);this.listeners=new Set}addQuery(l,r,o,u){if(this.queries[l]!==void 0)throw new Error(`Tried to add a new query with identifier ${l} when it already exists.`);const f=this.createWatch(r,o,u),d=f.onUpdate(()=>this.notifyListeners());this.queries[l]={query:r,args:o,watch:f,unsubscribe:d}}removeQuery(l){const r=this.queries[l];if(r===void 0)throw new Error(`No query found with identifier ${l}.`);r.unsubscribe(),delete this.queries[l]}notifyListeners(){for(const l of this.listeners)l()}}function CS({getCurrentValue:i,subscribe:l}){const[r,o]=x.useState(()=>({getCurrentValue:i,subscribe:l,value:i()}));let u=r.value;return(r.getCurrentValue!==i||r.subscribe!==l)&&(u=i(),o({getCurrentValue:i,subscribe:l,value:u})),x.useEffect(()=>{let f=!1;const d=()=>{f||o(m=>{if(m.getCurrentValue!==i||m.subscribe!==l)return m;const g=i();return m.value===g?m:{...m,value:g}})},v=l(d);return d(),()=>{f=!0,v()}},[i,l]),u}function OS(i){const l=_S();if(l===void 0)throw new Error("Could not find Convex client! `useQuery` must be used in the React component tree under `ConvexProvider`. Did you forget it? See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app");const r=x.useMemo(()=>(o,u,f)=>l.watchQuery(o,u,{journal:f}),[l]);return RS(i,r)}function RS(i,l){const[r]=x.useState(()=>new TS(l));r.createWatch!==l&&r.setCreateWatch(l),x.useEffect(()=>()=>r.destroy(),[r]);const o=x.useMemo(()=>({getCurrentValue:()=>r.getLocalResults(i),subscribe:u=>(r.setQueries(i),r.subscribe(u))}),[r,i]);return CS(o)}var kS=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function Cp({packageName:i,customMessages:l}){let r=i;const o={...kS,...l};function u(f,d){if(!d)return`${r}: ${f}`;let v=f;const m=f.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const g of m){const b=(d[g[1]]||"").toString();v=v.replace(`{{${g[1]}}}`,b)}return`${r}: ${v}`}return{setPackageName({packageName:f}){return typeof f=="string"&&(r=f),this},setMessages({customMessages:f}){return Object.assign(o,f||{}),this},throwInvalidPublishableKeyError(f){throw new Error(u(o.InvalidPublishableKeyErrorMessage,f))},throwInvalidProxyUrl(f){throw new Error(u(o.InvalidProxyUrlErrorMessage,f))},throwMissingPublishableKeyError(){throw new Error(u(o.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(u(o.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(f){throw new Error(u(o.MissingClerkProvider,f))},throw(f){throw new Error(u(f))}}}var Op=Object.defineProperty,MS=Object.getOwnPropertyDescriptor,jS=Object.getOwnPropertyNames,US=Object.prototype.hasOwnProperty,DS=(i,l)=>{for(var r in l)Op(i,r,{get:l[r],enumerable:!0})},LS=(i,l,r,o)=>{if(l&&typeof l=="object"||typeof l=="function")for(let u of jS(l))!US.call(i,u)&&u!==r&&Op(i,u,{get:()=>l[u],enumerable:!(o=MS(l,u))||o.enumerable});return i},zS=(i,l,r)=>(LS(i,l,"default"),r),NS={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},qS=new Set(["first_factor","second_factor","multi_factor"]),BS=new Set(["strict_mfa","strict","moderate","lax"]),HS=i=>typeof i=="number"&&i>0,VS=i=>qS.has(i),QS=i=>BS.has(i),_c=i=>i.replace(/^(org:)*/,"org:"),PS=(i,l)=>{const{orgId:r,orgRole:o,orgPermissions:u}=l;return!i.role&&!i.permission||!r||!o||!u?null:i.permission?u.includes(_c(i.permission)):i.role?_c(o)===_c(i.role):null},kg=(i,l)=>{const{org:r,user:o}=IS(i),[u,f]=l.split(":"),d=f||u;return u==="org"?r.includes(d):u==="user"?o.includes(d):[...r,...o].includes(d)},YS=(i,l)=>{const{features:r,plans:o}=l;return i.feature&&r?kg(r,i.feature):i.plan&&o?kg(o,i.plan):null},IS=i=>{const l=i?i.split(",").map(r=>r.trim()):[];return{org:l.filter(r=>r.split(":")[0].includes("o")).map(r=>r.split(":")[1]),user:l.filter(r=>r.split(":")[0].includes("u")).map(r=>r.split(":")[1])}},$S=i=>{if(!i)return!1;const l=u=>typeof u=="string"?NS[u]:u,r=typeof i=="string"&&QS(i),o=typeof i=="object"&&VS(i.level)&&HS(i.afterMinutes);return r||o?l.bind(null,i):!1},GS=(i,{factorVerificationAge:l})=>{if(!i.reverification||!l)return null;const r=$S(i.reverification);if(!r)return null;const{level:o,afterMinutes:u}=r(),[f,d]=l,v=f!==-1?u>f:null,m=d!==-1?u>d:null;switch(o){case"first_factor":return v;case"second_factor":return d!==-1?m:v;case"multi_factor":return d===-1?v:v&&m}},XS=i=>l=>{if(!i.userId)return!1;const r=YS(l,i),o=PS(l,i),u=GS(l,i);return[r||o,u].some(f=>f===null)?[r||o,u].some(f=>f===!0):[r||o,u].every(f=>f===!0)},KS=({authObject:{sessionId:i,sessionStatus:l,userId:r,actor:o,orgId:u,orgRole:f,orgSlug:d,signOut:v,getToken:m,has:g,sessionClaims:b},options:{treatPendingAsSignedOut:C=!0}})=>{if(i===void 0&&r===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:i,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:v,getToken:m};if(i===null&&r===null)return{isLoaded:!0,isSignedIn:!1,sessionId:i,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:v,getToken:m};if(C&&l==="pending")return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:v,getToken:m};if(i&&b&&r&&u&&f)return{isLoaded:!0,isSignedIn:!0,sessionId:i,sessionClaims:b,userId:r,actor:o||null,orgId:u,orgRole:f,orgSlug:d||null,has:g,signOut:v,getToken:m};if(i&&b&&r&&!u)return{isLoaded:!0,isSignedIn:!0,sessionId:i,sessionClaims:b,userId:r,actor:o||null,orgId:null,orgRole:null,orgSlug:null,has:g,signOut:v,getToken:m}},Rp=i=>typeof atob<"u"&&typeof atob=="function"?atob(i):typeof global<"u"&&global.Buffer?new global.Buffer(i,"base64").toString():i,WS=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],kp="pk_live_",ZS="pk_test_";function Mg(i,l={}){if(i=i||"",!i||!Yc(i)){if(l.fatal&&!i)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(l.fatal&&!Yc(i))throw new Error("Publishable key not valid.");return null}const r=i.startsWith(kp)?"production":"development";let o=Rp(i.split("_")[2]);return o=o.slice(0,-1),l.proxyUrl?o=l.proxyUrl:r!=="development"&&l.domain&&l.isSatellite&&(o=`clerk.${l.domain}`),{instanceType:r,frontendApi:o}}function Yc(i=""){try{const l=i.startsWith(kp)||i.startsWith(ZS),r=Rp(i.split("_")[2]||"").endsWith("$");return l&&r}catch{return!1}}function FS(){const i=new Map;return{isDevOrStagingUrl:l=>{if(!l)return!1;const r=typeof l=="string"?l:l.hostname;let o=i.get(r);return o===void 0&&(o=WS.some(u=>r.endsWith(u)),i.set(r,o)),o}}}var JS="METHOD_CALLED";function Mp(i,l){return{event:JS,payload:{method:i,...l}}}var Ec={exports:{}},wc={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jg;function e1(){if(jg)return wc;jg=1;var i=Hs();function l(C,S){return C===S&&(C!==0||1/C===1/S)||C!==C&&S!==S}var r=typeof Object.is=="function"?Object.is:l,o=i.useState,u=i.useEffect,f=i.useLayoutEffect,d=i.useDebugValue;function v(C,S){var O=S(),w=o({inst:{value:O,getSnapshot:S}}),D=w[0].inst,L=w[1];return f(function(){D.value=O,D.getSnapshot=S,m(D)&&L({inst:D})},[C,O,S]),u(function(){return m(D)&&L({inst:D}),C(function(){m(D)&&L({inst:D})})},[C]),d(O),O}function m(C){var S=C.getSnapshot;C=C.value;try{var O=S();return!r(C,O)}catch{return!0}}function g(C,S){return S()}var b=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?g:v;return wc.useSyncExternalStore=i.useSyncExternalStore!==void 0?i.useSyncExternalStore:b,wc}var Ug;function t1(){return Ug||(Ug=1,Ec.exports=e1()),Ec.exports}var jp=t1();const Up=0,Dp=1,Lp=2,Dg=3;var Lg=Object.prototype.hasOwnProperty;function Ic(i,l){var r,o;if(i===l)return!0;if(i&&l&&(r=i.constructor)===l.constructor){if(r===Date)return i.getTime()===l.getTime();if(r===RegExp)return i.toString()===l.toString();if(r===Array){if((o=i.length)===l.length)for(;o--&&Ic(i[o],l[o]););return o===-1}if(!r||typeof i=="object"){o=0;for(r in i)if(Lg.call(i,r)&&++o&&!Lg.call(l,r)||!(r in l)||!Ic(i[r],l[r]))return!1;return Object.keys(l).length===o}}return i!==i&&l!==l}const Ft=new WeakMap,ui=()=>{},Ze=ui(),Ns=Object,ue=i=>i===Ze,Gt=i=>typeof i=="function",Un=(i,l)=>({...i,...l}),zp=i=>Gt(i.then),xc={},bs={},uf="undefined",Kl=typeof window!=uf,$c=typeof document!=uf,n1=Kl&&"Deno"in window,i1=()=>Kl&&typeof window.requestAnimationFrame!=uf,si=(i,l)=>{const r=Ft.get(i);return[()=>!ue(l)&&i.get(l)||xc,o=>{if(!ue(l)){const u=i.get(l);l in bs||(bs[l]=u),r[5](l,Un(u,o),u||xc)}},r[6],()=>!ue(l)&&l in bs?bs[l]:!ue(l)&&i.get(l)||xc]};let Gc=!0;const a1=()=>Gc,[Xc,Kc]=Kl&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[ui,ui],l1=()=>{const i=$c&&document.visibilityState;return ue(i)||i!=="hidden"},r1=i=>($c&&document.addEventListener("visibilitychange",i),Xc("focus",i),()=>{$c&&document.removeEventListener("visibilitychange",i),Kc("focus",i)}),s1=i=>{const l=()=>{Gc=!0,i()},r=()=>{Gc=!1};return Xc("online",l),Xc("offline",r),()=>{Kc("online",l),Kc("offline",r)}},o1={isOnline:a1,isVisible:l1},u1={initFocus:r1,initReconnect:s1},zg=!q.useId,Gl=!Kl||n1,c1=i=>i1()?window.requestAnimationFrame(i):setTimeout(i,1),Hl=Gl?x.useEffect:x.useLayoutEffect,Ac=typeof navigator<"u"&&navigator.connection,Ng=!Gl&&Ac&&(["slow-2g","2g"].includes(Ac.effectiveType)||Ac.saveData),Ss=new WeakMap,Tc=(i,l)=>Ns.prototype.toString.call(i)===`[object ${l}]`;let f1=0;const Wc=i=>{const l=typeof i,r=Tc(i,"Date"),o=Tc(i,"RegExp"),u=Tc(i,"Object");let f,d;if(Ns(i)===i&&!r&&!o){if(f=Ss.get(i),f)return f;if(f=++f1+"~",Ss.set(i,f),Array.isArray(i)){for(f="@",d=0;d<i.length;d++)f+=Wc(i[d])+",";Ss.set(i,f)}if(u){f="#";const v=Ns.keys(i).sort();for(;!ue(d=v.pop());)ue(i[d])||(f+=d+":"+Wc(i[d])+",");Ss.set(i,f)}}else f=r?i.toJSON():l=="symbol"?i.toString():l=="string"?JSON.stringify(i):""+i;return f},ka=i=>{if(Gt(i))try{i=i()}catch{i=""}const l=i;return i=typeof i=="string"?i:(Array.isArray(i)?i.length:i)?Wc(i):"",[i,l]};let d1=0;const Zc=()=>++d1;async function Np(...i){const[l,r,o,u]=i,f=Un({populateCache:!0,throwOnError:!0},typeof u=="boolean"?{revalidate:u}:u||{});let d=f.populateCache;const v=f.rollbackOnError;let m=f.optimisticData;const g=S=>typeof v=="function"?v(S):v!==!1,b=f.throwOnError;if(Gt(r)){const S=r,O=[],w=l.keys();for(const D of w)!/^\$(inf|sub)\$/.test(D)&&S(l.get(D)._k)&&O.push(D);return Promise.all(O.map(C))}return C(r);async function C(S){const[O]=ka(S);if(!O)return;const[w,D]=si(l,O),[L,T,H,Y]=Ft.get(l),F=()=>{const Ue=L[O];return(Gt(f.revalidate)?f.revalidate(w().data,S):f.revalidate!==!1)&&(delete H[O],delete Y[O],Ue&&Ue[0])?Ue[0](Lp).then(()=>w().data):w().data};if(i.length<3)return F();let P=o,Z;const re=Zc();T[O]=[re,0];const X=!ue(m),se=w(),ie=se.data,be=se._c,ve=ue(be)?ie:be;if(X&&(m=Gt(m)?m(ve,ie):m,D({data:m,_c:ve})),Gt(P))try{P=P(ve)}catch(Ue){Z=Ue}if(P&&zp(P))if(P=await P.catch(Ue=>{Z=Ue}),re!==T[O][0]){if(Z)throw Z;return P}else Z&&X&&g(Z)&&(d=!0,D({data:ve,_c:Ze}));if(d&&!Z)if(Gt(d)){const Ue=d(P,ve);D({data:Ue,error:Ze,_c:Ze})}else D({data:P,error:Ze,_c:Ze});if(T[O][1]=Zc(),Promise.resolve(F()).then(()=>{D({_c:Ze})}),Z){if(b)throw Z;return}return P}}const qg=(i,l)=>{for(const r in i)i[r][0]&&i[r][0](l)},qp=(i,l)=>{if(!Ft.has(i)){const r=Un(u1,l),o=Object.create(null),u=Np.bind(Ze,i);let f=ui;const d=Object.create(null),v=(b,C)=>{const S=d[b]||[];return d[b]=S,S.push(C),()=>S.splice(S.indexOf(C),1)},m=(b,C,S)=>{i.set(b,C);const O=d[b];if(O)for(const w of O)w(C,S)},g=()=>{if(!Ft.has(i)&&(Ft.set(i,[o,Object.create(null),Object.create(null),Object.create(null),u,m,v]),!Gl)){const b=r.initFocus(setTimeout.bind(Ze,qg.bind(Ze,o,Up))),C=r.initReconnect(setTimeout.bind(Ze,qg.bind(Ze,o,Dp)));f=()=>{b&&b(),C&&C(),Ft.delete(i)}}};return g(),[i,u,g,f]}return[i,Ft.get(i)[4]]},h1=(i,l,r,o,u)=>{const f=r.errorRetryCount,d=u.retryCount,v=~~((Math.random()+.5)*(1<<(d<8?d:8)))*r.errorRetryInterval;!ue(f)&&d>f||setTimeout(o,v,u)},m1=Ic,[Wl,Bp]=qp(new Map),Hp=Un({onLoadingSlow:ui,onSuccess:ui,onError:ui,onErrorRetry:h1,onDiscarded:ui,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Ng?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Ng?5e3:3e3,compare:m1,isPaused:()=>!1,cache:Wl,mutate:Bp,fallback:{}},o1),Vp=(i,l)=>{const r=Un(i,l);if(l){const{use:o,fallback:u}=i,{use:f,fallback:d}=l;o&&f&&(r.use=o.concat(f)),u&&d&&(r.fallback=Un(u,d))}return r},Fc=x.createContext({}),g1=i=>{const{value:l}=i,r=x.useContext(Fc),o=Gt(l),u=x.useMemo(()=>o?l(r):l,[o,r,l]),f=x.useMemo(()=>o?u:Vp(r,u),[o,r,u]),d=u&&u.provider,v=x.useRef(Ze);d&&!v.current&&(v.current=qp(d(f.cache||Wl),u));const m=v.current;return m&&(f.cache=m[0],f.mutate=m[1]),Hl(()=>{if(m)return m[2]&&m[2](),m[3]},[]),x.createElement(Fc.Provider,Un(i,{value:f}))},Qp="$inf$",Pp=Kl&&window.__SWR_DEVTOOLS_USE__,p1=Pp?window.__SWR_DEVTOOLS_USE__:[],v1=()=>{Pp&&(window.__SWR_DEVTOOLS_REACT__=q)},Yp=i=>Gt(i[1])?[i[0],i[1],i[2]||{}]:[i[0],null,(i[1]===null?i[2]:i[1])||{}],Ip=()=>Un(Hp,x.useContext(Fc)),y1=(i,l)=>{const[r,o]=ka(i),[,,,u]=Ft.get(Wl);if(u[r])return u[r];const f=l(o);return u[r]=f,f},b1=i=>(l,r,o)=>i(l,r&&((...f)=>{const[d]=ka(l),[,,,v]=Ft.get(Wl);if(d.startsWith(Qp))return r(...f);const m=v[d];return ue(m)?r(...f):(delete v[d],m)}),o),S1=p1.concat(b1),_1=i=>function(...r){const o=Ip(),[u,f,d]=Yp(r),v=Vp(o,d);let m=i;const{use:g}=v,b=(g||[]).concat(S1);for(let C=b.length;C--;)m=b[C](m);return m(u,f||v.fetcher||null,v)},E1=(i,l,r)=>{const o=l[i]||(l[i]=[]);return o.push(r),()=>{const u=o.indexOf(r);u>=0&&(o[u]=o[o.length-1],o.pop())}},w1=(i,l)=>(...r)=>{const[o,u,f]=Yp(r),d=(f.use||[]).concat(l);return i(o,u,{...f,use:d})};v1();const x1=()=>{},A1=x1(),Jc=Object,Bg=i=>i===A1,T1=i=>typeof i=="function",_s=new WeakMap,Cc=(i,l)=>Jc.prototype.toString.call(i)===`[object ${l}]`;let C1=0;const ef=i=>{const l=typeof i,r=Cc(i,"Date"),o=Cc(i,"RegExp"),u=Cc(i,"Object");let f,d;if(Jc(i)===i&&!r&&!o){if(f=_s.get(i),f)return f;if(f=++C1+"~",_s.set(i,f),Array.isArray(i)){for(f="@",d=0;d<i.length;d++)f+=ef(i[d])+",";_s.set(i,f)}if(u){f="#";const v=Jc.keys(i).sort();for(;!Bg(d=v.pop());)Bg(i[d])||(f+=d+":"+ef(i[d])+",");_s.set(i,f)}}else f=r?i.toJSON():l=="symbol"?i.toString():l=="string"?JSON.stringify(i):""+i;return f},O1=i=>{if(T1(i))try{i=i()}catch{i=""}const l=i;return i=typeof i=="string"?i:(Array.isArray(i)?i.length:i)?ef(i):"",[i,l]},R1=i=>O1(i)[0],Oc=q.use||(i=>{switch(i.status){case"pending":throw i;case"fulfilled":return i.value;case"rejected":throw i.reason;default:throw i.status="pending",i.then(l=>{i.status="fulfilled",i.value=l},l=>{i.status="rejected",i.reason=l}),i}}),Rc={dedupe:!0},k1=(i,l,r)=>{const{cache:o,compare:u,suspense:f,fallbackData:d,revalidateOnMount:v,revalidateIfStale:m,refreshInterval:g,refreshWhenHidden:b,refreshWhenOffline:C,keepPreviousData:S}=r,[O,w,D,L]=Ft.get(o),[T,H]=ka(i),Y=x.useRef(!1),F=x.useRef(!1),P=x.useRef(T),Z=x.useRef(l),re=x.useRef(r),X=()=>re.current,se=()=>X().isVisible()&&X().isOnline(),[ie,be,ve,Ue]=si(o,T),ze=x.useRef({}).current,Oe=ue(d)?ue(r.fallback)?Ze:r.fallback[T]:d,z=(Ee,_e)=>{for(const Fe in ze){const Ne=Fe;if(Ne==="data"){if(!u(Ee[Ne],_e[Ne])&&(!ue(Ee[Ne])||!u(fe,_e[Ne])))return!1}else if(_e[Ne]!==Ee[Ne])return!1}return!0},I=x.useMemo(()=>{const Ee=!T||!l?!1:ue(v)?X().isPaused()||f?!1:m!==!1:v,_e=Je=>{const bt=Un(Je);return delete bt._k,Ee?{isValidating:!0,isLoading:!0,...bt}:bt},Fe=ie(),Ne=Ue(),He=_e(Fe),Ln=Fe===Ne?He:_e(Ne);let $e=He;return[()=>{const Je=_e(ie());return z(Je,$e)?($e.data=Je.data,$e.isLoading=Je.isLoading,$e.isValidating=Je.isValidating,$e.error=Je.error,$e):($e=Je,Je)},()=>Ln]},[o,T]),W=jp.useSyncExternalStore(x.useCallback(Ee=>ve(T,(_e,Fe)=>{z(Fe,_e)||Ee()}),[o,T]),I[0],I[1]),ge=!Y.current,_=O[T]&&O[T].length>0,Q=W.data,$=ue(Q)?Oe&&zp(Oe)?Oc(Oe):Oe:Q,G=W.error,J=x.useRef($),fe=S?ue(Q)?ue(J.current)?$:J.current:Q:$,ae=_&&!ue(G)?!1:ge&&!ue(v)?v:X().isPaused()?!1:f?ue($)?!1:m:ue($)||m,ut=!!(T&&l&&ge&&ae),Me=ue(W.isValidating)?ut:W.isValidating,nn=ue(W.isLoading)?ut:W.isLoading,mn=x.useCallback(async Ee=>{const _e=Z.current;if(!T||!_e||F.current||X().isPaused())return!1;let Fe,Ne,He=!0;const Ln=Ee||{},$e=!D[T]||!Ln.dedupe,Je=()=>zg?!F.current&&T===P.current&&Y.current:T===P.current,bt={isValidating:!1,isLoading:!1},nr=()=>{be(bt)},za=()=>{const St=D[T];St&&St[1]===Ne&&delete D[T]},ir={isValidating:!0};ue(ie().data)&&(ir.isLoading=!0);try{if($e&&(be(ir),r.loadingTimeout&&ue(ie().data)&&setTimeout(()=>{He&&Je()&&X().onLoadingSlow(T,r)},r.loadingTimeout),D[T]=[_e(H),Zc()]),[Fe,Ne]=D[T],Fe=await Fe,$e&&setTimeout(za,r.dedupingInterval),!D[T]||D[T][1]!==Ne)return $e&&Je()&&X().onDiscarded(T),!1;bt.error=Ze;const St=w[T];if(!ue(St)&&(Ne<=St[0]||Ne<=St[1]||St[1]===0))return nr(),$e&&Je()&&X().onDiscarded(T),!1;const gt=ie().data;bt.data=u(gt,Fe)?gt:Fe,$e&&Je()&&X().onSuccess(Fe,T,r)}catch(St){za();const gt=X(),{shouldRetryOnError:at}=gt;gt.isPaused()||(bt.error=St,$e&&Je()&&(gt.onError(St,T,gt),(at===!0||Gt(at)&&at(St))&&(!X().revalidateOnFocus||!X().revalidateOnReconnect||se())&&gt.onErrorRetry(St,T,gt,an=>{const lt=O[T];lt&&lt[0]&&lt[0](Dg,an)},{retryCount:(Ln.retryCount||0)+1,dedupe:!0})))}return He=!1,nr(),!0},[T,o]),hi=x.useCallback((...Ee)=>Np(o,P.current,...Ee),[]);if(Hl(()=>{Z.current=l,re.current=r,ue(Q)||(J.current=Q)}),Hl(()=>{if(!T)return;const Ee=mn.bind(Ze,Rc);let _e=0;X().revalidateOnFocus&&(_e=Date.now()+X().focusThrottleInterval);const Ne=E1(T,O,(He,Ln={})=>{if(He==Up){const $e=Date.now();X().revalidateOnFocus&&$e>_e&&se()&&(_e=$e+X().focusThrottleInterval,Ee())}else if(He==Dp)X().revalidateOnReconnect&&se()&&Ee();else{if(He==Lp)return mn();if(He==Dg)return mn(Ln)}});return F.current=!1,P.current=T,Y.current=!0,be({_k:H}),ae&&(ue($)||Gl?Ee():c1(Ee)),()=>{F.current=!0,Ne()}},[T]),Hl(()=>{let Ee;function _e(){const Ne=Gt(g)?g(ie().data):g;Ne&&Ee!==-1&&(Ee=setTimeout(Fe,Ne))}function Fe(){!ie().error&&(b||X().isVisible())&&(C||X().isOnline())?mn(Rc).then(_e):_e()}return _e(),()=>{Ee&&(clearTimeout(Ee),Ee=-1)}},[g,b,C,T]),x.useDebugValue(fe),f&&ue($)&&T){if(!zg&&Gl)throw new Error("Fallback data is required when using Suspense in SSR.");Z.current=l,re.current=r,F.current=!1;const Ee=L[T];if(!ue(Ee)){const _e=hi(Ee);Oc(_e)}if(ue(G)){const _e=mn(Rc);ue(fe)||(_e.status="fulfilled",_e.value=!0),Oc(_e)}else throw G}return{mutate:hi,get data(){return ze.data=!0,fe},get error(){return ze.error=!0,G},get isValidating(){return ze.isValidating=!0,Me},get isLoading(){return ze.isLoading=!0,nn}}},M1=Ns.defineProperty(g1,"defaultValue",{value:Hp}),cf=_1(k1),j1=Object.freeze(Object.defineProperty({__proto__:null,SWRConfig:M1,default:cf,mutate:Bp,preload:y1,unstable_serialize:R1,useSWRConfig:Ip},Symbol.toStringTag,{value:"Module"})),U1=()=>{},D1=U1(),tf=Object,Hg=i=>i===D1,L1=i=>typeof i=="function",Es=new WeakMap,kc=(i,l)=>tf.prototype.toString.call(i)===`[object ${l}]`;let z1=0;const nf=i=>{const l=typeof i,r=kc(i,"Date"),o=kc(i,"RegExp"),u=kc(i,"Object");let f,d;if(tf(i)===i&&!r&&!o){if(f=Es.get(i),f)return f;if(f=++z1+"~",Es.set(i,f),Array.isArray(i)){for(f="@",d=0;d<i.length;d++)f+=nf(i[d])+",";Es.set(i,f)}if(u){f="#";const v=tf.keys(i).sort();for(;!Hg(d=v.pop());)Hg(i[d])||(f+=d+":"+nf(i[d])+",");Es.set(i,f)}}else f=r?i.toJSON():l=="symbol"?i.toString():l=="string"?JSON.stringify(i):""+i;return f},N1=i=>{if(L1(i))try{i=i()}catch{i=""}const l=i;return i=typeof i=="string"?i:(Array.isArray(i)?i.length:i)?nf(i):"",[i,l]},q1=i=>N1(i?i(0,null):null)[0],Mc=Promise.resolve(),B1=i=>(l,r,o)=>{const u=x.useRef(!1),{cache:f,initialSize:d=1,revalidateAll:v=!1,persistSize:m=!1,revalidateFirstPage:g=!0,revalidateOnMount:b=!1,parallel:C=!1}=o,[,,,S]=Ft.get(Wl);let O;try{O=q1(l),O&&(O=Qp+O)}catch{}const[w,D,L]=si(f,O),T=x.useCallback(()=>ue(w()._l)?d:w()._l,[f,O,d]);jp.useSyncExternalStore(x.useCallback(X=>O?L(O,()=>{X()}):()=>{},[f,O]),T,T);const H=x.useCallback(()=>{const X=w()._l;return ue(X)?d:X},[O,d]),Y=x.useRef(H());Hl(()=>{if(!u.current){u.current=!0;return}O&&D({_l:m?Y.current:H()})},[O,f]);const F=b&&!u.current,P=i(O,async X=>{const se=w()._i,ie=w()._r;D({_r:Ze});const be=[],ve=H(),[Ue]=si(f,X),ze=Ue().data,Oe=[];let z=null;for(let I=0;I<ve;++I){const[W,ge]=ka(l(I,C?null:z));if(!W)break;const[_,Q]=si(f,W);let $=_().data;const G=v||se||ue($)||g&&!I&&!ue(ze)||F||ze&&!ue(ze[I])&&!o.compare(ze[I],$);if(r&&(typeof ie=="function"?ie($,ge):G)){const J=async()=>{if(!(W in S))$=await r(ge);else{const ae=S[W];delete S[W],$=await ae}Q({data:$,_k:ge}),be[I]=$};C?Oe.push(J):await J()}else be[I]=$;C||(z=$)}return C&&await Promise.all(Oe.map(I=>I())),D({_i:Ze}),be},o),Z=x.useCallback(function(X,se){const ie=typeof se=="boolean"?{revalidate:se}:se||{},be=ie.revalidate!==!1;return O?(be&&(ue(X)?D({_i:!0,_r:ie.revalidate}):D({_i:!1,_r:ie.revalidate})),arguments.length?P.mutate(X,{...ie,revalidate:be}):P.mutate()):Mc},[O,f]),re=x.useCallback(X=>{if(!O)return Mc;const[,se]=si(f,O);let ie;if(Gt(X)?ie=X(H()):typeof X=="number"&&(ie=X),typeof ie!="number")return Mc;se({_l:ie}),Y.current=ie;const be=[],[ve]=si(f,O);let Ue=null;for(let ze=0;ze<ie;++ze){const[Oe]=ka(l(ze,Ue)),[z]=si(f,Oe),I=Oe?z().data:Ze;if(ue(I))return Z(ve().data);be.push(I),Ue=I}return Z(be)},[O,f,Z,H]);return{size:H(),setSize:re,mutate:Z,get data(){return P.data},get error(){return P.error},get isValidating(){return P.isValidating},get isLoading(){return P.isLoading}}},H1=w1(cf,B1);var Vg=Object.prototype.hasOwnProperty;function Qg(i,l,r){for(r of i.keys())if(Vl(r,l))return r}function Vl(i,l){var r,o,u;if(i===l)return!0;if(i&&l&&(r=i.constructor)===l.constructor){if(r===Date)return i.getTime()===l.getTime();if(r===RegExp)return i.toString()===l.toString();if(r===Array){if((o=i.length)===l.length)for(;o--&&Vl(i[o],l[o]););return o===-1}if(r===Set){if(i.size!==l.size)return!1;for(o of i)if(u=o,u&&typeof u=="object"&&(u=Qg(l,u),!u)||!l.has(u))return!1;return!0}if(r===Map){if(i.size!==l.size)return!1;for(o of i)if(u=o[0],u&&typeof u=="object"&&(u=Qg(l,u),!u)||!Vl(o[1],l.get(u)))return!1;return!0}if(r===ArrayBuffer)i=new Uint8Array(i),l=new Uint8Array(l);else if(r===DataView){if((o=i.byteLength)===l.byteLength)for(;o--&&i.getInt8(o)===l.getInt8(o););return o===-1}if(ArrayBuffer.isView(i)){if((o=i.byteLength)===l.byteLength)for(;o--&&i[o]===l[o];);return o===-1}if(!r||typeof i=="object"){o=0;for(r in i)if(Vg.call(i,r)&&++o&&!Vg.call(l,r)||!(r in l)||!Vl(i[r],l[r]))return!1;return Object.keys(l).length===o}}return i!==i&&l!==l}function V1(i,l){if(!i)throw typeof l=="string"?new Error(l):new Error(`${l.displayName} not found`)}var Ma=(i,l)=>{const{assertCtxFn:r=V1}={},o=q.createContext(void 0);return o.displayName=i,[o,()=>{const d=q.useContext(o);return r(d,`${i} not found`),d.value},()=>{const d=q.useContext(o);return d?d.value:{}}]},ff={};DS(ff,{useSWR:()=>cf,useSWRInfinite:()=>H1});zS(ff,j1);var[$p,Gp]=Ma("ClerkInstanceContext"),[Q1,P1]=Ma("UserContext"),[Y1,rx]=Ma("ClientContext"),[I1,sx]=Ma("SessionContext");q.createContext({});var[$1,ox]=Ma("OrganizationContext"),G1=({children:i,organization:l,swrConfig:r})=>q.createElement(ff.SWRConfig,{value:r},q.createElement($1.Provider,{value:{value:{organization:l}}},i));function Xp(i){if(!q.useContext($p)){if(typeof i=="function"){i();return}throw new Error(`${i} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}typeof window<"u"?q.useLayoutEffect:q.useEffect;var Pg="useUser";function ux(){Xp(Pg);const i=P1();return Gp().telemetry?.record(Mp(Pg)),i===void 0?{isLoaded:!1,isSignedIn:void 0,user:void 0}:i===null?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:i}}var Yg=Vl,X1=()=>{try{return!1}catch{}return!1},K1=()=>{try{return!1}catch{}return!1},W1=()=>{try{return!0}catch{}return!1},Ig=new Set,df=(i,l,r)=>{const o=K1()||W1(),u=i;Ig.has(u)||o||(Ig.add(u),console.warn(`Clerk - DEPRECATION WARNING: "${i}" is deprecated and will be removed in the next major release.
${l}`))},hn=Cp({packageName:"@clerk/clerk-react"});function Z1(i){hn.setMessages(i).setPackageName(i)}var[F1,J1]=Ma("AuthContext"),e_=$p,Kp=Gp,t_="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",n_=i=>`You've passed multiple children components to <${i}/>. You can only pass a single child component or text.`,i_="Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support",jc="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",a_="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",l_="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",r_="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",s_="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",o_=i=>`<${i} /> can only accept <${i}.Page /> and <${i}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,u_=i=>`Missing props. <${i}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,c_=i=>`Missing props. <${i}.Link /> component requires the following props: url, label and labelIcon.`,f_="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",d_="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",h_="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",m_="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",g_="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",p_="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",v_="Missing props. <UserButton.Action /> component requires the following props: label.",Vs=i=>{Xp(()=>{hn.throwMissingClerkProviderError({source:i})})},Wp=i=>new Promise(l=>{const r=o=>{["ready","degraded"].includes(o)&&(l(),i.off("status",r))};i.on("status",r,{notify:!0})}),y_=i=>async l=>(await Wp(i),i.session?i.session.getToken(l):null),b_=i=>async(...l)=>(await Wp(i),i.signOut(...l)),Zp=(i={})=>{var l,r;Vs("useAuth");const{treatPendingAsSignedOut:o,...u}=i??{},f=u;let v=J1();v.sessionId===void 0&&v.userId===void 0&&(v=f??{});const m=Kp(),g=x.useCallback(y_(m),[m]),b=x.useCallback(b_(m),[m]);return(l=m.telemetry)==null||l.record(Mp("useAuth",{treatPendingAsSignedOut:o})),S_({...v,getToken:g,signOut:b},{treatPendingAsSignedOut:o??((r=m.__internal_getOption)==null?void 0:r.call(m,"treatPendingAsSignedOut"))})};function S_(i,{treatPendingAsSignedOut:l=!0}={}){const{userId:r,orgId:o,orgRole:u,has:f,signOut:d,getToken:v,orgPermissions:m,factorVerificationAge:g,sessionClaims:b}=i??{},C=x.useCallback(O=>f?f(O):XS({userId:r,orgId:o,orgRole:u,orgPermissions:m,factorVerificationAge:g,features:b?.fea||"",plans:b?.pla||""})(O),[f,r,o,u,m,g]),S=KS({authObject:{...i,getToken:v,signOut:d,has:C},options:{treatPendingAsSignedOut:l}});return S||hn.throw(i_)}var Pe=(i,l)=>{const o=(typeof l=="string"?l:l?.component)||i.displayName||i.name||"Component";i.displayName=o;const u=typeof l=="string"?void 0:l,f=d=>{Vs(o||"withClerk");const v=Kp();return!v.loaded&&!u?.renderWhileLoading?null:q.createElement(i,{...d,component:o,clerk:v})};return f.displayName=`withClerk(${o})`,f},ws=({children:i,treatPendingAsSignedOut:l})=>{Vs("SignedIn");const{userId:r}=Zp({treatPendingAsSignedOut:l});return r?i:null},xs=({children:i,treatPendingAsSignedOut:l})=>{Vs("SignedOut");const{userId:r}=Zp({treatPendingAsSignedOut:l});return r===null?i:null};Pe(({clerk:i,...l})=>{const{client:r,session:o}=i,u=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return q.useEffect(()=>{o===null&&u?i.redirectToAfterSignOut():i.redirectToSignIn(l)},[]),null},"RedirectToSignIn");Pe(({clerk:i,...l})=>(q.useEffect(()=>{i.redirectToSignUp(l)},[]),null),"RedirectToSignUp");Pe(({clerk:i})=>(q.useEffect(()=>{df("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),i.redirectToUserProfile()},[]),null),"RedirectToUserProfile");Pe(({clerk:i})=>(q.useEffect(()=>{df("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),i.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile");Pe(({clerk:i})=>(q.useEffect(()=>{df("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),i.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization");Pe(({clerk:i,...l})=>(q.useEffect(()=>{i.handleRedirectCallback(l)},[]),null),"AuthenticateWithRedirectCallback");var Fp=i=>{throw TypeError(i)},hf=(i,l,r)=>l.has(i)||Fp("Cannot "+r),Be=(i,l,r)=>(hf(i,l,"read from private field"),r?r.call(i):l.get(i)),Ui=(i,l,r)=>l.has(i)?Fp("Cannot add the same private member more than once"):l instanceof WeakSet?l.add(i):l.set(i,r),Ea=(i,l,r,o)=>(hf(i,l,"write to private field"),l.set(i,r),r),$g=(i,l,r)=>(hf(i,l,"access private method"),r),__=(i,l="5.69.1")=>{if(i)return i;const r=E_(l);return r?r==="snapshot"?"5.69.1":r:w_(l)},E_=i=>i.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],w_=i=>i.trim().replace(/^v/,"").split(".")[0];function x_(i){return i?A_(i)||Jp(i):!0}function A_(i){return/^http(s)?:\/\//.test(i||"")}function Jp(i){return i.startsWith("/")}function T_(i){return i?Jp(i)?new URL(i,window.location.origin).toString():i:""}function C_(i){if(!i)return"";let l;if(i.match(/^(clerk\.)+\w*$/))l=/(clerk\.)*(?=clerk\.)/;else{if(i.match(/\.clerk.accounts/))return i;l=/^(clerk\.)*/gi}return`clerk.${i.replace(l,"")}`}var O_={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(i,l)=>l<5,retryImmediately:!1,jitter:!0},R_=100,ev=async i=>new Promise(l=>setTimeout(l,i)),tv=(i,l)=>l?i*(1+Math.random()):i,k_=i=>{let l=0;const r=()=>{const o=i.initialDelay,u=i.factor;let f=o*Math.pow(u,l);return f=tv(f,i.jitter),Math.min(i.maxDelayBetweenRetries||f,f)};return async()=>{await ev(r()),l++}},M_=async(i,l={})=>{let r=0;const{shouldRetry:o,initialDelay:u,maxDelayBetweenRetries:f,factor:d,retryImmediately:v,jitter:m}={...O_,...l},g=k_({initialDelay:u,maxDelayBetweenRetries:f,factor:d,jitter:m});for(;;)try{return await i()}catch(b){if(r++,!o(b,r))throw b;v&&r===1?await ev(tv(R_,m)):await g()}},j_="loadScript cannot be called when document does not exist",U_="loadScript cannot be called without a src";async function D_(i="",l){const{async:r,defer:o,beforeLoad:u,crossOrigin:f,nonce:d}=l||{};return M_(()=>new Promise((m,g)=>{i||g(new Error(U_)),(!document||!document.body)&&g(j_);const b=document.createElement("script");f&&b.setAttribute("crossorigin",f),b.async=r||!1,b.defer=o||!1,b.addEventListener("load",()=>{b.remove(),m(b)}),b.addEventListener("error",()=>{b.remove(),g()}),b.src=i,b.nonce=d,u?.(b),document.body.appendChild(b)}),{shouldRetry:(m,g)=>g<=5})}var Gg="Clerk: Failed to load Clerk",{isDevOrStagingUrl:L_}=FS(),nv=Cp({packageName:"@clerk/shared"});function z_(i){nv.setPackageName({packageName:i})}var N_=async i=>{const l=document.querySelector("script[data-clerk-js-script]");if(l)return new Promise((r,o)=>{l.addEventListener("load",()=>{r(l)}),l.addEventListener("error",()=>{o(Gg)})});if(!i?.publishableKey){nv.throwMissingPublishableKeyError();return}return D_(q_(i),{async:!0,crossOrigin:"anonymous",nonce:i.nonce,beforeLoad:H_(i)}).catch(()=>{throw new Error(Gg)})},q_=i=>{const{clerkJSUrl:l,clerkJSVariant:r,clerkJSVersion:o,proxyUrl:u,domain:f,publishableKey:d}=i;if(l)return l;let v="";u&&x_(u)?v=T_(u).replace(/http(s)?:\/\//,""):f&&!L_(Mg(d)?.frontendApi||"")?v=C_(f):v=Mg(d)?.frontendApi||"";const m=r?`${r.replace(/\.+$/,"")}.`:"",g=__(o);return`https://${v}/npm/@clerk/clerk-js@${g}/dist/clerk.${m}browser.js`},B_=i=>{const l={};return i.publishableKey&&(l["data-clerk-publishable-key"]=i.publishableKey),i.proxyUrl&&(l["data-clerk-proxy-url"]=i.proxyUrl),i.domain&&(l["data-clerk-domain"]=i.domain),i.nonce&&(l.nonce=i.nonce),l},H_=i=>l=>{const r=B_(i);for(const o in r)l.setAttribute(o,r[o])},yt=i=>{X1()&&console.error(`Clerk: ${i}`)};function Uc(i,l,r){if(typeof i=="function")return i(l);if(typeof i<"u")return i;if(typeof r<"u")return r}var V_=hp(),Xg=(i,...l)=>{const r={...i};for(const o of l)delete r[o];return r},Q_=(i,l,r)=>!i&&r?P_(r):Y_(l),P_=i=>{const l=i.userId,r=i.user,o=i.sessionId,u=i.sessionStatus,f=i.sessionClaims,d=i.session,v=i.organization,m=i.orgId,g=i.orgRole,b=i.orgPermissions,C=i.orgSlug,S=i.actor,O=i.factorVerificationAge;return{userId:l,user:r,sessionId:o,session:d,sessionStatus:u,sessionClaims:f,organization:v,orgId:m,orgRole:g,orgPermissions:b,orgSlug:C,actor:S,factorVerificationAge:O}},Y_=i=>{const l=i.user?i.user.id:i.user,r=i.user,o=i.session?i.session.id:i.session,u=i.session,f=i.session?.status,d=i.session?i.session.lastActiveToken?.jwt?.claims:null,v=i.session?i.session.factorVerificationAge:null,m=u?.actor,g=i.organization,b=i.organization?i.organization.id:i.organization,C=g?.slug,S=g&&r?.organizationMemberships?.find(D=>D.organization.id===b),O=S&&S.permissions,w=S&&S.role;return{userId:l,user:r,sessionId:o,session:u,sessionStatus:f,sessionClaims:d,organization:g,orgId:b,orgRole:w,orgSlug:C,orgPermissions:O,actor:m,factorVerificationAge:v}};function Kg(){return typeof window<"u"}var Wg=(i,l,r,o,u)=>{const{notify:f}=u||{};let d=i.get(r);d||(d=[],i.set(r,d)),d.push(o),f&&l.has(r)&&o(l.get(r))},Zg=(i,l,r)=>(i.get(l)||[]).map(o=>o(r)),Fg=(i,l,r)=>{const o=i.get(l);o&&(r?o.splice(o.indexOf(r)>>>0,1):i.set(l,[]))},I_=()=>{const i=new Map,l=new Map,r=new Map;return{on:(...u)=>Wg(i,l,...u),prioritizedOn:(...u)=>Wg(r,l,...u),emit:(u,f)=>{l.set(u,f),Zg(r,u,f),Zg(i,u,f)},off:(...u)=>Fg(i,...u),prioritizedOff:(...u)=>Fg(r,...u),internal:{retrieveListeners:u=>i.get(u)||[]}}},As={Status:"status"},$_=()=>I_();typeof window<"u"&&!window.global&&(window.global=typeof global>"u"?window:global);var Qs=i=>l=>{try{return q.Children.only(i)}catch{return hn.throw(n_(l))}},Ps=(i,l)=>(i||(i=l),typeof i=="string"&&(i=q.createElement("button",null,i)),i),Ys=i=>(...l)=>{if(i&&typeof i=="function")return i(...l)};function G_(i){return typeof i=="function"}var Ts=new Map;function X_(i,l,r=1){q.useEffect(()=>{const o=Ts.get(i)||0;return o==r?hn.throw(l):(Ts.set(i,o+1),()=>{Ts.set(i,(Ts.get(i)||1)-1)})},[])}function K_(i,l,r){const o=i.displayName||i.name||l||"Component",u=f=>(X_(l,r),q.createElement(i,{...f}));return u.displayName=`withMaxAllowedInstancesGuard(${o})`,u}var Ql=i=>{const l=Array(i.length).fill(null),[r,o]=x.useState(l);return i.map((u,f)=>({id:u.id,mount:d=>o(v=>v.map((m,g)=>g===f?d:m)),unmount:()=>o(d=>d.map((v,m)=>m===f?null:v)),portal:()=>q.createElement(q.Fragment,null,r[f]?V_.createPortal(u.component,r[f]):null)}))},vt=(i,l)=>!!i&&q.isValidElement(i)&&i?.type===l,iv=(i,l)=>rv({children:i,reorderItemsLabels:["account","security"],LinkComponent:Fl,PageComponent:Zl,MenuItemsComponent:$s,componentName:"UserProfile"},l),av=(i,l)=>rv({children:i,reorderItemsLabels:["general","members"],LinkComponent:Xs,PageComponent:Gs,componentName:"OrganizationProfile"},l),lv=i=>{const l=[],r=[Xs,Gs,$s,Zl,Fl];return q.Children.forEach(i,o=>{r.some(u=>vt(o,u))||l.push(o)}),l},rv=(i,l)=>{const{children:r,LinkComponent:o,PageComponent:u,MenuItemsComponent:f,reorderItemsLabels:d,componentName:v}=i,{allowForAnyChildren:m=!1}=l||{},g=[];q.Children.forEach(r,H=>{if(!vt(H,u)&&!vt(H,o)&&!vt(H,f)){H&&!m&&yt(o_(v));return}const{props:Y}=H,{children:F,label:P,url:Z,labelIcon:re}=Y;if(vt(H,u))if(Jg(Y,d))g.push({label:P});else if(Dc(Y))g.push({label:P,labelIcon:re,children:F,url:Z});else{yt(u_(v));return}if(vt(H,o))if(Lc(Y))g.push({label:P,labelIcon:re,url:Z});else{yt(c_(v));return}});const b=[],C=[],S=[];g.forEach((H,Y)=>{if(Dc(H)){b.push({component:H.children,id:Y}),C.push({component:H.labelIcon,id:Y});return}Lc(H)&&S.push({component:H.labelIcon,id:Y})});const O=Ql(b),w=Ql(C),D=Ql(S),L=[],T=[];return g.forEach((H,Y)=>{if(Jg(H,d)){L.push({label:H.label});return}if(Dc(H)){const{portal:F,mount:P,unmount:Z}=O.find(ie=>ie.id===Y),{portal:re,mount:X,unmount:se}=w.find(ie=>ie.id===Y);L.push({label:H.label,url:H.url,mount:P,unmount:Z,mountIcon:X,unmountIcon:se}),T.push(F),T.push(re);return}if(Lc(H)){const{portal:F,mount:P,unmount:Z}=D.find(re=>re.id===Y);L.push({label:H.label,url:H.url,mountIcon:P,unmountIcon:Z}),T.push(F);return}}),{customPages:L,customPagesPortals:T}},Jg=(i,l)=>{const{children:r,label:o,url:u,labelIcon:f}=i;return!r&&!u&&!f&&l.some(d=>d===o)},Dc=i=>{const{children:l,label:r,url:o,labelIcon:u}=i;return!!l&&!!o&&!!u&&!!r},Lc=i=>{const{children:l,label:r,url:o,labelIcon:u}=i;return!l&&!!o&&!!u&&!!r},W_=i=>Z_({children:i,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:$s,MenuActionComponent:ov,MenuLinkComponent:uv,UserProfileLinkComponent:Fl,UserProfilePageComponent:Zl}),Z_=({children:i,MenuItemsComponent:l,MenuActionComponent:r,MenuLinkComponent:o,UserProfileLinkComponent:u,UserProfilePageComponent:f,reorderItemsLabels:d})=>{const v=[],m=[],g=[];q.Children.forEach(i,w=>{if(!vt(w,l)&&!vt(w,u)&&!vt(w,f)){w&&yt(f_);return}if(vt(w,u)||vt(w,f))return;const{props:D}=w;q.Children.forEach(D.children,L=>{if(!vt(L,r)&&!vt(L,o)){L&&yt(d_);return}const{props:T}=L,{label:H,labelIcon:Y,href:F,onClick:P,open:Z}=T;if(vt(L,r))if(ep(T,d))v.push({label:H});else if(zc(T)){const re={label:H,labelIcon:Y};if(P!==void 0)v.push({...re,onClick:P});else if(Z!==void 0)v.push({...re,open:Z.startsWith("/")?Z:`/${Z}`});else{yt("Custom menu item must have either onClick or open property");return}}else{yt(v_);return}if(vt(L,o))if(Nc(T))v.push({label:H,labelIcon:Y,href:F});else{yt(p_);return}})});const b=[],C=[];v.forEach((w,D)=>{zc(w)&&b.push({component:w.labelIcon,id:D}),Nc(w)&&C.push({component:w.labelIcon,id:D})});const S=Ql(b),O=Ql(C);return v.forEach((w,D)=>{if(ep(w,d)&&m.push({label:w.label}),zc(w)){const{portal:L,mount:T,unmount:H}=S.find(F=>F.id===D),Y={label:w.label,mountIcon:T,unmountIcon:H};"onClick"in w?Y.onClick=w.onClick:"open"in w&&(Y.open=w.open),m.push(Y),g.push(L)}if(Nc(w)){const{portal:L,mount:T,unmount:H}=O.find(Y=>Y.id===D);m.push({label:w.label,href:w.href,mountIcon:T,unmountIcon:H}),g.push(L)}}),{customMenuItems:m,customMenuItemsPortals:g}},ep=(i,l)=>{const{children:r,label:o,onClick:u,labelIcon:f}=i;return!r&&!u&&!f&&l.some(d=>d===o)},zc=i=>{const{label:l,labelIcon:r,onClick:o,open:u}=i;return!!r&&!!l&&(typeof o=="function"||typeof u=="string")},Nc=i=>{const{label:l,href:r,labelIcon:o}=i;return!!r&&!!o&&!!l};function F_(i){const{root:l=document?.body,selector:r,timeout:o=0}=i;return new Promise((u,f)=>{if(!l){f(new Error("No root element provided"));return}let d=l;if(r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0){u();return}const m=new MutationObserver(g=>{for(const b of g)if(b.type==="childList"&&(!d&&r&&(d=l?.querySelector(r)),d?.childElementCount&&d.childElementCount>0)){m.disconnect(),u();return}});m.observe(l,{childList:!0,subtree:!0}),o>0&&setTimeout(()=>{m.disconnect(),f(new Error("Timeout waiting for element children"))},o)})}function Xt(i){const l=x.useRef(),[r,o]=x.useState("rendering");return x.useEffect(()=>{if(!i)throw new Error("Clerk: no component name provided, unable to detect mount.");typeof window<"u"&&!l.current&&(l.current=F_({selector:`[data-clerk-component="${i}"]`}).then(()=>{o("rendered")}).catch(()=>{o("error")}))},[i]),r}var Cs=i=>"mount"in i,tp=i=>"open"in i,np=i=>i?.map(({mountIcon:l,unmountIcon:r,...o})=>o),Tt=class extends q.PureComponent{constructor(){super(...arguments),this.rootRef=q.createRef()}componentDidUpdate(i){var l,r,o,u;if(!Cs(i)||!Cs(this.props))return;const f=Xg(i.props,"customPages","customMenuItems","children"),d=Xg(this.props.props,"customPages","customMenuItems","children"),v=((l=f.customPages)==null?void 0:l.length)!==((r=d.customPages)==null?void 0:r.length),m=((o=f.customMenuItems)==null?void 0:o.length)!==((u=d.customMenuItems)==null?void 0:u.length),g=np(i.props.customMenuItems),b=np(this.props.props.customMenuItems);(!Yg(f,d)||!Yg(g,b)||v||m)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(Cs(this.props)&&this.props.mount(this.rootRef.current,this.props.props),tp(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(Cs(this.props)&&this.props.unmount(this.rootRef.current),tp(this.props)&&this.props.close())}render(){const{hideRootHtmlElement:i=!1}=this.props,l={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return q.createElement(q.Fragment,null,!i&&q.createElement("div",{...l}),this.props.children)}},Is=i=>{var l,r;return q.createElement(q.Fragment,null,(l=i?.customPagesPortals)==null?void 0:l.map((o,u)=>x.createElement(o,{key:u})),(r=i?.customMenuItemsPortals)==null?void 0:r.map((o,u)=>x.createElement(o,{key:u})))},cx=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountSignIn,unmount:i.unmountSignIn,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"SignIn",renderWhileLoading:!0}),fx=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountSignUp,unmount:i.unmountSignUp,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"SignUp",renderWhileLoading:!0});function Zl({children:i}){return yt(a_),q.createElement(q.Fragment,null,i)}function Fl({children:i}){return yt(l_),q.createElement(q.Fragment,null,i)}var J_=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=iv(o.children);return q.createElement(q.Fragment,null,f&&r,q.createElement(Tt,{component:l,mount:i.mountUserProfile,unmount:i.unmountUserProfile,updateProps:i.__unstable__updateProps,props:{...o,customPages:v},rootProps:d},q.createElement(Is,{customPagesPortals:m})))},{component:"UserProfile",renderWhileLoading:!0});Object.assign(J_,{Page:Zl,Link:Fl});var sv=x.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),eE=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=iv(o.children,{allowForAnyChildren:!!o.__experimental_asProvider}),g=Object.assign(o.userProfileProps||{},{customPages:v}),{customMenuItems:b,customMenuItemsPortals:C}=W_(o.children),S=lv(o.children),O={mount:i.mountUserButton,unmount:i.unmountUserButton,updateProps:i.__unstable__updateProps,props:{...o,userProfileProps:g,customMenuItems:b}},w={customPagesPortals:m,customMenuItemsPortals:C};return q.createElement(sv.Provider,{value:O},f&&r,i.loaded&&q.createElement(Tt,{component:l,...O,hideRootHtmlElement:!!o.__experimental_asProvider,rootProps:d},o.__experimental_asProvider?S:null,q.createElement(Is,{...w})))},{component:"UserButton",renderWhileLoading:!0});function $s({children:i}){return yt(h_),q.createElement(q.Fragment,null,i)}function ov({children:i}){return yt(m_),q.createElement(q.Fragment,null,i)}function uv({children:i}){return yt(g_),q.createElement(q.Fragment,null,i)}function tE(i){const l=x.useContext(sv),r={...l,props:{...l.props,...i}};return q.createElement(Tt,{...r})}var dx=Object.assign(eE,{UserProfilePage:Zl,UserProfileLink:Fl,MenuItems:$s,Action:ov,Link:uv,__experimental_Outlet:tE});function Gs({children:i}){return yt(r_),q.createElement(q.Fragment,null,i)}function Xs({children:i}){return yt(s_),q.createElement(q.Fragment,null,i)}var nE=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=av(o.children);return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountOrganizationProfile,unmount:i.unmountOrganizationProfile,updateProps:i.__unstable__updateProps,props:{...o,customPages:v},rootProps:d},q.createElement(Is,{customPagesPortals:m})))},{component:"OrganizationProfile",renderWhileLoading:!0});Object.assign(nE,{Page:Gs,Link:Xs});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountCreateOrganization,unmount:i.unmountCreateOrganization,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"CreateOrganization",renderWhileLoading:!0});var cv=x.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),iE=Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}},{customPages:v,customPagesPortals:m}=av(o.children,{allowForAnyChildren:!!o.__experimental_asProvider}),g=Object.assign(o.organizationProfileProps||{},{customPages:v}),b=lv(o.children),C={mount:i.mountOrganizationSwitcher,unmount:i.unmountOrganizationSwitcher,updateProps:i.__unstable__updateProps,props:{...o,organizationProfileProps:g},rootProps:d,component:l};return i.__experimental_prefetchOrganizationSwitcher(),q.createElement(cv.Provider,{value:C},q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{...C,hideRootHtmlElement:!!o.__experimental_asProvider},o.__experimental_asProvider?b:null,q.createElement(Is,{customPagesPortals:m}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0});function aE(i){const l=x.useContext(cv),r={...l,props:{...l.props,...i}};return q.createElement(Tt,{...r})}Object.assign(iE,{OrganizationProfilePage:Gs,OrganizationProfileLink:Xs,__experimental_Outlet:aE});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountOrganizationList,unmount:i.unmountOrganizationList,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"OrganizationList",renderWhileLoading:!0});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,open:i.openGoogleOneTap,close:i.closeGoogleOneTap,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"GoogleOneTap",renderWhileLoading:!0});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountWaitlist,unmount:i.unmountWaitlist,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"Waitlist",renderWhileLoading:!0});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountPricingTable,unmount:i.unmountPricingTable,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"PricingTable",renderWhileLoading:!0});Pe(({clerk:i,component:l,fallback:r,...o})=>{const f=Xt(l)==="rendering"||!i.loaded,d={...f&&r&&{style:{display:"none"}}};return q.createElement(q.Fragment,null,f&&r,i.loaded&&q.createElement(Tt,{component:l,mount:i.mountApiKeys,unmount:i.unmountApiKeys,updateProps:i.__unstable__updateProps,props:o,rootProps:d}))},{component:"ApiKeys",renderWhileLoading:!0});Pe(({clerk:i,children:l,...r})=>{const{signUpFallbackRedirectUrl:o,forceRedirectUrl:u,fallbackRedirectUrl:f,signUpForceRedirectUrl:d,mode:v,initialValues:m,withSignUp:g,oauthFlow:b,...C}=r;l=Ps(l,"Sign in");const S=Qs(l)("SignInButton"),O=()=>{const L={forceRedirectUrl:u,fallbackRedirectUrl:f,signUpFallbackRedirectUrl:o,signUpForceRedirectUrl:d,initialValues:m,withSignUp:g,oauthFlow:b};return v==="modal"?i.openSignIn({...L,appearance:r.appearance}):i.redirectToSignIn({...L,signInFallbackRedirectUrl:f,signInForceRedirectUrl:u})},D={...C,onClick:async L=>(S&&typeof S=="object"&&"props"in S&&await Ys(S.props.onClick)(L),O())};return q.cloneElement(S,D)},{component:"SignInButton",renderWhileLoading:!0});Pe(({clerk:i,children:l,...r})=>{const{fallbackRedirectUrl:o,forceRedirectUrl:u,signInFallbackRedirectUrl:f,signInForceRedirectUrl:d,mode:v,unsafeMetadata:m,initialValues:g,oauthFlow:b,...C}=r;l=Ps(l,"Sign up");const S=Qs(l)("SignUpButton"),O=()=>{const L={fallbackRedirectUrl:o,forceRedirectUrl:u,signInFallbackRedirectUrl:f,signInForceRedirectUrl:d,unsafeMetadata:m,initialValues:g,oauthFlow:b};return v==="modal"?i.openSignUp({...L,appearance:r.appearance}):i.redirectToSignUp({...L,signUpFallbackRedirectUrl:o,signUpForceRedirectUrl:u})},D={...C,onClick:async L=>(S&&typeof S=="object"&&"props"in S&&await Ys(S.props.onClick)(L),O())};return q.cloneElement(S,D)},{component:"SignUpButton",renderWhileLoading:!0});Pe(({clerk:i,children:l,...r})=>{const{redirectUrl:o="/",signOutOptions:u,...f}=r;l=Ps(l,"Sign out");const d=Qs(l)("SignOutButton"),v=()=>i.signOut({redirectUrl:o,...u}),g={...f,onClick:async b=>(await Ys(d.props.onClick)(b),v())};return q.cloneElement(d,g)},{component:"SignOutButton",renderWhileLoading:!0});Pe(({clerk:i,children:l,...r})=>{const{redirectUrl:o,...u}=r;l=Ps(l,"Sign in with Metamask");const f=Qs(l)("SignInWithMetamaskButton"),d=async()=>{async function g(){await i.authenticateWithMetamask({redirectUrl:o||void 0})}g()},m={...u,onClick:async g=>(await Ys(f.props.onClick)(g),d())};return q.cloneElement(f,m)},{component:"SignInWithMetamask",renderWhileLoading:!0});typeof globalThis.__BUILD_DISABLE_RHC__>"u"&&(globalThis.__BUILD_DISABLE_RHC__=!1);var lE={name:"@clerk/clerk-react",version:"5.32.1",environment:"production"},ks,xa,Aa,ri,kn,oi,Ms,af,fv=class dv{constructor(l){Ui(this,Ms),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],Ui(this,ks,"loading"),Ui(this,xa),Ui(this,Aa),Ui(this,ri),Ui(this,kn,$_()),this.buildSignInUrl=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignInUrl(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildSignInUrl",f)},this.buildSignUpUrl=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildSignUpUrl(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildSignUpUrl",f)},this.buildAfterSignInUrl=(...u)=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignInUrl(...u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildAfterSignInUrl",f)},this.buildAfterSignUpUrl=(...u)=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildAfterSignUpUrl(...u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildAfterSignUpUrl",f)},this.buildAfterSignOutUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterSignOutUrl",u)},this.buildNewSubscriptionRedirectUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",u)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",u)},this.buildUserProfileUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildUserProfileUrl",u)},this.buildCreateOrganizationUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildCreateOrganizationUrl",u)},this.buildOrganizationProfileUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildOrganizationProfileUrl",u)},this.buildWaitlistUrl=()=>{const u=()=>{var f;return((f=this.clerkjs)==null?void 0:f.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("buildWaitlistUrl",u)},this.buildUrlWithAuth=u=>{const f=()=>{var d;return((d=this.clerkjs)==null?void 0:d.buildUrlWithAuth(u))||""};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("buildUrlWithAuth",f)},this.handleUnauthenticated=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.handleUnauthenticated()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("handleUnauthenticated",u)},this.on=(...u)=>{var f;if((f=this.clerkjs)!=null&&f.on)return this.clerkjs.on(...u);Be(this,kn).on(...u)},this.off=(...u)=>{var f;if((f=this.clerkjs)!=null&&f.off)return this.clerkjs.off(...u);Be(this,kn).off(...u)},this.addOnLoaded=u=>{this.loadedListeners.push(u),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(u=>u()),this.loadedListeners=[]},this.beforeLoad=u=>{if(!u)throw new Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=u=>{var f;if(!u)throw new Error("Failed to hydrate latest Clerk JS");return this.clerkjs=u,this.premountMethodCalls.forEach(d=>d()),this.premountAddListenerCalls.forEach((d,v)=>{d.nativeUnsubscribe=u.addListener(v)}),(f=Be(this,kn).internal.retrieveListeners("status"))==null||f.forEach(d=>{this.on("status",d,{notify:!0})}),this.preopenSignIn!==null&&u.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&u.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&u.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSignUp!==null&&u.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&u.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&u.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&u.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&u.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&u.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&u.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((d,v)=>{u.mountSignIn(v,d)}),this.premountSignUpNodes.forEach((d,v)=>{u.mountSignUp(v,d)}),this.premountUserProfileNodes.forEach((d,v)=>{u.mountUserProfile(v,d)}),this.premountUserButtonNodes.forEach((d,v)=>{u.mountUserButton(v,d)}),this.premountOrganizationListNodes.forEach((d,v)=>{u.mountOrganizationList(v,d)}),this.premountWaitlistNodes.forEach((d,v)=>{u.mountWaitlist(v,d)}),this.premountPricingTableNodes.forEach((d,v)=>{u.mountPricingTable(v,d)}),this.premountApiKeysNodes.forEach((d,v)=>{u.mountApiKeys(v,d)}),this.premountOAuthConsentNodes.forEach((d,v)=>{u.__internal_mountOAuthConsent(v,d)}),typeof this.clerkjs.status>"u"&&Be(this,kn).emit(As.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async u=>{const f=await $g(this,Ms,af).call(this);if(f&&"__unstable__updateProps"in f)return f.__unstable__updateProps(u)},this.__experimental_navigateToTask=async u=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(u):Promise.reject(),this.setActive=u=>this.clerkjs?this.clerkjs.setActive(u):Promise.reject(),this.openSignIn=u=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(u):this.preopenSignIn=u},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(u):this.preopenCheckout=u},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(u):this.preopenPlanDetails=u},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(u):this.preopenUserVerification=u},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=u=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(u):this.preopenOneTap=u},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(u):this.preopenUserProfile=u},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(u):this.preopenOrganizationProfile=u},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=u=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(u):this.preopenCreateOrganization=u},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=u=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(u):this.preOpenWaitlist=u},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=u=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(u):this.preopenSignUp=u},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(u,f):this.premountSignInNodes.set(u,f)},this.unmountSignIn=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(u):this.premountSignInNodes.delete(u)},this.mountSignUp=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(u,f):this.premountSignUpNodes.set(u,f)},this.unmountSignUp=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(u):this.premountSignUpNodes.delete(u)},this.mountUserProfile=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(u,f):this.premountUserProfileNodes.set(u,f)},this.unmountUserProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(u):this.premountUserProfileNodes.delete(u)},this.mountOrganizationProfile=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(u,f):this.premountOrganizationProfileNodes.set(u,f)},this.unmountOrganizationProfile=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(u):this.premountOrganizationProfileNodes.delete(u)},this.mountCreateOrganization=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(u,f):this.premountCreateOrganizationNodes.set(u,f)},this.unmountCreateOrganization=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(u):this.premountCreateOrganizationNodes.delete(u)},this.mountOrganizationSwitcher=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(u,f):this.premountOrganizationSwitcherNodes.set(u,f)},this.unmountOrganizationSwitcher=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(u):this.premountOrganizationSwitcherNodes.delete(u)},this.__experimental_prefetchOrganizationSwitcher=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",u)},this.mountOrganizationList=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(u,f):this.premountOrganizationListNodes.set(u,f)},this.unmountOrganizationList=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(u):this.premountOrganizationListNodes.delete(u)},this.mountUserButton=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(u,f):this.premountUserButtonNodes.set(u,f)},this.unmountUserButton=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(u):this.premountUserButtonNodes.delete(u)},this.mountWaitlist=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(u,f):this.premountWaitlistNodes.set(u,f)},this.unmountWaitlist=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(u):this.premountWaitlistNodes.delete(u)},this.mountPricingTable=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(u,f):this.premountPricingTableNodes.set(u,f)},this.unmountPricingTable=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(u):this.premountPricingTableNodes.delete(u)},this.mountApiKeys=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(u,f):this.premountApiKeysNodes.set(u,f)},this.unmountApiKeys=u=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(u):this.premountApiKeysNodes.delete(u)},this.__internal_mountOAuthConsent=(u,f)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(u,f):this.premountOAuthConsentNodes.set(u,f)},this.__internal_unmountOAuthConsent=u=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(u):this.premountOAuthConsentNodes.delete(u)},this.addListener=u=>{if(this.clerkjs)return this.clerkjs.addListener(u);{const f=()=>{var d;const v=this.premountAddListenerCalls.get(u);v&&((d=v.nativeUnsubscribe)==null||d.call(v),this.premountAddListenerCalls.delete(u))};return this.premountAddListenerCalls.set(u,{unsubscribe:f,nativeUnsubscribe:void 0}),f}},this.navigate=u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.navigate(u)};this.clerkjs&&this.loaded?f():this.premountMethodCalls.set("navigate",f)},this.redirectWithAuth=async(...u)=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectWithAuth(...u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectWithAuth",f)},this.redirectToSignIn=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignIn(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectToSignIn",f)},this.redirectToSignUp=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.redirectToSignUp(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("redirectToSignUp",f)},this.redirectToUserProfile=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToUserProfile",u)},this.redirectToAfterSignUp=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToAfterSignUp",u)},this.redirectToAfterSignIn=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignIn()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("redirectToAfterSignIn",u)},this.redirectToAfterSignOut=()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToAfterSignOut()};this.clerkjs&&this.loaded?u():this.premountMethodCalls.set("redirectToAfterSignOut",u)},this.redirectToOrganizationProfile=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToOrganizationProfile",u)},this.redirectToCreateOrganization=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToCreateOrganization",u)},this.redirectToWaitlist=async()=>{const u=()=>{var f;return(f=this.clerkjs)==null?void 0:f.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return u();this.premountMethodCalls.set("redirectToWaitlist",u)},this.handleRedirectCallback=async u=>{var f;const d=()=>{var v;return(v=this.clerkjs)==null?void 0:v.handleRedirectCallback(u)};this.clerkjs&&this.loaded?(f=d())==null||f.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",d)},this.handleGoogleOneTapCallback=async(u,f)=>{var d;const v=()=>{var m;return(m=this.clerkjs)==null?void 0:m.handleGoogleOneTapCallback(u,f)};this.clerkjs&&this.loaded?(d=v())==null||d.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",v)},this.handleEmailLinkVerification=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.handleEmailLinkVerification(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("handleEmailLinkVerification",f)},this.authenticateWithMetamask=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithMetamask(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithMetamask",f)},this.authenticateWithCoinbaseWallet=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithCoinbaseWallet(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",f)},this.authenticateWithOKXWallet=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithOKXWallet(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithOKXWallet",f)},this.authenticateWithWeb3=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.authenticateWithWeb3(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("authenticateWithWeb3",f)},this.authenticateWithGoogleOneTap=async u=>(await $g(this,Ms,af).call(this)).authenticateWithGoogleOneTap(u),this.createOrganization=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.createOrganization(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("createOrganization",f)},this.getOrganization=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.getOrganization(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("getOrganization",f)},this.joinWaitlist=async u=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.joinWaitlist(u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("joinWaitlist",f)},this.signOut=async(...u)=>{const f=()=>{var d;return(d=this.clerkjs)==null?void 0:d.signOut(...u)};if(this.clerkjs&&this.loaded)return f();this.premountMethodCalls.set("signOut",f)};const{Clerk:r=null,publishableKey:o}=l||{};Ea(this,ri,o),Ea(this,Aa,l?.proxyUrl),Ea(this,xa,l?.domain),this.options=l,this.Clerk=r,this.mode=Kg()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=lE),Be(this,kn).emit(As.Status,"loading"),Be(this,kn).prioritizedOn(As.Status,u=>Ea(this,ks,u)),Be(this,ri)&&this.loadClerkJS()}get publishableKey(){return Be(this,ri)}get loaded(){var l;return((l=this.clerkjs)==null?void 0:l.loaded)||!1}get status(){var l;return this.clerkjs?((l=this.clerkjs)==null?void 0:l.status)||(this.clerkjs.loaded?"ready":"loading"):Be(this,ks)}static getOrCreateInstance(l){return(!Kg()||!Be(this,oi)||l.Clerk&&Be(this,oi).Clerk!==l.Clerk||Be(this,oi).publishableKey!==l.publishableKey)&&Ea(this,oi,new dv(l)),Be(this,oi)}static clearInstance(){Ea(this,oi,null)}get domain(){return typeof window<"u"&&window.location?Uc(Be(this,xa),new URL(window.location.href),""):typeof Be(this,xa)=="function"?hn.throw(jc):Be(this,xa)||""}get proxyUrl(){return typeof window<"u"&&window.location?Uc(Be(this,Aa),new URL(window.location.href),""):typeof Be(this,Aa)=="function"?hn.throw(jc):Be(this,Aa)||""}__internal_getOption(l){var r,o;return(r=this.clerkjs)!=null&&r.__internal_getOption?(o=this.clerkjs)==null?void 0:o.__internal_getOption(l):this.options[l]}get sdkMetadata(){var l;return((l=this.clerkjs)==null?void 0:l.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var l;return(l=this.clerkjs)==null?void 0:l.instanceType}get frontendApi(){var l;return((l=this.clerkjs)==null?void 0:l.frontendApi)||""}get isStandardBrowser(){var l;return((l=this.clerkjs)==null?void 0:l.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return typeof window<"u"&&window.location?Uc(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite=="function"?hn.throw(jc):!1}async loadClerkJS(){var l;if(!(this.mode!=="browser"||this.loaded)){typeof window<"u"&&(window.__clerk_publishable_key=Be(this,ri),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let r;G_(this.Clerk)?(r=new this.Clerk(Be(this,ri),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(r),await r.load(this.options)):(r=this.Clerk,r.loaded||(this.beforeLoad(r),await r.load(this.options))),global.Clerk=r}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await N_({...this.options,publishableKey:Be(this,ri),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}return(l=global.Clerk)!=null&&l.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(r){const o=r;Be(this,kn).emit(As.Status,"error"),console.error(o.stack||o.message||o);return}}}get version(){var l;return(l=this.clerkjs)==null?void 0:l.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var l;return(l=this.clerkjs)==null?void 0:l.billing}get apiKeys(){var l;return(l=this.clerkjs)==null?void 0:l.apiKeys}__unstable__setEnvironment(...l){if(this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs)this.clerkjs.__unstable__setEnvironment(l);else return}};ks=new WeakMap;xa=new WeakMap;Aa=new WeakMap;ri=new WeakMap;kn=new WeakMap;oi=new WeakMap;Ms=new WeakSet;af=function(){return new Promise(i=>{this.addOnLoaded(()=>i(this.clerkjs))})};Ui(fv,oi);var ip=fv;function rE(i){const{isomorphicClerkOptions:l,initialState:r,children:o}=i,{isomorphicClerk:u,clerkStatus:f}=sE(l),[d,v]=q.useState({client:u.client,session:u.session,user:u.user,organization:u.organization});q.useEffect(()=>u.addListener(ve=>v({...ve})),[]);const m=Q_(u.loaded,d,r),g=q.useMemo(()=>({value:u}),[f]),b=q.useMemo(()=>({value:d.client}),[d.client]),{sessionId:C,sessionStatus:S,sessionClaims:O,session:w,userId:D,user:L,orgId:T,actor:H,organization:Y,orgRole:F,orgSlug:P,orgPermissions:Z,factorVerificationAge:re}=m,X=q.useMemo(()=>({value:{sessionId:C,sessionStatus:S,sessionClaims:O,userId:D,actor:H,orgId:T,orgRole:F,orgSlug:P,orgPermissions:Z,factorVerificationAge:re}}),[C,S,D,H,T,F,P,re,O?.__raw]),se=q.useMemo(()=>({value:w}),[C,w]),ie=q.useMemo(()=>({value:L}),[D,L]),be=q.useMemo(()=>({value:{organization:Y}}),[T,Y]);return q.createElement(e_.Provider,{value:g},q.createElement(Y1.Provider,{value:b},q.createElement(I1.Provider,{value:se},q.createElement(G1,{...be.value},q.createElement(F1.Provider,{value:X},q.createElement(Q1.Provider,{value:ie},o))))))}var sE=i=>{const l=q.useRef(ip.getOrCreateInstance(i)),[r,o]=q.useState(l.current.status);return q.useEffect(()=>{l.current.__unstable__updateProps({appearance:i.appearance})},[i.appearance]),q.useEffect(()=>{l.current.__unstable__updateProps({options:i})},[i.localization]),q.useEffect(()=>(l.current.on("status",o),()=>{l.current&&l.current.off("status",o),ip.clearInstance()}),[]),{isomorphicClerk:l.current,clerkStatus:r}};function oE(i){const{initialState:l,children:r,__internal_bypassMissingPublishableKey:o,...u}=i,{publishableKey:f="",Clerk:d}=u;return!d&&!o&&(f?f&&!Yc(f)&&hn.throwInvalidPublishableKeyError({key:f}):hn.throwMissingPublishableKeyError()),q.createElement(rE,{initialState:l,isomorphicClerkOptions:u},r)}var hv=K_(oE,"ClerkProvider",t_);hv.displayName="ClerkProvider";Z1({packageName:"@clerk/clerk-react"});z_("@clerk/clerk-react");var Ll={},ap;function uE(){if(ap)return Ll;ap=1,Object.defineProperty(Ll,"__esModule",{value:!0}),Ll.parse=d,Ll.serialize=g;const i=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,u=Object.prototype.toString,f=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function d(S,O){const w=new f,D=S.length;if(D<2)return w;const L=O?.decode||b;let T=0;do{const H=S.indexOf("=",T);if(H===-1)break;const Y=S.indexOf(";",T),F=Y===-1?D:Y;if(H>F){T=S.lastIndexOf(";",H-1)+1;continue}const P=v(S,T,H),Z=m(S,H,P),re=S.slice(P,Z);if(w[re]===void 0){let X=v(S,H+1,F),se=m(S,F,X);const ie=L(S.slice(X,se));w[re]=ie}T=F+1}while(T<D);return w}function v(S,O,w){do{const D=S.charCodeAt(O);if(D!==32&&D!==9)return O}while(++O<w);return w}function m(S,O,w){for(;O>w;){const D=S.charCodeAt(--O);if(D!==32&&D!==9)return O+1}return w}function g(S,O,w){const D=w?.encode||encodeURIComponent;if(!i.test(S))throw new TypeError(`argument name is invalid: ${S}`);const L=D(O);if(!l.test(L))throw new TypeError(`argument val is invalid: ${O}`);let T=S+"="+L;if(!w)return T;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);T+="; Max-Age="+w.maxAge}if(w.domain){if(!r.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);T+="; Domain="+w.domain}if(w.path){if(!o.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);T+="; Path="+w.path}if(w.expires){if(!C(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);T+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(T+="; HttpOnly"),w.secure&&(T+="; Secure"),w.partitioned&&(T+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":T+="; Priority=Low";break;case"medium":T+="; Priority=Medium";break;case"high":T+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":T+="; SameSite=Strict";break;case"lax":T+="; SameSite=Lax";break;case"none":T+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return T}function b(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function C(S){return u.call(S)==="[object Date]"}return Ll}uE();var lp="popstate";function cE(i={}){function l(o,u){let{pathname:f,search:d,hash:v}=o.location;return lf("",{pathname:f,search:d,hash:v},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function r(o,u){return typeof u=="string"?u:Xl(u)}return dE(l,r,null,i)}function Le(i,l){if(i===!1||i===null||typeof i>"u")throw new Error(l)}function Jt(i,l){if(!i){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function fE(){return Math.random().toString(36).substring(2,10)}function rp(i,l){return{usr:i.state,key:i.key,idx:l}}function lf(i,l,r=null,o){return{pathname:typeof i=="string"?i:i.pathname,search:"",hash:"",...typeof l=="string"?ja(l):l,state:r,key:l&&l.key||o||fE()}}function Xl({pathname:i="/",search:l="",hash:r=""}){return l&&l!=="?"&&(i+=l.charAt(0)==="?"?l:"?"+l),r&&r!=="#"&&(i+=r.charAt(0)==="#"?r:"#"+r),i}function ja(i){let l={};if(i){let r=i.indexOf("#");r>=0&&(l.hash=i.substring(r),i=i.substring(0,r));let o=i.indexOf("?");o>=0&&(l.search=i.substring(o),i=i.substring(0,o)),i&&(l.pathname=i)}return l}function dE(i,l,r,o={}){let{window:u=document.defaultView,v5Compat:f=!1}=o,d=u.history,v="POP",m=null,g=b();g==null&&(g=0,d.replaceState({...d.state,idx:g},""));function b(){return(d.state||{idx:null}).idx}function C(){v="POP";let L=b(),T=L==null?null:L-g;g=L,m&&m({action:v,location:D.location,delta:T})}function S(L,T){v="PUSH";let H=lf(D.location,L,T);g=b()+1;let Y=rp(H,g),F=D.createHref(H);try{d.pushState(Y,"",F)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;u.location.assign(F)}f&&m&&m({action:v,location:D.location,delta:1})}function O(L,T){v="REPLACE";let H=lf(D.location,L,T);g=b();let Y=rp(H,g),F=D.createHref(H);d.replaceState(Y,"",F),f&&m&&m({action:v,location:D.location,delta:0})}function w(L){return hE(L)}let D={get action(){return v},get location(){return i(u,d)},listen(L){if(m)throw new Error("A history only accepts one active listener");return u.addEventListener(lp,C),m=L,()=>{u.removeEventListener(lp,C),m=null}},createHref(L){return l(u,L)},createURL:w,encodeLocation(L){let T=w(L);return{pathname:T.pathname,search:T.search,hash:T.hash}},push:S,replace:O,go(L){return d.go(L)}};return D}function hE(i,l=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),Le(r,"No window.location.(origin|href) available to create URL");let o=typeof i=="string"?i:Xl(i);return o=o.replace(/ $/,"%20"),!l&&o.startsWith("//")&&(o=r+o),new URL(o,r)}function mv(i,l,r="/"){return mE(i,l,r,!1)}function mE(i,l,r,o){let u=typeof l=="string"?ja(l):l,f=Dn(u.pathname||"/",r);if(f==null)return null;let d=gv(i);gE(d);let v=null;for(let m=0;v==null&&m<d.length;++m){let g=TE(f);v=xE(d[m],g,o)}return v}function gv(i,l=[],r=[],o=""){let u=(f,d,v)=>{let m={relativePath:v===void 0?f.path||"":v,caseSensitive:f.caseSensitive===!0,childrenIndex:d,route:f};m.relativePath.startsWith("/")&&(Le(m.relativePath.startsWith(o),`Absolute route path "${m.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),m.relativePath=m.relativePath.slice(o.length));let g=jn([o,m.relativePath]),b=r.concat(m);f.children&&f.children.length>0&&(Le(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${g}".`),gv(f.children,l,b,g)),!(f.path==null&&!f.index)&&l.push({path:g,score:EE(g,f.index),routesMeta:b})};return i.forEach((f,d)=>{if(f.path===""||!f.path?.includes("?"))u(f,d);else for(let v of pv(f.path))u(f,d,v)}),l}function pv(i){let l=i.split("/");if(l.length===0)return[];let[r,...o]=l,u=r.endsWith("?"),f=r.replace(/\?$/,"");if(o.length===0)return u?[f,""]:[f];let d=pv(o.join("/")),v=[];return v.push(...d.map(m=>m===""?f:[f,m].join("/"))),u&&v.push(...d),v.map(m=>i.startsWith("/")&&m===""?"/":m)}function gE(i){i.sort((l,r)=>l.score!==r.score?r.score-l.score:wE(l.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}var pE=/^:[\w-]+$/,vE=3,yE=2,bE=1,SE=10,_E=-2,sp=i=>i==="*";function EE(i,l){let r=i.split("/"),o=r.length;return r.some(sp)&&(o+=_E),l&&(o+=yE),r.filter(u=>!sp(u)).reduce((u,f)=>u+(pE.test(f)?vE:f===""?bE:SE),o)}function wE(i,l){return i.length===l.length&&i.slice(0,-1).every((o,u)=>o===l[u])?i[i.length-1]-l[l.length-1]:0}function xE(i,l,r=!1){let{routesMeta:o}=i,u={},f="/",d=[];for(let v=0;v<o.length;++v){let m=o[v],g=v===o.length-1,b=f==="/"?l:l.slice(f.length)||"/",C=qs({path:m.relativePath,caseSensitive:m.caseSensitive,end:g},b),S=m.route;if(!C&&g&&r&&!o[o.length-1].route.index&&(C=qs({path:m.relativePath,caseSensitive:m.caseSensitive,end:!1},b)),!C)return null;Object.assign(u,C.params),d.push({params:u,pathname:jn([f,C.pathname]),pathnameBase:kE(jn([f,C.pathnameBase])),route:S}),C.pathnameBase!=="/"&&(f=jn([f,C.pathnameBase]))}return d}function qs(i,l){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[r,o]=AE(i.path,i.caseSensitive,i.end),u=l.match(r);if(!u)return null;let f=u[0],d=f.replace(/(.)\/+$/,"$1"),v=u.slice(1);return{params:o.reduce((g,{paramName:b,isOptional:C},S)=>{if(b==="*"){let w=v[S]||"";d=f.slice(0,f.length-w.length).replace(/(.)\/+$/,"$1")}const O=v[S];return C&&!O?g[b]=void 0:g[b]=(O||"").replace(/%2F/g,"/"),g},{}),pathname:f,pathnameBase:d,pattern:i}}function AE(i,l=!1,r=!0){Jt(i==="*"||!i.endsWith("*")||i.endsWith("/*"),`Route path "${i}" will be treated as if it were "${i.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${i.replace(/\*$/,"/*")}".`);let o=[],u="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,v,m)=>(o.push({paramName:v,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(o.push({paramName:"*"}),u+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?u+="\\/*$":i!==""&&i!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,l?void 0:"i"),o]}function TE(i){try{return i.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return Jt(!1,`The URL path "${i}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),i}}function Dn(i,l){if(l==="/")return i;if(!i.toLowerCase().startsWith(l.toLowerCase()))return null;let r=l.endsWith("/")?l.length-1:l.length,o=i.charAt(r);return o&&o!=="/"?null:i.slice(r)||"/"}function CE(i,l="/"){let{pathname:r,search:o="",hash:u=""}=typeof i=="string"?ja(i):i;return{pathname:r?r.startsWith("/")?r:OE(r,l):l,search:ME(o),hash:jE(u)}}function OE(i,l){let r=l.replace(/\/+$/,"").split("/");return i.split("/").forEach(u=>{u===".."?r.length>1&&r.pop():u!=="."&&r.push(u)}),r.length>1?r.join("/"):"/"}function qc(i,l,r,o){return`Cannot include a '${i}' character in a manually specified \`to.${l}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function RE(i){return i.filter((l,r)=>r===0||l.route.path&&l.route.path.length>0)}function mf(i){let l=RE(i);return l.map((r,o)=>o===l.length-1?r.pathname:r.pathnameBase)}function gf(i,l,r,o=!1){let u;typeof i=="string"?u=ja(i):(u={...i},Le(!u.pathname||!u.pathname.includes("?"),qc("?","pathname","search",u)),Le(!u.pathname||!u.pathname.includes("#"),qc("#","pathname","hash",u)),Le(!u.search||!u.search.includes("#"),qc("#","search","hash",u)));let f=i===""||u.pathname==="",d=f?"/":u.pathname,v;if(d==null)v=r;else{let C=l.length-1;if(!o&&d.startsWith("..")){let S=d.split("/");for(;S[0]==="..";)S.shift(),C-=1;u.pathname=S.join("/")}v=C>=0?l[C]:"/"}let m=CE(u,v),g=d&&d!=="/"&&d.endsWith("/"),b=(f||d===".")&&r.endsWith("/");return!m.pathname.endsWith("/")&&(g||b)&&(m.pathname+="/"),m}var jn=i=>i.join("/").replace(/\/\/+/g,"/"),kE=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),ME=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,jE=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function UE(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}var vv=["POST","PUT","PATCH","DELETE"];new Set(vv);var DE=["GET",...vv];new Set(DE);var Ua=x.createContext(null);Ua.displayName="DataRouter";var Ks=x.createContext(null);Ks.displayName="DataRouterState";var yv=x.createContext({isTransitioning:!1});yv.displayName="ViewTransition";var LE=x.createContext(new Map);LE.displayName="Fetchers";var zE=x.createContext(null);zE.displayName="Await";var en=x.createContext(null);en.displayName="Navigation";var Jl=x.createContext(null);Jl.displayName="Location";var tn=x.createContext({outlet:null,matches:[],isDataRoute:!1});tn.displayName="Route";var pf=x.createContext(null);pf.displayName="RouteError";function NE(i,{relative:l}={}){Le(Da(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=x.useContext(en),{hash:u,pathname:f,search:d}=er(i,{relative:l}),v=f;return r!=="/"&&(v=f==="/"?r:jn([r,f])),o.createHref({pathname:v,search:d,hash:u})}function Da(){return x.useContext(Jl)!=null}function di(){return Le(Da(),"useLocation() may be used only in the context of a <Router> component."),x.useContext(Jl).location}var bv="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Sv(i){x.useContext(en).static||x.useLayoutEffect(i)}function _v(){let{isDataRoute:i}=x.useContext(tn);return i?WE():qE()}function qE(){Le(Da(),"useNavigate() may be used only in the context of a <Router> component.");let i=x.useContext(Ua),{basename:l,navigator:r}=x.useContext(en),{matches:o}=x.useContext(tn),{pathname:u}=di(),f=JSON.stringify(mf(o)),d=x.useRef(!1);return Sv(()=>{d.current=!0}),x.useCallback((m,g={})=>{if(Jt(d.current,bv),!d.current)return;if(typeof m=="number"){r.go(m);return}let b=gf(m,JSON.parse(f),u,g.relative==="path");i==null&&l!=="/"&&(b.pathname=b.pathname==="/"?l:jn([l,b.pathname])),(g.replace?r.replace:r.push)(b,g.state,g)},[l,r,f,u,i])}x.createContext(null);function hx(){let{matches:i}=x.useContext(tn),l=i[i.length-1];return l?l.params:{}}function er(i,{relative:l}={}){let{matches:r}=x.useContext(tn),{pathname:o}=di(),u=JSON.stringify(mf(r));return x.useMemo(()=>gf(i,JSON.parse(u),o,l==="path"),[i,u,o,l])}function BE(i,l){return Ev(i,l)}function Ev(i,l,r,o){Le(Da(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:u}=x.useContext(en),{matches:f}=x.useContext(tn),d=f[f.length-1],v=d?d.params:{},m=d?d.pathname:"/",g=d?d.pathnameBase:"/",b=d&&d.route;{let T=b&&b.path||"";wv(m,!b||T.endsWith("*")||T.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${m}" (under <Route path="${T}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${T}"> to <Route path="${T==="/"?"*":`${T}/*`}">.`)}let C=di(),S;if(l){let T=typeof l=="string"?ja(l):l;Le(g==="/"||T.pathname?.startsWith(g),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${g}" but pathname "${T.pathname}" was given in the \`location\` prop.`),S=T}else S=C;let O=S.pathname||"/",w=O;if(g!=="/"){let T=g.replace(/^\//,"").split("/");w="/"+O.replace(/^\//,"").split("/").slice(T.length).join("/")}let D=mv(i,{pathname:w});Jt(b||D!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),Jt(D==null||D[D.length-1].route.element!==void 0||D[D.length-1].route.Component!==void 0||D[D.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=YE(D&&D.map(T=>Object.assign({},T,{params:Object.assign({},v,T.params),pathname:jn([g,u.encodeLocation?u.encodeLocation(T.pathname).pathname:T.pathname]),pathnameBase:T.pathnameBase==="/"?g:jn([g,u.encodeLocation?u.encodeLocation(T.pathnameBase).pathname:T.pathnameBase])})),f,r,o);return l&&L?x.createElement(Jl.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...S},navigationType:"POP"}},L):L}function HE(){let i=KE(),l=UE(i)?`${i.status} ${i.statusText}`:i instanceof Error?i.message:JSON.stringify(i),r=i instanceof Error?i.stack:null,o="rgba(200,200,200, 0.5)",u={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},d=null;return console.error("Error handled by React Router default ErrorBoundary:",i),d=x.createElement(x.Fragment,null,x.createElement("p",null,"💿 Hey developer 👋"),x.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",x.createElement("code",{style:f},"ErrorBoundary")," or"," ",x.createElement("code",{style:f},"errorElement")," prop on your route.")),x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},l),r?x.createElement("pre",{style:u},r):null,d)}var VE=x.createElement(HE,null),QE=class extends x.Component{constructor(i){super(i),this.state={location:i.location,revalidation:i.revalidation,error:i.error}}static getDerivedStateFromError(i){return{error:i}}static getDerivedStateFromProps(i,l){return l.location!==i.location||l.revalidation!=="idle"&&i.revalidation==="idle"?{error:i.error,location:i.location,revalidation:i.revalidation}:{error:i.error!==void 0?i.error:l.error,location:l.location,revalidation:i.revalidation||l.revalidation}}componentDidCatch(i,l){console.error("React Router caught the following error during render",i,l)}render(){return this.state.error!==void 0?x.createElement(tn.Provider,{value:this.props.routeContext},x.createElement(pf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function PE({routeContext:i,match:l,children:r}){let o=x.useContext(Ua);return o&&o.static&&o.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=l.route.id),x.createElement(tn.Provider,{value:i},r)}function YE(i,l=[],r=null,o=null){if(i==null){if(!r)return null;if(r.errors)i=r.matches;else if(l.length===0&&!r.initialized&&r.matches.length>0)i=r.matches;else return null}let u=i,f=r?.errors;if(f!=null){let m=u.findIndex(g=>g.route.id&&f?.[g.route.id]!==void 0);Le(m>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),u=u.slice(0,Math.min(u.length,m+1))}let d=!1,v=-1;if(r)for(let m=0;m<u.length;m++){let g=u[m];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(v=m),g.route.id){let{loaderData:b,errors:C}=r,S=g.route.loader&&!b.hasOwnProperty(g.route.id)&&(!C||C[g.route.id]===void 0);if(g.route.lazy||S){d=!0,v>=0?u=u.slice(0,v+1):u=[u[0]];break}}}return u.reduceRight((m,g,b)=>{let C,S=!1,O=null,w=null;r&&(C=f&&g.route.id?f[g.route.id]:void 0,O=g.route.errorElement||VE,d&&(v<0&&b===0?(wv("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,w=null):v===b&&(S=!0,w=g.route.hydrateFallbackElement||null)));let D=l.concat(u.slice(0,b+1)),L=()=>{let T;return C?T=O:S?T=w:g.route.Component?T=x.createElement(g.route.Component,null):g.route.element?T=g.route.element:T=m,x.createElement(PE,{match:g,routeContext:{outlet:m,matches:D,isDataRoute:r!=null},children:T})};return r&&(g.route.ErrorBoundary||g.route.errorElement||b===0)?x.createElement(QE,{location:r.location,revalidation:r.revalidation,component:O,error:C,children:L(),routeContext:{outlet:null,matches:D,isDataRoute:!0}}):L()},null)}function vf(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function IE(i){let l=x.useContext(Ua);return Le(l,vf(i)),l}function $E(i){let l=x.useContext(Ks);return Le(l,vf(i)),l}function GE(i){let l=x.useContext(tn);return Le(l,vf(i)),l}function yf(i){let l=GE(i),r=l.matches[l.matches.length-1];return Le(r.route.id,`${i} can only be used on routes that contain a unique "id"`),r.route.id}function XE(){return yf("useRouteId")}function KE(){let i=x.useContext(pf),l=$E("useRouteError"),r=yf("useRouteError");return i!==void 0?i:l.errors?.[r]}function WE(){let{router:i}=IE("useNavigate"),l=yf("useNavigate"),r=x.useRef(!1);return Sv(()=>{r.current=!0}),x.useCallback(async(u,f={})=>{Jt(r.current,bv),r.current&&(typeof u=="number"?i.navigate(u):await i.navigate(u,{fromRouteId:l,...f}))},[i,l])}var op={};function wv(i,l,r){!l&&!op[i]&&(op[i]=!0,Jt(!1,r))}x.memo(ZE);function ZE({routes:i,future:l,state:r}){return Ev(i,void 0,r,l)}function Os({to:i,replace:l,state:r,relative:o}){Le(Da(),"<Navigate> may be used only in the context of a <Router> component.");let{static:u}=x.useContext(en);Jt(!u,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=x.useContext(tn),{pathname:d}=di(),v=_v(),m=gf(i,mf(f),d,o==="path"),g=JSON.stringify(m);return x.useEffect(()=>{v(JSON.parse(g),{replace:l,state:r,relative:o})},[v,g,o,l,r]),null}function Di(i){Le(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function FE({basename:i="/",children:l=null,location:r,navigationType:o="POP",navigator:u,static:f=!1}){Le(!Da(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=i.replace(/^\/*/,"/"),v=x.useMemo(()=>({basename:d,navigator:u,static:f,future:{}}),[d,u,f]);typeof r=="string"&&(r=ja(r));let{pathname:m="/",search:g="",hash:b="",state:C=null,key:S="default"}=r,O=x.useMemo(()=>{let w=Dn(m,d);return w==null?null:{location:{pathname:w,search:g,hash:b,state:C,key:S},navigationType:o}},[d,m,g,b,C,S,o]);return Jt(O!=null,`<Router basename="${d}"> is not able to match the URL "${m}${g}${b}" because it does not start with the basename, so the <Router> won't render anything.`),O==null?null:x.createElement(en.Provider,{value:v},x.createElement(Jl.Provider,{children:l,value:O}))}function JE({children:i,location:l}){return BE(rf(i),l)}function rf(i,l=[]){let r=[];return x.Children.forEach(i,(o,u)=>{if(!x.isValidElement(o))return;let f=[...l,u];if(o.type===x.Fragment){r.push.apply(r,rf(o.props.children,f));return}Le(o.type===Di,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Le(!o.props.index||!o.props.children,"An index route cannot have child routes.");let d={id:o.props.id||f.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(d.children=rf(o.props.children,f)),r.push(d)}),r}var js="get",Us="application/x-www-form-urlencoded";function Ws(i){return i!=null&&typeof i.tagName=="string"}function ew(i){return Ws(i)&&i.tagName.toLowerCase()==="button"}function tw(i){return Ws(i)&&i.tagName.toLowerCase()==="form"}function nw(i){return Ws(i)&&i.tagName.toLowerCase()==="input"}function iw(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function aw(i,l){return i.button===0&&(!l||l==="_self")&&!iw(i)}var Rs=null;function lw(){if(Rs===null)try{new FormData(document.createElement("form"),0),Rs=!1}catch{Rs=!0}return Rs}var rw=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Bc(i){return i!=null&&!rw.has(i)?(Jt(!1,`"${i}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Us}"`),null):i}function sw(i,l){let r,o,u,f,d;if(tw(i)){let v=i.getAttribute("action");o=v?Dn(v,l):null,r=i.getAttribute("method")||js,u=Bc(i.getAttribute("enctype"))||Us,f=new FormData(i)}else if(ew(i)||nw(i)&&(i.type==="submit"||i.type==="image")){let v=i.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let m=i.getAttribute("formaction")||v.getAttribute("action");if(o=m?Dn(m,l):null,r=i.getAttribute("formmethod")||v.getAttribute("method")||js,u=Bc(i.getAttribute("formenctype"))||Bc(v.getAttribute("enctype"))||Us,f=new FormData(v,i),!lw()){let{name:g,type:b,value:C}=i;if(b==="image"){let S=g?`${g}.`:"";f.append(`${S}x`,"0"),f.append(`${S}y`,"0")}else g&&f.append(g,C)}}else{if(Ws(i))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=js,o=null,u=Us,d=i}return f&&u==="text/plain"&&(d=f,f=void 0),{action:o,method:r.toLowerCase(),encType:u,formData:f,body:d}}function bf(i,l){if(i===!1||i===null||typeof i>"u")throw new Error(l)}async function ow(i,l){if(i.id in l)return l[i.id];try{let r=await import(i.module);return l[i.id]=r,r}catch(r){return console.error(`Error loading route module \`${i.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function uw(i){return i==null?!1:i.href==null?i.rel==="preload"&&typeof i.imageSrcSet=="string"&&typeof i.imageSizes=="string":typeof i.rel=="string"&&typeof i.href=="string"}async function cw(i,l,r){let o=await Promise.all(i.map(async u=>{let f=l.routes[u.route.id];if(f){let d=await ow(f,r);return d.links?d.links():[]}return[]}));return mw(o.flat(1).filter(uw).filter(u=>u.rel==="stylesheet"||u.rel==="preload").map(u=>u.rel==="stylesheet"?{...u,rel:"prefetch",as:"style"}:{...u,rel:"prefetch"}))}function up(i,l,r,o,u,f){let d=(m,g)=>r[g]?m.route.id!==r[g].route.id:!0,v=(m,g)=>r[g].pathname!==m.pathname||r[g].route.path?.endsWith("*")&&r[g].params["*"]!==m.params["*"];return f==="assets"?l.filter((m,g)=>d(m,g)||v(m,g)):f==="data"?l.filter((m,g)=>{let b=o.routes[m.route.id];if(!b||!b.hasLoader)return!1;if(d(m,g)||v(m,g))return!0;if(m.route.shouldRevalidate){let C=m.route.shouldRevalidate({currentUrl:new URL(u.pathname+u.search+u.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(i,window.origin),nextParams:m.params,defaultShouldRevalidate:!0});if(typeof C=="boolean")return C}return!0}):[]}function fw(i,l,{includeHydrateFallback:r}={}){return dw(i.map(o=>{let u=l.routes[o.route.id];if(!u)return[];let f=[u.module];return u.clientActionModule&&(f=f.concat(u.clientActionModule)),u.clientLoaderModule&&(f=f.concat(u.clientLoaderModule)),r&&u.hydrateFallbackModule&&(f=f.concat(u.hydrateFallbackModule)),u.imports&&(f=f.concat(u.imports)),f}).flat(1))}function dw(i){return[...new Set(i)]}function hw(i){let l={},r=Object.keys(i).sort();for(let o of r)l[o]=i[o];return l}function mw(i,l){let r=new Set;return new Set(l),i.reduce((o,u)=>{let f=JSON.stringify(hw(u));return r.has(f)||(r.add(f),o.push({key:f,link:u})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var gw=new Set([100,101,204,205]);function pw(i,l){let r=typeof i=="string"?new URL(i,typeof window>"u"?"server://singlefetch/":window.location.origin):i;return r.pathname==="/"?r.pathname="_root.data":l&&Dn(r.pathname,l)==="/"?r.pathname=`${l.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function xv(){let i=x.useContext(Ua);return bf(i,"You must render this element inside a <DataRouterContext.Provider> element"),i}function vw(){let i=x.useContext(Ks);return bf(i,"You must render this element inside a <DataRouterStateContext.Provider> element"),i}var Sf=x.createContext(void 0);Sf.displayName="FrameworkContext";function Av(){let i=x.useContext(Sf);return bf(i,"You must render this element inside a <HydratedRouter> element"),i}function yw(i,l){let r=x.useContext(Sf),[o,u]=x.useState(!1),[f,d]=x.useState(!1),{onFocus:v,onBlur:m,onMouseEnter:g,onMouseLeave:b,onTouchStart:C}=l,S=x.useRef(null);x.useEffect(()=>{if(i==="render"&&d(!0),i==="viewport"){let D=T=>{T.forEach(H=>{d(H.isIntersecting)})},L=new IntersectionObserver(D,{threshold:.5});return S.current&&L.observe(S.current),()=>{L.disconnect()}}},[i]),x.useEffect(()=>{if(o){let D=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(D)}}},[o]);let O=()=>{u(!0)},w=()=>{u(!1),d(!1)};return r?i!=="intent"?[f,S,{}]:[f,S,{onFocus:zl(v,O),onBlur:zl(m,w),onMouseEnter:zl(g,O),onMouseLeave:zl(b,w),onTouchStart:zl(C,O)}]:[!1,S,{}]}function zl(i,l){return r=>{i&&i(r),r.defaultPrevented||l(r)}}function bw({page:i,...l}){let{router:r}=xv(),o=x.useMemo(()=>mv(r.routes,i,r.basename),[r.routes,i,r.basename]);return o?x.createElement(_w,{page:i,matches:o,...l}):null}function Sw(i){let{manifest:l,routeModules:r}=Av(),[o,u]=x.useState([]);return x.useEffect(()=>{let f=!1;return cw(i,l,r).then(d=>{f||u(d)}),()=>{f=!0}},[i,l,r]),o}function _w({page:i,matches:l,...r}){let o=di(),{manifest:u,routeModules:f}=Av(),{basename:d}=xv(),{loaderData:v,matches:m}=vw(),g=x.useMemo(()=>up(i,l,m,u,o,"data"),[i,l,m,u,o]),b=x.useMemo(()=>up(i,l,m,u,o,"assets"),[i,l,m,u,o]),C=x.useMemo(()=>{if(i===o.pathname+o.search+o.hash)return[];let w=new Set,D=!1;if(l.forEach(T=>{let H=u.routes[T.route.id];!H||!H.hasLoader||(!g.some(Y=>Y.route.id===T.route.id)&&T.route.id in v&&f[T.route.id]?.shouldRevalidate||H.hasClientLoader?D=!0:w.add(T.route.id))}),w.size===0)return[];let L=pw(i,d);return D&&w.size>0&&L.searchParams.set("_routes",l.filter(T=>w.has(T.route.id)).map(T=>T.route.id).join(",")),[L.pathname+L.search]},[d,v,o,u,g,l,i,f]),S=x.useMemo(()=>fw(b,u),[b,u]),O=Sw(b);return x.createElement(x.Fragment,null,C.map(w=>x.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...r})),S.map(w=>x.createElement("link",{key:w,rel:"modulepreload",href:w,...r})),O.map(({key:w,link:D})=>x.createElement("link",{key:w,...D})))}function Ew(...i){return l=>{i.forEach(r=>{typeof r=="function"?r(l):r!=null&&(r.current=l)})}}var Tv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Tv&&(window.__reactRouterVersion="7.6.2")}catch{}function ww({basename:i,children:l,window:r}){let o=x.useRef();o.current==null&&(o.current=cE({window:r,v5Compat:!0}));let u=o.current,[f,d]=x.useState({action:u.action,location:u.location}),v=x.useCallback(m=>{x.startTransition(()=>d(m))},[d]);return x.useLayoutEffect(()=>u.listen(v),[u,v]),x.createElement(FE,{basename:i,children:l,location:f.location,navigationType:f.action,navigator:u})}var Cv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ov=x.forwardRef(function({onClick:l,discover:r="render",prefetch:o="none",relative:u,reloadDocument:f,replace:d,state:v,target:m,to:g,preventScrollReset:b,viewTransition:C,...S},O){let{basename:w}=x.useContext(en),D=typeof g=="string"&&Cv.test(g),L,T=!1;if(typeof g=="string"&&D&&(L=g,Tv))try{let se=new URL(window.location.href),ie=g.startsWith("//")?new URL(se.protocol+g):new URL(g),be=Dn(ie.pathname,w);ie.origin===se.origin&&be!=null?g=be+ie.search+ie.hash:T=!0}catch{Jt(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let H=NE(g,{relative:u}),[Y,F,P]=yw(o,S),Z=Cw(g,{replace:d,state:v,target:m,preventScrollReset:b,relative:u,viewTransition:C});function re(se){l&&l(se),se.defaultPrevented||Z(se)}let X=x.createElement("a",{...S,...P,href:L||H,onClick:T||f?l:re,ref:Ew(O,F),target:m,"data-discover":!D&&r==="render"?"true":void 0});return Y&&!D?x.createElement(x.Fragment,null,X,x.createElement(bw,{page:H})):X});Ov.displayName="Link";var xw=x.forwardRef(function({"aria-current":l="page",caseSensitive:r=!1,className:o="",end:u=!1,style:f,to:d,viewTransition:v,children:m,...g},b){let C=er(d,{relative:g.relative}),S=di(),O=x.useContext(Ks),{navigator:w,basename:D}=x.useContext(en),L=O!=null&&jw(C)&&v===!0,T=w.encodeLocation?w.encodeLocation(C).pathname:C.pathname,H=S.pathname,Y=O&&O.navigation&&O.navigation.location?O.navigation.location.pathname:null;r||(H=H.toLowerCase(),Y=Y?Y.toLowerCase():null,T=T.toLowerCase()),Y&&D&&(Y=Dn(Y,D)||Y);const F=T!=="/"&&T.endsWith("/")?T.length-1:T.length;let P=H===T||!u&&H.startsWith(T)&&H.charAt(F)==="/",Z=Y!=null&&(Y===T||!u&&Y.startsWith(T)&&Y.charAt(T.length)==="/"),re={isActive:P,isPending:Z,isTransitioning:L},X=P?l:void 0,se;typeof o=="function"?se=o(re):se=[o,P?"active":null,Z?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let ie=typeof f=="function"?f(re):f;return x.createElement(Ov,{...g,"aria-current":X,className:se,ref:b,style:ie,to:d,viewTransition:v},typeof m=="function"?m(re):m)});xw.displayName="NavLink";var Aw=x.forwardRef(({discover:i="render",fetcherKey:l,navigate:r,reloadDocument:o,replace:u,state:f,method:d=js,action:v,onSubmit:m,relative:g,preventScrollReset:b,viewTransition:C,...S},O)=>{let w=kw(),D=Mw(v,{relative:g}),L=d.toLowerCase()==="get"?"get":"post",T=typeof v=="string"&&Cv.test(v),H=Y=>{if(m&&m(Y),Y.defaultPrevented)return;Y.preventDefault();let F=Y.nativeEvent.submitter,P=F?.getAttribute("formmethod")||d;w(F||Y.currentTarget,{fetcherKey:l,method:P,navigate:r,replace:u,state:f,relative:g,preventScrollReset:b,viewTransition:C})};return x.createElement("form",{ref:O,method:L,action:D,onSubmit:o?m:H,...S,"data-discover":!T&&i==="render"?"true":void 0})});Aw.displayName="Form";function Tw(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Rv(i){let l=x.useContext(Ua);return Le(l,Tw(i)),l}function Cw(i,{target:l,replace:r,state:o,preventScrollReset:u,relative:f,viewTransition:d}={}){let v=_v(),m=di(),g=er(i,{relative:f});return x.useCallback(b=>{if(aw(b,l)){b.preventDefault();let C=r!==void 0?r:Xl(m)===Xl(g);v(i,{replace:C,state:o,preventScrollReset:u,relative:f,viewTransition:d})}},[m,v,g,r,o,l,i,u,f,d])}var Ow=0,Rw=()=>`__${String(++Ow)}__`;function kw(){let{router:i}=Rv("useSubmit"),{basename:l}=x.useContext(en),r=XE();return x.useCallback(async(o,u={})=>{let{action:f,method:d,encType:v,formData:m,body:g}=sw(o,l);if(u.navigate===!1){let b=u.fetcherKey||Rw();await i.fetch(b,r,u.action||f,{preventScrollReset:u.preventScrollReset,formData:m,body:g,formMethod:u.method||d,formEncType:u.encType||v,flushSync:u.flushSync})}else await i.navigate(u.action||f,{preventScrollReset:u.preventScrollReset,formData:m,body:g,formMethod:u.method||d,formEncType:u.encType||v,replace:u.replace,state:u.state,fromRouteId:r,flushSync:u.flushSync,viewTransition:u.viewTransition})},[i,l,r])}function Mw(i,{relative:l}={}){let{basename:r}=x.useContext(en),o=x.useContext(tn);Le(o,"useFormAction must be used inside a RouteContext");let[u]=o.matches.slice(-1),f={...er(i||".",{relative:l})},d=di();if(i==null){f.search=d.search;let v=new URLSearchParams(f.search),m=v.getAll("index");if(m.some(b=>b==="")){v.delete("index"),m.filter(C=>C).forEach(C=>v.append("index",C));let b=v.toString();f.search=b?`?${b}`:""}}return(!i||i===".")&&u.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(f.pathname=f.pathname==="/"?r:jn([r,f.pathname])),Xl(f)}function jw(i,l={}){let r=x.useContext(yv);Le(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Rv("useViewTransitionState"),u=er(i,{relative:l.relative});if(!r.isTransitioning)return!1;let f=Dn(r.currentLocation.pathname,o)||r.currentLocation.pathname,d=Dn(r.nextLocation.pathname,o)||r.nextLocation.pathname;return qs(u.pathname,d)!=null||qs(u.pathname,f)!=null}[...gw];function kv(){const[i,l]=x.useState(!1),[r,o]=x.useState(!1),[u,f]=x.useState(navigator.onLine),[d,v]=x.useState(!1),[m,g]=x.useState(null),[b,C]=x.useState(null);x.useEffect(()=>{(()=>{const P=window.matchMedia("(display-mode: standalone)").matches,Z=window.navigator.standalone===!0;o(P||Z)})();const T=P=>{P.preventDefault(),g(P),l(!0)},H=()=>{o(!0),l(!1),g(null),console.log("[PWA] App installed successfully")},Y=()=>f(!0),F=()=>f(!1);return window.addEventListener("beforeinstallprompt",T),window.addEventListener("appinstalled",H),window.addEventListener("online",Y),window.addEventListener("offline",F),S(),()=>{window.removeEventListener("beforeinstallprompt",T),window.removeEventListener("appinstalled",H),window.removeEventListener("online",Y),window.removeEventListener("offline",F)}},[]);const S=async()=>{if("serviceWorker"in navigator)try{const L=await navigator.serviceWorker.register("/sw.js",{scope:"/"});C(L),L.addEventListener("updatefound",()=>{const T=L.installing;T&&T.addEventListener("statechange",()=>{T.state==="installed"&&navigator.serviceWorker.controller&&(v(!0),console.log("[PWA] New version available"))})}),navigator.serviceWorker.addEventListener("controllerchange",()=>{window.location.reload()}),console.log("[PWA] Service Worker registered successfully")}catch(L){console.error("[PWA] Service Worker registration failed:",L)}};return{isInstallable:i,isInstalled:r,isOnline:u,isUpdateAvailable:d,installPrompt:m,installApp:async()=>{if(!m)return console.warn("[PWA] No install prompt available"),!1;try{return await m.prompt(),(await m.userChoice).outcome==="accepted"?(console.log("[PWA] User accepted the install prompt"),l(!1),g(null),!0):(console.log("[PWA] User dismissed the install prompt"),!1)}catch(L){return console.error("[PWA] Install failed:",L),!1}},updateApp:async()=>{if(!b){console.warn("[PWA] No service worker registration available");return}try{const L=b.waiting;L&&(L.postMessage({type:"SKIP_WAITING"}),v(!1))}catch(L){console.error("[PWA] Update failed:",L)}},registerForNotifications:async()=>{if(!("Notification"in window)||!b)return console.warn("[PWA] Notifications not supported"),!1;try{if(await Notification.requestPermission()==="granted"){console.log("[PWA] Notification permission granted");const T=await b.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:Uw("YOUR_VAPID_PUBLIC_KEY")});return console.log("[PWA] Push subscription created:",T),!0}else return console.log("[PWA] Notification permission denied"),!1}catch(L){return console.error("[PWA] Notification registration failed:",L),!1}}}}function Uw(i){const l="=".repeat((4-i.length%4)%4),r=(i+l).replace(/-/g,"+").replace(/_/g,"/"),o=window.atob(r),u=new Uint8Array(o.length);for(let f=0;f<o.length;++f)u[f]=o.charCodeAt(f);return u}const Dw={isPWA:()=>{const i=window.matchMedia("(display-mode: standalone)").matches,l=window.navigator.standalone===!0;return i||l},getInstallInstructions:()=>{const i=navigator.userAgent.toLowerCase();return i.includes("chrome")&&!i.includes("edg")?'Trykk på meny-ikonet (⋮) og velg "Installer JobbLogg"':i.includes("firefox")?'Trykk på adresselinjen og velg "Installer denne siden som app"':i.includes("safari")?'Trykk på Del-knappen og velg "Legg til på hjemskjerm"':i.includes("edg")?'Trykk på meny-ikonet (⋯) og velg "Installer denne siden som app"':"Se nettleserens meny for å installere som app"},shareContent:async i=>{if(navigator.share)try{return await navigator.share(i),!0}catch(l){return console.error("[PWA] Share failed:",l),!1}else try{const l=`${i.title}
${i.text}${i.url?`
${i.url}`:""}`;return await navigator.clipboard.writeText(l),!0}catch(l){return console.error("[PWA] Clipboard fallback failed:",l),!1}}},Bs=({children:i,onClick:l,type:r="button",disabled:o=!1,className:u="",loading:f=!1,icon:d,variant:v="primary",size:m="md",fullWidth:g=!1})=>{const b=()=>{!o&&!f&&l&&l()},C=w=>{(w.key==="Enter"||w.key===" ")&&!o&&!f&&(w.preventDefault(),l&&l())},S=()=>{switch(v){case"secondary":return"btn-secondary-solid";case"outline":return"btn-outline";case"ghost":return"btn-ghost-enhanced";case"danger":return"btn-error-soft";default:return"btn-primary-solid"}},O=()=>{switch(m){case"sm":return"px-3 py-1.5 text-sm";case"lg":return"px-6 py-3 text-lg";default:return"px-4 py-2 text-base"}};return M.jsx("button",{type:r,onClick:b,onKeyDown:C,disabled:o||f,className:`
        ${S()}
        ${O()}
        ${g?"w-full":""}
        ${u}
      `.trim().replace(/\s+/g," "),"aria-disabled":o||f,children:f?M.jsxs(M.Fragment,{children:[M.jsx("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"}),M.jsx("span",{children:"Laster..."})]}):M.jsxs(M.Fragment,{children:[d&&M.jsx("span",{className:"flex-shrink-0",children:d}),M.jsx("span",{children:i})]})})},Lw=x.forwardRef(({label:i,error:l,helperText:r,required:o=!1,disabled:u=!1,size:f="medium",fullWidth:d=!1,startIcon:v,endIcon:m,className:g="",id:b,"aria-describedby":C,...S},O)=>{const w=b||`text-input-${Math.random().toString(36).substr(2,9)}`,D=l?`${w}-error`:void 0,L=r?`${w}-helper`:void 0,T=[C,D,L].filter(Boolean).join(" ")||void 0,Y=`
      input-modern
      ${l?"border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error":""}
      ${{small:"h-8 text-sm px-3",medium:"h-10 text-base px-4",large:"h-12 text-lg px-4"}[f]}
      ${d?"w-full":""}
      ${v?"pl-10":""}
      ${m?"pr-10":""}
    `.trim().replace(/\s+/g," ");return M.jsxs("div",{className:`${d?"w-full":""} ${g}`,children:[i&&M.jsxs("label",{htmlFor:w,className:`
              block text-sm font-medium mb-2
              ${l?"text-jobblogg-error":"text-jobblogg-text-strong"}
              ${u?"text-jobblogg-text-muted":""}
            `,children:[i,o&&M.jsx("span",{className:"text-jobblogg-error ml-1","aria-label":"required",children:"*"})]}),M.jsxs("div",{className:"relative",children:[v&&M.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none",children:v}),M.jsx("input",{ref:O,id:w,className:Y,disabled:u,required:o,"aria-invalid":l?"true":"false","aria-describedby":T,...S}),m&&M.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-jobblogg-text-muted pointer-events-none",children:m})]}),r&&!l&&M.jsx("p",{id:L,className:"mt-1 text-sm text-jobblogg-text-muted",children:r}),l&&M.jsxs("p",{id:D,className:"mt-1 text-sm text-jobblogg-error flex items-center gap-1",role:"alert","aria-live":"polite",children:[M.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),l]})]})});Lw.displayName="TextInput";const zw=x.forwardRef(({label:i,error:l,helperText:r,required:o=!1,disabled:u=!1,size:f="medium",fullWidth:d=!1,showCharCount:v=!1,showCharacterCount:m,maxLength:g,className:b="",id:C,value:S,"aria-describedby":O,...w},D)=>{const L=C||`textarea-${Math.random().toString(36).substr(2,9)}`,T=l?`${L}-error`:void 0,H=r?`${L}-helper`:void 0,Y=v||m,F=Y?`${L}-charcount`:void 0,P=[O,T,H,F].filter(Boolean).join(" ")||void 0,Z=typeof S=="string"?S.length:0,re=g&&Z>g*.8,X=g&&Z>g,ie=`
      textarea-modern
      ${l?"border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error":""}
      ${{small:"min-h-[80px] text-sm p-3",medium:"min-h-[100px] text-base p-4",large:"min-h-[120px] text-lg p-4"}[f]}
      ${d?"w-full":""}
    `.trim().replace(/\s+/g," ");return M.jsxs("div",{className:`${d?"w-full":""} ${b}`,children:[i&&M.jsxs("label",{htmlFor:L,className:`
              block text-sm font-medium mb-2
              ${l?"text-jobblogg-error":"text-jobblogg-text-strong"}
              ${u?"text-jobblogg-text-muted":""}
            `,children:[i,o&&M.jsx("span",{className:"text-jobblogg-error ml-1","aria-label":"required",children:"*"})]}),M.jsx("textarea",{ref:D,id:L,className:ie,disabled:u,required:o,maxLength:g,value:S,"aria-invalid":l?"true":"false","aria-describedby":P,...w}),M.jsxs("div",{className:"mt-1 flex justify-between items-start gap-2",children:[M.jsxs("div",{className:"flex-1",children:[r&&!l&&M.jsx("p",{id:H,className:"text-sm text-jobblogg-text-muted",children:r}),l&&M.jsxs("p",{id:T,className:"text-sm text-jobblogg-error flex items-center gap-1",role:"alert","aria-live":"polite",children:[M.jsx("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),l]})]}),Y&&g&&M.jsxs("p",{id:F,className:`
                text-sm flex-shrink-0 tabular-nums
                ${X?"text-jobblogg-error font-medium":re?"text-jobblogg-warning":"text-jobblogg-text-muted"}
              `,"aria-live":"polite","aria-label":`${Z} av ${g} tegn brukt`,children:[Z,"/",g]})]})]})});zw.displayName="TextArea";const Nw=({className:i=""})=>{const{isInstallable:l,isInstalled:r,installApp:o}=kv(),[u,f]=x.useState(!1),[d,v]=x.useState(!1);if(r||!l||u)return null;const m=async()=>{v(!0);try{await o()&&f(!0)}catch(b){console.error("Install failed:",b)}finally{v(!1)}},g=()=>{f(!0),localStorage.setItem("pwa-install-dismissed","true")};return M.jsxs("div",{className:`
      fixed bottom-4 left-4 right-4 z-50 
      bg-white border border-jobblogg-primary/20 rounded-xl shadow-lg 
      p-4 animate-slide-up
      md:left-auto md:right-4 md:max-w-sm
      ${i}
    `,children:[M.jsxs("div",{className:"flex items-start gap-3",children:[M.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center",children:M.jsx("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),M.jsxs("div",{className:"flex-1 min-w-0",children:[M.jsx("h3",{className:"font-semibold text-jobblogg-text-strong text-sm mb-1",children:"Installer JobbLogg"}),M.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-3 leading-relaxed",children:"Få rask tilgang til prosjektene dine direkte fra hjemskjermen. Fungerer offline og gir deg en bedre opplevelse."}),M.jsxs("div",{className:"flex items-center gap-2",children:[M.jsx(Bs,{onClick:m,loading:d,size:"sm",className:"text-xs px-3 py-1.5",children:d?"Installerer...":"Installer"}),M.jsx("button",{onClick:g,className:"text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1.5 rounded-md transition-colors",children:"Ikke nå"})]})]}),M.jsx("button",{onClick:g,className:"flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center","aria-label":"Lukk installasjonsbanner",children:M.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),M.jsx("div",{className:"mt-3 pt-3 border-t border-jobblogg-neutral",children:M.jsxs("p",{className:"text-jobblogg-text-muted text-xs",children:["💡 ",Dw.getInstallInstructions()]})})]})},qw=()=>{const{isOnline:i,isUpdateAvailable:l,updateApp:r}=kv(),[o,u]=x.useState(!1),f=async()=>{u(!0);try{await r()}catch(d){console.error("Update failed:",d)}finally{u(!1)}};return M.jsxs("div",{className:"fixed top-4 right-4 z-40 flex flex-col gap-2",children:[M.jsxs("div",{className:`
        px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 transition-all duration-300
        ${i?"bg-jobblogg-success-soft text-jobblogg-success border border-jobblogg-success/20":"bg-jobblogg-warning-soft text-jobblogg-warning border border-jobblogg-warning/20"}
      `,children:[M.jsx("div",{className:`w-2 h-2 rounded-full ${i?"bg-jobblogg-success":"bg-jobblogg-warning"}`}),i?"Online":"Offline"]}),l&&M.jsx("div",{className:"bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg p-3 max-w-xs animate-slide-down",children:M.jsxs("div",{className:"flex items-start gap-2",children:[M.jsx("div",{className:"w-5 h-5 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:M.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})})}),M.jsxs("div",{className:"flex-1 min-w-0",children:[M.jsx("p",{className:"text-jobblogg-text-strong text-sm font-medium mb-1",children:"Oppdatering tilgjengelig"}),M.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-2",children:"En ny versjon av JobbLogg er klar til installasjon."}),M.jsx(Bs,{onClick:f,loading:o,size:"sm",className:"text-xs",children:o?"Oppdaterer...":"Oppdater nå"})]})]})})]})},cp=L0,ji={OFFLINE_DATA:"jobblogg-offline-data",SYNC_QUEUE:"jobblogg-sync-queue",LAST_SYNC:"jobblogg-last-sync"};class Ca{static instance;syncQueue=[];constructor(){this.loadSyncQueue()}static getInstance(){return Ca.instance||(Ca.instance=new Ca),Ca.instance}getOfflineData(){try{const l=localStorage.getItem(ji.OFFLINE_DATA);return l?JSON.parse(l):{projects:[],projectLogs:[],lastSync:""}}catch(l){return console.error("[OfflineStorage] Error loading offline data:",l),{projects:[],projectLogs:[],lastSync:""}}}saveOfflineData(l){try{localStorage.setItem(ji.OFFLINE_DATA,JSON.stringify(l))}catch(r){console.error("[OfflineStorage] Error saving offline data:",r)}}addOfflineProject(l){const r=this.getOfflineData(),o={...l,isOffline:!0,syncStatus:"pending"};r.projects.push(o),this.saveOfflineData(r),this.addToSyncQueue("create-project",o)}addOfflineProjectLog(l){const r=this.getOfflineData(),o={...l,isOffline:!0,syncStatus:"pending"};r.projectLogs.push(o),this.saveOfflineData(r),this.addToSyncQueue("create-project-log",o)}getAllProjects(){return this.getOfflineData().projects}getProjectLogs(l){return this.getOfflineData().projectLogs.filter(o=>o.projectId===l)}addToSyncQueue(l,r){const o={type:l,data:r,timestamp:new Date().toISOString()};this.syncQueue.push(o),this.saveSyncQueue()}loadSyncQueue(){try{const l=localStorage.getItem(ji.SYNC_QUEUE);this.syncQueue=l?JSON.parse(l):[]}catch(l){console.error("[OfflineStorage] Error loading sync queue:",l),this.syncQueue=[]}}saveSyncQueue(){try{localStorage.setItem(ji.SYNC_QUEUE,JSON.stringify(this.syncQueue))}catch(l){console.error("[OfflineStorage] Error saving sync queue:",l)}}getSyncQueue(){return[...this.syncQueue]}clearSyncQueue(){this.syncQueue=[],this.saveSyncQueue()}removeFromSyncQueue(l){this.syncQueue.splice(l,1),this.saveSyncQueue()}updateSyncStatus(l,r,o){const u=this.getOfflineData();if(r==="project"){const f=u.projects.find(d=>d.id===l);f&&(f.syncStatus=o,o==="synced"&&(f.isOffline=!1))}else{const f=u.projectLogs.find(d=>d.id===l);f&&(f.syncStatus=o,o==="synced"&&(f.isOffline=!1))}this.saveOfflineData(u)}clearOfflineData(){localStorage.removeItem(ji.OFFLINE_DATA),localStorage.removeItem(ji.SYNC_QUEUE),localStorage.removeItem(ji.LAST_SYNC),this.syncQueue=[]}getStorageUsage(){try{"storage"in navigator&&"estimate"in navigator.storage&&navigator.storage.estimate().then(u=>{const f=u.usage||0,d=u.quota||0,v=d>0?f/d*100:0;return console.log("[OfflineStorage] Storage usage:",{used:Math.round(f/1024/1024*100)/100+" MB",available:Math.round(d/1024/1024*100)/100+" MB",percentage:Math.round(v*100)/100+"%"}),{used:f,available:d,percentage:v}});let l=0;for(const u in localStorage)localStorage.hasOwnProperty(u)&&(l+=localStorage[u].length);const r=5*1024*1024,o=l/r*100;return{used:l,available:r,percentage:o}}catch(l){return console.error("[OfflineStorage] Error calculating storage usage:",l),{used:0,available:0,percentage:0}}}isStorageNearlyFull(){return this.getStorageUsage().percentage>80}}const fn=Ca.getInstance(),Bw=({className:i=""})=>{const[l,r]=x.useState(!1),[o,u]=x.useState(0),[f,d]=x.useState("idle"),[v,m]=x.useState(0),[g,b]=x.useState(navigator.onLine),C=Rg(cp.projects.create),S=Rg(cp.logEntries.create);x.useEffect(()=>{const w=()=>b(!0),D=()=>b(!1);return window.addEventListener("online",w),window.addEventListener("offline",D),()=>{window.removeEventListener("online",w),window.removeEventListener("offline",D)}},[]),x.useEffect(()=>{const w=()=>{const L=fn.getSyncQueue();m(L.length)};w();const D=setInterval(w,5e3);return()=>clearInterval(D)},[f]),x.useEffect(()=>{if(g&&v>0&&f==="idle"){const w=setTimeout(()=>{O()},2e3);return()=>clearTimeout(w)}},[g,v,f]);const O=async()=>{if(!(!g||l)){r(!0),d("syncing"),u(0);try{const w=fn.getSyncQueue();if(w.length===0){d("success"),r(!1);return}let D=0;const L=w.length;for(let T=0;T<w.length;T++){const H=w[T];try{H.type==="create-project"?(fn.updateSyncStatus(H.data.id,"project","syncing"),await C({name:H.data.title,description:H.data.description,userId:H.data.userId||"offline-user"}),fn.updateSyncStatus(H.data.id,"project","synced")):H.type==="create-project-log"&&(fn.updateSyncStatus(H.data.id,"projectLog","syncing"),await S({projectId:H.data.projectId,userId:H.data.userId||"offline-user",description:H.data.description,imageId:H.data.imageId}),fn.updateSyncStatus(H.data.id,"projectLog","synced")),fn.removeFromSyncQueue(0),D++,u(D/L*100)}catch(Y){console.error("[OfflineSync] Error syncing item:",Y),H.type==="create-project"?fn.updateSyncStatus(H.data.id,"project","error"):H.type==="create-project-log"&&fn.updateSyncStatus(H.data.id,"projectLog","error"),D++,u(D/L*100)}}d("success"),setTimeout(()=>{d("idle")},3e3)}catch(w){console.error("[OfflineSync] Sync failed:",w),d("error"),setTimeout(()=>{d("idle")},5e3)}finally{r(!1),u(0)}}};return v===0&&f==="idle"?null:M.jsxs("div",{className:`
      fixed bottom-20 left-4 right-4 z-40
      bg-white border border-jobblogg-border rounded-xl shadow-lg p-4
      md:left-auto md:right-4 md:max-w-sm
      ${i}
    `,children:[M.jsxs("div",{className:"flex items-start gap-3",children:[M.jsx("div",{className:`
          flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
          ${f==="syncing"?"bg-jobblogg-primary-soft":f==="success"?"bg-jobblogg-success-soft":f==="error"?"bg-jobblogg-error-soft":"bg-jobblogg-warning-soft"}
        `,children:f==="syncing"?M.jsx("div",{className:"w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"}):f==="success"?M.jsx("svg",{className:"w-5 h-5 text-jobblogg-success",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):f==="error"?M.jsx("svg",{className:"w-5 h-5 text-jobblogg-error",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}):M.jsx("svg",{className:"w-5 h-5 text-jobblogg-warning",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),M.jsxs("div",{className:"flex-1 min-w-0",children:[M.jsx("h3",{className:"font-semibold text-jobblogg-text-strong text-sm mb-1",children:f==="syncing"?"Synkroniserer data...":f==="success"?"Synkronisering fullført":f==="error"?"Synkroniseringsfeil":`${v} element${v!==1?"er":""} venter på synkronisering`}),M.jsx("p",{className:"text-jobblogg-text-medium text-xs mb-3",children:f==="syncing"?`${Math.round(o)}% fullført`:f==="success"?"Alle endringer er synkronisert med serveren":f==="error"?"Noen elementer kunne ikke synkroniseres":g?"Trykk for å synkronisere nå":"Venter på internettforbindelse"}),f==="syncing"&&M.jsx("div",{className:"w-full bg-jobblogg-neutral rounded-full h-2 mb-3",children:M.jsx("div",{className:"bg-jobblogg-primary h-2 rounded-full transition-all duration-300",style:{width:`${o}%`}})}),f==="idle"&&g&&v>0&&M.jsx(Bs,{onClick:O,size:"sm",className:"text-xs",children:"Synkroniser nå"}),f==="error"&&M.jsxs("div",{className:"flex gap-2",children:[M.jsx(Bs,{onClick:O,size:"sm",className:"text-xs",children:"Prøv igjen"}),M.jsx("button",{onClick:()=>d("idle"),className:"text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1 rounded transition-colors",children:"Lukk"})]})]}),f!=="syncing"&&M.jsx("button",{onClick:()=>d("idle"),className:"flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center","aria-label":"Lukk synkroniseringspanel",children:M.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),M.jsx("div",{className:"mt-3 pt-3 border-t border-jobblogg-neutral",children:M.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[M.jsx("div",{className:`w-2 h-2 rounded-full ${g?"bg-jobblogg-success":"bg-jobblogg-error"}`}),M.jsx("span",{className:"text-jobblogg-text-muted",children:g?"Online":"Offline - endringer lagres lokalt"})]})})]})},Hw=()=>{const[i,l]=x.useState({used:0,available:0,percentage:0}),[r,o]=x.useState(!1);return x.useEffect(()=>{const u=()=>{const d=fn.getStorageUsage();l(d)};u();const f=setInterval(u,3e4);return()=>clearInterval(f)},[]),i.percentage<50?null:M.jsx("div",{className:"fixed top-4 left-4 z-40 bg-white border border-jobblogg-warning/20 rounded-lg p-3 max-w-xs shadow-lg",children:M.jsxs("div",{className:"flex items-start gap-2",children:[M.jsx("svg",{className:"w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:M.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})}),M.jsxs("div",{className:"flex-1 min-w-0",children:[M.jsxs("p",{className:"text-sm font-medium text-jobblogg-text-strong",children:["Lagringsplass ",i.percentage>80?"nesten full":"fylles opp"]}),M.jsxs("p",{className:"text-xs text-jobblogg-text-medium mb-2",children:[Math.round(i.percentage),"% av tilgjengelig plass brukt"]}),M.jsx("div",{className:"w-full bg-jobblogg-neutral rounded-full h-2 mb-2",children:M.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${i.percentage>90?"bg-jobblogg-error":i.percentage>80?"bg-jobblogg-warning":"bg-jobblogg-primary"}`,style:{width:`${Math.min(i.percentage,100)}%`}})}),M.jsx("button",{onClick:()=>o(!r),className:"text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors",children:r?"Skjul detaljer":"Vis detaljer"}),r&&M.jsxs("div",{className:"mt-2 text-xs text-jobblogg-text-muted",children:[M.jsxs("p",{children:["Brukt: ",Math.round(i.used/1024)," KB"]}),M.jsxs("p",{children:["Tilgjengelig: ",Math.round(i.available/1024)," KB"]})]})]})]})})},Vw="modulepreload",Qw=function(i){return"/"+i},fp={},fi=function(l,r,o){let u=Promise.resolve();if(r&&r.length>0){let m=function(g){return Promise.all(g.map(b=>Promise.resolve(b).then(C=>({status:"fulfilled",value:C}),C=>({status:"rejected",reason:C}))))};document.getElementsByTagName("link");const d=document.querySelector("meta[property=csp-nonce]"),v=d?.nonce||d?.getAttribute("nonce");u=m(r.map(g=>{if(g=Qw(g),g in fp)return;fp[g]=!0;const b=g.endsWith(".css"),C=b?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${C}`))return;const S=document.createElement("link");if(S.rel=b?"stylesheet":Vw,b||(S.as="script"),S.crossOrigin="",S.href=g,v&&S.setAttribute("nonce",v),document.head.appendChild(S),b)return new Promise((O,w)=>{S.addEventListener("load",O),S.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${g}`)))})}))}function f(d){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=d,window.dispatchEvent(v),!v.defaultPrevented)throw d}return u.then(d=>{for(const v of d||[])v.status==="rejected"&&f(v.reason);return l().catch(f)})};function La(i){return x.lazy(i)}function dp(i){i().catch(l=>{console.warn("[LazyLoading] Preload failed:",l)})}class Pw{observer=null;images=new Set;constructor(){"IntersectionObserver"in window&&(this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:"50px 0px",threshold:.01}))}handleIntersection(l){l.forEach(r=>{if(r.isIntersecting){const o=r.target;this.loadImage(o),this.observer?.unobserve(o),this.images.delete(o)}})}loadImage(l){const r=l.dataset.src,o=l.dataset.srcset;r&&(l.src=r),o&&(l.srcset=o),l.classList.remove("lazy"),l.classList.add("lazy-loaded")}observe(l){this.observer?(this.images.add(l),this.observer.observe(l)):this.loadImage(l)}unobserve(l){this.observer&&(this.observer.unobserve(l),this.images.delete(l))}disconnect(){this.observer&&(this.observer.disconnect(),this.images.clear())}}new Pw;class Yw{observer=null;elements=new Map;constructor(){"IntersectionObserver"in window&&(this.observer=new IntersectionObserver(this.handleIntersection.bind(this),{rootMargin:"100px 0px",threshold:.1}))}handleIntersection(l){l.forEach(r=>{if(r.isIntersecting){const o=this.elements.get(r.target);o&&(o(),this.observer?.unobserve(r.target),this.elements.delete(r.target))}})}observe(l,r){this.observer?(this.elements.set(l,r),this.observer.observe(l)):r()}unobserve(l){this.observer&&(this.observer.unobserve(l),this.elements.delete(l))}disconnect(){this.observer&&(this.observer.disconnect(),this.elements.clear())}}new Yw;const Iw={measureRender:(i,l)=>{const r=performance.now();l();const o=performance.now();console.log(`[Performance] ${i} render time: ${o-r}ms`)},measureImageLoad:i=>new Promise(l=>{const r=performance.now(),o=new Image;o.onload=()=>{const u=performance.now()-r;console.log(`[Performance] Image load time (${i}): ${u}ms`),l(u)},o.onerror=()=>{const u=performance.now()-r;console.warn(`[Performance] Image load failed (${i}): ${u}ms`),l(u)},o.src=i}),monitorWebVitals:()=>{new PerformanceObserver(l=>{const r=l.getEntries(),o=r[r.length-1];console.log("[Performance] LCP:",o.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(l=>{l.getEntries().forEach(o=>{console.log("[Performance] FID:",o.processingStart-o.startTime)})}).observe({entryTypes:["first-input"]});let i=0;new PerformanceObserver(l=>{l.getEntries().forEach(o=>{o.hadRecentInput||(i+=o.value,console.log("[Performance] CLS:",i))})}).observe({entryTypes:["layout-shift"]})}},$w=()=>M.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:M.jsxs("div",{className:"text-center",children:[M.jsx("div",{className:"w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"}),M.jsx("p",{className:"text-jobblogg-text-medium",children:"Laster inn..."})]})}),Gw=La(()=>fi(()=>import("./Dashboard-BUFpwwdV.js"),__vite__mapDeps([0,1,2]))),Xw=La(()=>fi(()=>import("./CreateProject-BhjuMJEe.js"),__vite__mapDeps([3,1,4,5,6]))),Kw=La(()=>fi(()=>import("./ProjectDetail-BSOuW_cT.js"),__vite__mapDeps([7,1,4,5,2]))),Ww=La(()=>fi(()=>import("./ProjectLog-D4PKsjCb.js"),__vite__mapDeps([8,5,6]))),Zw=La(()=>fi(()=>import("./SignIn-DgwY8h9P.js"),[])),Fw=La(()=>fi(()=>import("./SignUp-B9HLQpsb.js"),[])),wa=({children:i,fallback:l=$w})=>M.jsx(x.Suspense,{fallback:M.jsx(l,{}),children:i}),Jw=()=>{dp(()=>fi(()=>import("./Dashboard-BUFpwwdV.js"),__vite__mapDeps([0,1,2]))),dp(()=>fi(()=>import("./CreateProject-BhjuMJEe.js"),__vite__mapDeps([3,1,4,5,6])))};function ex(){return x.useEffect(()=>{Jw()},[]),M.jsx(ww,{children:M.jsxs("div",{className:"min-h-screen bg-white transition-colors duration-300",children:[M.jsx(qw,{}),M.jsx(Nw,{}),M.jsx(Bw,{}),M.jsx(Hw,{}),M.jsxs(JE,{children:[M.jsx(Di,{path:"/sign-in",element:M.jsx(wa,{children:M.jsx(Zw,{})})}),M.jsx(Di,{path:"/sign-up",element:M.jsx(wa,{children:M.jsx(Fw,{})})}),M.jsx(Di,{path:"/",element:M.jsxs(M.Fragment,{children:[M.jsx(ws,{children:M.jsx(wa,{children:M.jsx(Gw,{})})}),M.jsx(xs,{children:M.jsx(Os,{to:"/sign-in",replace:!0})})]})}),M.jsx(Di,{path:"/create",element:M.jsxs(M.Fragment,{children:[M.jsx(ws,{children:M.jsx(wa,{children:M.jsx(Xw,{})})}),M.jsx(xs,{children:M.jsx(Os,{to:"/sign-in",replace:!0})})]})}),M.jsx(Di,{path:"/project/:projectId/details",element:M.jsxs(M.Fragment,{children:[M.jsx(ws,{children:M.jsx(wa,{children:M.jsx(Kw,{})})}),M.jsx(xs,{children:M.jsx(Os,{to:"/sign-in",replace:!0})})]})}),M.jsx(Di,{path:"/project/:projectId",element:M.jsxs(M.Fragment,{children:[M.jsx(ws,{children:M.jsx(wa,{children:M.jsx(Ww,{})})}),M.jsx(xs,{children:M.jsx(Os,{to:"/sign-in",replace:!0})})]})})]})]})})}const ke={background:"#ffffff",backgroundSecondary:"#F8FAFC",text:"#1F2937",textSecondary:"#4B5563",textMuted:"#6B7280",border:"#E5E7EB",shimmer:"#F8FAFC",inputBackground:"#ffffff",cardBackground:"#F8FAFC"},tx={variables:{colorPrimary:"#1D4ED8",colorPrimaryFocus:"#1E40AF",colorSuccess:"#10B981",colorWarning:"#FBBF24",colorDanger:"#DC2626",fontFamily:"Inter, system-ui, sans-serif",fontSize:"16px",fontWeight:{normal:"400",medium:"500",semibold:"600",bold:"700"},borderRadius:"0.75rem",spacingUnit:"1rem",colorBackground:ke.background,colorInputBackground:ke.inputBackground,colorInputText:ke.text,colorText:ke.text,colorTextSecondary:ke.textSecondary,colorTextOnPrimaryBackground:"#ffffff",colorBorder:ke.border,colorShimmer:ke.shimmer},elements:{card:{backgroundColor:ke.cardBackground,borderRadius:"1rem",boxShadow:"0 25px 50px -12px rgb(0 0 0 / 0.25)",border:`1px solid ${ke.border}`,padding:"2rem",maxWidth:"28rem",width:"100%"},headerTitle:{fontSize:"1.875rem",fontWeight:"700",color:ke.text,textAlign:"center",marginBottom:"0.5rem"},headerSubtitle:{fontSize:"1rem",color:ke.textSecondary,textAlign:"center",marginBottom:"2rem"},formButtonPrimary:{backgroundColor:"#1D4ED8",color:"#ffffff",borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"600",border:"none",cursor:"pointer",transition:"all 0.2s ease",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.25)",width:"100%",height:"3rem","&:hover":{backgroundColor:"#1E40AF",transform:"scale(1.02)",boxShadow:"0 8px 25px 0 rgb(29 78 216 / 0.35)"},"&:focus":{outline:"2px solid #1D4ED8",outlineOffset:"2px",transform:"scale(1.02)"},"&:active":{transform:"scale(0.98)"}},formButtonSecondary:{backgroundColor:"transparent",color:ke.text,border:`2px solid ${ke.border}`,borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",width:"100%",height:"3rem","&:hover":{borderColor:"#1D4ED8",color:"#1D4ED8",transform:"scale(1.02)",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.15)"}},socialButtonsBlockButton:{backgroundColor:ke.background,color:ke.text,border:`2px solid ${ke.border}`,borderRadius:"0.75rem",padding:"0.75rem 1.5rem",fontSize:"1rem",fontWeight:"500",cursor:"pointer",transition:"all 0.2s ease",width:"100%",height:"3rem",display:"flex",alignItems:"center",justifyContent:"center",gap:"0.75rem",marginBottom:"0.75rem","&:hover":{borderColor:"#1D4ED8",transform:"scale(1.02)",boxShadow:"0 4px 14px 0 rgb(29 78 216 / 0.15)"},"&:focus":{outline:"2px solid #1D4ED8",outlineOffset:"2px"}},formFieldInput:{backgroundColor:ke.inputBackground,color:ke.text,border:`2px solid ${ke.border}`,borderRadius:"0.75rem",padding:"0.75rem 1rem",fontSize:"1rem",width:"100%",height:"3rem",transition:"all 0.2s ease","&:focus":{borderColor:"#1D4ED8",outline:"none",boxShadow:"0 0 0 3px rgb(29 78 216 / 0.1)",backgroundColor:ke.inputBackground},"&::placeholder":{color:ke.textMuted}},formFieldLabel:{fontSize:"0.875rem",fontWeight:"600",color:ke.text,marginBottom:"0.5rem",display:"block"},formFieldAction:{color:"#1D4ED8",fontSize:"0.875rem",fontWeight:"500",textDecoration:"none",transition:"color 0.2s ease","&:hover":{color:"#1E40AF",textDecoration:"underline"}},footerActionLink:{color:"#1D4ED8",fontSize:"0.875rem",fontWeight:"500",textDecoration:"none",transition:"color 0.2s ease","&:hover":{color:"#1E40AF",textDecoration:"underline"}},dividerLine:{backgroundColor:ke.border,height:"1px",margin:"1.5rem 0"},dividerText:{color:ke.textSecondary,fontSize:"0.875rem",fontWeight:"500"},spinner:{color:"#1D4ED8",width:"1.5rem",height:"1.5rem"},formFieldErrorText:{color:"#DC2626",fontSize:"0.875rem",fontWeight:"500",marginTop:"0.5rem"},formFieldSuccessText:{color:"#10B981",fontSize:"0.875rem",fontWeight:"500",marginTop:"0.5rem"},alert:{backgroundColor:ke.backgroundSecondary,border:`1px solid ${ke.border}`,borderRadius:"0.75rem",padding:"1rem",marginBottom:"1rem"},alertText:{color:ke.text,fontSize:"0.875rem",lineHeight:"1.5"}}},nx={locale:"nb-NO",signIn:{start:{title:"Velkommen tilbake! 👋",subtitle:"Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen",actionText:"Har du ikke konto ennå?",actionLink:"Opprett konto"},emailCode:{title:"Sjekk e-posten din",subtitle:"Vi har sendt en kode til {{identifier}}",formTitle:"Bekreftelseskode",formSubtitle:"Skriv inn koden du mottok på e-post",resendButton:"Send kode på nytt"},emailLink:{title:"Sjekk e-posten din",subtitle:"Vi har sendt en innloggingslenke til {{identifier}}",formTitle:"Innloggingslenke",formSubtitle:"Klikk på lenken i e-posten for å logge inn",resendButton:"Send lenke på nytt"},forgotPasswordAlternativeMethods:{title:"Glemt passordet?",label:"Ingen problem! Vi hjelper deg å komme inn igjen.",blockButton__emailCode:"Send kode til e-post",blockButton__emailLink:"Send innloggingslenke til e-post"},alternativeMethods:{title:"Andre innloggingsmåter",actionLink:"Få hjelp",blockButton__emailCode:"Bruk e-postkode",blockButton__emailLink:"Bruk e-postlenke",blockButton__password:"Bruk passord"}},signUp:{start:{title:"Opprett din JobbLogg-konto 🚀",subtitle:"Begynn å dokumentere arbeidet ditt profesjonelt",actionText:"Har du allerede en konto?",actionLink:"Logg inn"},emailCode:{title:"Bekreft e-postadressen din",subtitle:"Vi har sendt en bekreftelseskode til {{identifier}}",formTitle:"Bekreftelseskode",formSubtitle:"Skriv inn koden du mottok på e-post",resendButton:"Send kode på nytt"},emailLink:{title:"Bekreft e-postadressen din",subtitle:"Vi har sendt en bekreftelseslenke til {{identifier}}",formTitle:"Bekreftelseslenke",formSubtitle:"Klikk på lenken i e-posten for å bekrefte kontoen",resendButton:"Send lenke på nytt"},continue:{title:"Fullfør registreringen",subtitle:"Fyll ut de siste detaljene for å komme i gang",actionText:"Har du allerede en konto?",actionLink:"Logg inn"}},formFieldLabel__emailAddress:"E-postadresse",formFieldLabel__emailAddress_username:"E-postadresse eller brukernavn",formFieldLabel__username:"Brukernavn",formFieldLabel__firstName:"Fornavn",formFieldLabel__lastName:"Etternavn",formFieldLabel__password:"Passord",formFieldLabel__newPassword:"Nytt passord",formFieldLabel__confirmPassword:"Bekreft passord",formFieldLabel__currentPassword:"Nåværende passord",formFieldLabel__signOutOfOtherSessions:"Logg ut av andre enheter",formFieldInputPlaceholder__emailAddress:"Skriv inn e-postadressen din",formFieldInputPlaceholder__emailAddress_username:"E-postadresse eller brukernavn",formFieldInputPlaceholder__username:"Skriv inn brukernavn",formFieldInputPlaceholder__firstName:"Skriv inn fornavn",formFieldInputPlaceholder__lastName:"Skriv inn etternavn",formFieldInputPlaceholder__password:"Skriv inn passord",formFieldInputPlaceholder__newPassword:"Skriv inn nytt passord",formFieldInputPlaceholder__confirmPassword:"Bekreft passordet",formFieldInputPlaceholder__currentPassword:"Skriv inn nåværende passord",formButtonPrimary:"Fortsett",formButtonPrimary__signIn:"Logg inn",formButtonPrimary__signUp:"Opprett konto",formButtonPrimary__continue:"Fortsett",formButtonPrimary__finish:"Fullfør",socialButtonsBlockButton:"Fortsett med {{provider|titleize}}",dividerText:"eller",footerActionLink__useAnotherMethod:"Bruk en annen metode",footerActionLink__signUp:"Opprett konto",footerActionLink__signIn:"Logg inn",formFieldError__notProvided:"Dette feltet er påkrevd",formFieldError__emailAddress_invalid:"Ugyldig e-postadresse",formFieldError__password_pwned:"Dette passordet er kompromittert. Velg et annet passord.",formFieldError__password_too_short:"Passordet må være minst {{length}} tegn",formFieldError__password_weak:"Passordet er for svakt. Bruk en kombinasjon av bokstaver, tall og symboler.",formFieldError__firstName_invalid:"Ugyldig fornavn",formFieldError__lastName_invalid:"Ugyldig etternavn",formFieldError__username_invalid:"Ugyldig brukernavn",formFieldError__username_taken:"Dette brukernavnet er allerede tatt",formFieldError__emailAddress_taken:"Denne e-postadressen er allerede registrert",formFieldSuccess__signUp:"Kontoen din er opprettet!",formFieldSuccess__signIn:"Du er nå logget inn!",formFieldAction__loading:"Laster...",formFieldHintText__optional:"(valgfritt)",formFieldHintText__slug:"Kun bokstaver, tall og bindestreker",verificationLinkText:"Bekreftelseslenke",verificationCodeText:"Bekreftelseskode",resendButton:"Send på nytt",backButton:"Tilbake",modalCloseButton:"Lukk",breadcrumbsItem1:"Logg inn",breadcrumbsItem2:"Velg konto",breadcrumbsItem3:"Bekreft",alertText:"Hvis du fortsetter, godtar du våre vilkår og betingelser.",badge__primary:"Primær",badge__thisDevice:"Denne enheten",badge__userDevice:"Brukerenhet",badge__otherImpersonatorDevice:"Annen enhet",dates:{previous6Days:"Siste {{count}} dager",lastDay:"I går",sameDay:"I dag",nextDay:"I morgen",next6Days:"Neste {{count}} dager",numeric:"{{date}}"}},ix=new SS("https://enchanted-quail-174.convex.cloud"),ax="pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk";"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(i=>{console.log("[PWA] Service Worker registered successfully:",i.scope)}).catch(i=>{console.log("[PWA] Service Worker registration failed:",i)})});Iw.monitorWebVitals();n0.createRoot(document.getElementById("root")).render(M.jsx(x.StrictMode,{children:M.jsx(hv,{publishableKey:ax,appearance:tx,localization:nx,children:M.jsx(ES,{client:ix,children:M.jsx(ex,{})})})}));export{Ov as L,Bs as P,cx as S,Lw as T,dx as U,_v as a,lx as b,cp as c,Rg as d,zw as e,hx as f,fx as g,M as j,x as r,ux as u};
