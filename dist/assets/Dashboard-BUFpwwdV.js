import{j as e,u as h,a as j,b as p,c as u,U as b,L as v}from"./index-CKtwqk6x.js";import{H as k}from"./Heading2-D3ePk88U.js";import{E as f}from"./EmptyState-C2i22Zrb.js";const N=({title:o,description:n,projectId:i,updatedAt:a,onClick:s,className:t="",animationDelay:l="0s"})=>{const d=()=>{s()},x=r=>{(r.key==="Enter"||r.key===" ")&&(r.preventDefault(),s())},c=r=>{try{return new Date(r).toLocaleDateString("nb-NO")}catch{return r}};return e.jsxs("div",{className:`
        card-elevated hover-lift animate-slide-up group cursor-pointer
        ${t}
      `.trim().replace(/\s+/g," "),style:{animationDelay:l},onClick:d,onKeyDown:x,tabIndex:0,role:"button","aria-label":`Åpne prosjekt: ${o}`,children:[e.jsx("figure",{className:"px-6 pt-6",children:e.jsx("div",{className:"w-full h-48 gradient-blue-soft rounded-xl flex items-center justify-center group-hover:gradient-card-hover transition-all duration-300",children:e.jsx("div",{className:"p-4 bg-white/90 backdrop-blur-sm rounded-2xl shadow-soft group-hover:shadow-medium transition-all duration-300",children:e.jsx("svg",{className:"w-12 h-12 text-jobblogg-primary group-hover:text-jobblogg-primary-light transition-colors duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),e.jsxs("div",{className:"card-body p-6",children:[e.jsx("h2",{className:"card-title text-jobblogg-text-strong font-semibold text-xl group-hover:text-jobblogg-primary transition-colors duration-200",children:o}),e.jsx("p",{className:"text-jobblogg-text-medium text-sm leading-relaxed line-clamp-2",children:n||"Ingen beskrivelse tilgjengelig"}),e.jsxs("div",{className:"flex items-center gap-2 mt-4",children:[e.jsx("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary-soft text-jobblogg-primary",children:"Nytt"}),e.jsxs("span",{className:"text-jobblogg-text-muted text-xs",children:["Opprettet ",c(a)]})]}),e.jsxs("div",{className:"card-actions justify-end mt-6 gap-3",children:[e.jsxs("button",{className:"btn-outline text-sm flex items-center gap-2",onClick:r=>{r.stopPropagation()},"aria-label":`Se detaljer for ${o}`,children:[e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Se detaljer"]}),e.jsxs("button",{className:"btn-modern text-sm bg-jobblogg-primary text-white flex items-center gap-2",onClick:r=>{r.stopPropagation(),s()},"aria-label":`Åpne prosjektlogg for ${o}`,children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),"Åpne logg"]})]})]})]})},w=({children:o,title:n,subtitle:i,headerActions:a,statsSection:s,className:t="",noAnimation:l=!1})=>{const d=l?"":"animate-fade-in";return e.jsx("div",{className:`min-h-screen bg-white ${d} ${t}`.trim(),children:e.jsxs("div",{className:"container-full section-spacing",children:[(n||i||a)&&e.jsx("header",{className:"page-header",children:e.jsxs("div",{className:"flex-between gap-6 mb-8",children:[e.jsx("div",{className:"flex items-center gap-6",children:(n||i)&&e.jsxs("div",{className:l?"":"animate-slide-up",children:[n&&e.jsx("h1",{className:"text-heading-1 gradient-header bg-clip-text text-transparent mb-2",children:n}),i&&e.jsx("p",{className:"text-lg text-jobblogg-text-medium",children:i})]})}),a&&e.jsx("div",{className:"flex items-center gap-4",children:a})]})}),s&&e.jsx("section",{className:"space-section",children:s}),e.jsx("main",{className:`page-content ${l?"":"animate-slide-up"}`,style:l?{}:{animationDelay:"0.2s"},children:o})]})})},g=({title:o,value:n,subtitle:i,icon:a,variant:s="neutral",className:t="",animationDelay:l="0s",onClick:d})=>{const c=(()=>{switch(s){case"primary":return{iconBg:"bg-jobblogg-primary-soft",iconColor:"text-jobblogg-primary",valueColor:"text-jobblogg-primary"};case"accent":return{iconBg:"bg-jobblogg-accent-soft",iconColor:"text-jobblogg-accent",valueColor:"text-jobblogg-accent"};case"warning":return{iconBg:"bg-jobblogg-warning-soft",iconColor:"text-jobblogg-warning",valueColor:"text-jobblogg-warning"};default:return{iconBg:"bg-jobblogg-neutral",iconColor:"text-jobblogg-text-medium",valueColor:"text-jobblogg-text-strong"}}})(),r=!!d;return e.jsx("div",{className:`
        card-elevated animate-scale-in
        ${r?"cursor-pointer hover-lift":""}
        ${t}
      `.trim().replace(/\s+/g," "),style:{animationDelay:l},onClick:d,role:r?"button":void 0,tabIndex:r?0:void 0,onKeyDown:r?m=>{(m.key==="Enter"||m.key===" ")&&(m.preventDefault(),d?.())}:void 0,children:e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsx("div",{className:`p-3 ${c.iconBg} rounded-xl flex-shrink-0`,children:e.jsx("div",{className:`w-6 h-6 ${c.iconColor}`,children:a})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:`text-2xl font-bold ${c.valueColor} mb-1`,children:n}),e.jsx("p",{className:"text-sm text-jobblogg-text-strong font-medium mb-1",children:o}),i&&e.jsx("p",{className:"text-xs text-jobblogg-text-muted",children:i})]})]})})},D=()=>{const{user:o}=h(),n=j(),i=p(u.projects.getByUser,{userId:o?.id||""});if(i===void 0)return e.jsx("div",{className:"min-h-screen bg-jobblogg-neutral",children:e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12",children:[e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsx("div",{className:"skeleton h-12 w-64"}),e.jsx("div",{className:"skeleton h-10 w-10 rounded-full"})]}),e.jsx("div",{className:"skeleton h-12 w-40 rounded-xl"})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[...Array(4)].map((s,t)=>e.jsxs("div",{className:"bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border",children:[e.jsx("div",{className:"skeleton h-4 w-20 mb-3"}),e.jsx("div",{className:"skeleton h-8 w-16 mb-2"}),e.jsx("div",{className:"skeleton h-3 w-24"})]},t))}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[...Array(6)].map((s,t)=>e.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border",children:[e.jsx("div",{className:"skeleton h-48 w-full rounded-lg mb-4"}),e.jsx("div",{className:"skeleton h-6 w-3/4 mb-2"}),e.jsx("div",{className:"skeleton h-4 w-full mb-4"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"skeleton h-8 w-20"}),e.jsx("div",{className:"skeleton h-8 w-24"})]})]},t))})]})});const a=i.sort((s,t)=>t.createdAt-s.createdAt);return e.jsx(w,{title:"Dine prosjekter",subtitle:`Velkommen tilbake, ${o?.firstName||"Bruker"}! 👋`,headerActions:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{afterSignOutUrl:"/",appearance:{elements:{avatarBox:"w-10 h-10 rounded-full ring-2 ring-jobblogg-primary/20 hover:ring-jobblogg-primary/40 transition-all duration-200"}}}),e.jsxs(v,{to:"/create",className:"btn btn-primary btn-lg btn-modern shadow-lg hover:shadow-xl group",children:[e.jsx("svg",{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Nytt prosjekt"]})]}),statsSection:e.jsxs("div",{className:"grid-stats",children:[e.jsx(g,{title:"Totale prosjekter",value:a.length,variant:"primary",icon:e.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),e.jsx(g,{title:"Denne måneden",value:a.filter(s=>{const t=new Date(s.createdAt),l=new Date;return t.getMonth()===l.getMonth()&&t.getFullYear()===l.getFullYear()}).length,variant:"accent",animationDelay:"0.1s",icon:e.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),e.jsx(g,{title:"Siste prosjekt",value:a.length>0?new Date(a[0].createdAt).toLocaleDateString("nb-NO",{day:"numeric",month:"short"}):"-",variant:"primary",animationDelay:"0.2s",icon:e.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx(g,{title:"Totalt bilder",value:0,subtitle:"Kommer snart",variant:"warning",animationDelay:"0.3s",icon:e.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})]}),children:e.jsxs("section",{className:"space-section",children:[e.jsxs(k,{className:"mb-6 flex items-center gap-3",children:[e.jsx("svg",{className:"w-6 h-6 text-jobblogg-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),"Prosjektoversikt"]}),e.jsxs("div",{className:"grid-responsive",children:[a.map((s,t)=>e.jsx(N,{title:s.name,description:s.description||"Ingen beskrivelse tilgjengelig",projectId:s._id,updatedAt:new Date(s.createdAt).toLocaleDateString("nb-NO"),onClick:()=>n(`/project/${s._id}`),animationDelay:`${t*.1}s`},s._id)),a.length===0&&e.jsx("div",{className:"col-span-full",children:e.jsx(f,{title:"🚀 Kom i gang med ditt første prosjekt!",description:"JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang.",actionLabel:"Opprett ditt første prosjekt",onAction:()=>n("/create")})})]})]})})};export{D as default};
