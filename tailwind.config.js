/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'jobblogg': {
          'primary': '#1D4ED8',      // Modern blue for CTAs and active states
          'primary-light': '#3B82F6', // Lighter blue for hover states
          'primary-soft': '#DBEAFE',  // Soft blue for backgrounds
          'neutral': '#F8FAFC',      // Cooler light gray for card backgrounds
          'neutral-secondary': '#E5E7EB', // Secondary background with better contrast
          'neutral-light': '#F3F4F6',    // Even lighter neutral for subtle backgrounds
          'accent': '#10B981',       // Green for success states
          'accent-light': '#34D399', // Lighter green for hover states
          'accent-soft': '#D1FAE5',  // Soft green for backgrounds
          'warning': '#FBBF24',      // Lighter amber for improved contrast
          'warning-light': '#FCD34D', // Lighter amber for hover states
          'warning-soft': '#FEF3C7', // Soft amber for backgrounds
          'error': '#DC2626',        // Deeper red for enhanced clarity
          'error-light': '#EF4444',  // Lighter red for hover states
          'error-soft': '#FEE2E2',   // Soft red for backgrounds
          'text-strong': '#111827',  // Very dark gray for headings and important text (WCAG AA: 16.8:1 contrast)
          'text-medium': '#4B5563',  // Medium gray for body text (WCAG AA: 7.3:1 contrast)
          'text-muted': '#6B7280',   // Muted gray for secondary text (WCAG AA: 4.9:1 contrast)
          // Additional harmonious color variants
          'blue-50': '#EFF6FF',      // Very light blue for subtle backgrounds
          'blue-100': '#DBEAFE',     // Light blue for card backgrounds
          'indigo-50': '#EEF2FF',    // Very light indigo for gradient backgrounds
          'indigo-100': '#E0E7FF',   // Light indigo for hover states
          'slate-50': '#F8FAFC',     // Neutral light gray
          'slate-100': '#F1F5F9',    // Slightly darker neutral
        }
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('daisyui'),
  ],
  daisyui: {
    themes: false, // Disable all themes to use only custom colors
    base: true,
    styled: true,
    utils: true,
  },
}
