{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/lazycomponents.tsx", "../../src/components/offlinesync.tsx", "../../src/components/pwainstallbanner.tsx", "../../src/components/ui/componentdemo.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/button/primarybutton.tsx", "../../src/components/ui/button/index.ts", "../../src/components/ui/card/projectcard.tsx", "../../src/components/ui/card/index.ts", "../../src/components/ui/emptystate/emptystate.tsx", "../../src/components/ui/emptystate/index.ts", "../../src/components/ui/form/fileupload.tsx", "../../src/components/ui/form/formerror.tsx", "../../src/components/ui/form/submitbutton.tsx", "../../src/components/ui/form/textarea.tsx", "../../src/components/ui/form/textinput.tsx", "../../src/components/ui/form/index.ts", "../../src/components/ui/layout/dashboardlayout.tsx", "../../src/components/ui/layout/pagelayout.tsx", "../../src/components/ui/layout/statscard.tsx", "../../src/components/ui/layout/index.ts", "../../src/components/ui/typography/bodytext.tsx", "../../src/components/ui/typography/heading1.tsx", "../../src/components/ui/typography/heading2.tsx", "../../src/components/ui/typography/heading3.tsx", "../../src/components/ui/typography/textmedium.tsx", "../../src/components/ui/typography/textmuted.tsx", "../../src/components/ui/typography/textstrong.tsx", "../../src/components/ui/typography/index.ts", "../../src/hooks/usepwa.ts", "../../src/pages/createproject/createproject.tsx", "../../src/pages/dashboard/dashboard.tsx", "../../src/pages/projectdetail/projectdetail.tsx", "../../src/pages/projectlog/projectlog.tsx", "../../src/pages/signin/signin.tsx", "../../src/pages/signup/signup.tsx", "../../src/styles/clerkappearance.ts", "../../src/styles/clerklocalization.ts", "../../src/utils/lazyloading.ts", "../../src/utils/offlinestorage.ts"], "version": "5.8.3"}